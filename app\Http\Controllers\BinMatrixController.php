<?php

namespace App\Http\Controllers;

use App\Http\Requests\BinMatrixFlowUpdateRequest;
use App\Http\Requests\BinMatrixUpdateRequest;
use App\Http\Requests\BomListExpressionUpdateRequest;
use App\Models\BinMatrix;
use App\Models\BinMatrixFlow;
use App\Models\BinMatrixFlowAttribute;
use App\Models\BinMatrixFlowAttributeValue;
use App\Models\BinMatrixItem;
use App\Models\BomListAttribute;
use App\Models\BomListExpression;
use App\Models\Product;
use App\Models\ProductGroup;
use App\Models\ProductType;
use App\Models\SpeedBatch;
use App\Services\GenerateBinMatrixXmlService;
use App\Services\LineDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;

class BinMatrixController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        //Build Filter
        $filters = $this->filterSessions($request, 'bin_matrix', [
            'keyword' => '',
            'product_type_id' => null,
            'product_id' => null,
            'product_group_id' => null,
        ]);

        $list = BinMatrix::query()->with(['productType', 'productGroup', 'product', 'createdUser'])
            ->accessibleBy(auth()->user())
            ->when(!empty($filters['keyword']), function ($q) use ($filters) {
                $q->orWhere('name', 'like', '%' . $filters['keyword'] . '%');
            })->when(!empty($filters['product_type_id']), function ($q) use ($filters) {
                $q->where('product_type_id', $filters['product_type_id']);
            })->when(!empty($filters['product_id']), function ($q) use ($filters) {
                $q->where('product_id', $filters['product_id']);
            })->when(!empty($filters['product_group_id']), function ($q) use ($filters) {
                $q->where('product_group_id', $filters['product_group_id']);
            })->filterSort($filters)
            ->orderBy('created_at', 'desc')->paginate(config('table.per_page'));

        $products = Product::select('name', 'id')->get();
        $productGroups = ProductGroup::select('name', 'id', 'product_id')->get();



        return Inertia::render('BinMatrix/Index', [
            'header' => BinMatrix::header(),
            'filters' => $filters,
            'list' => $list,
            'products' => $products,
            'productGroups' => $productGroups
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return $this->edit(null);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(BinMatrixUpdateRequest $request)
    {
        return $this->update($request, null);
    }

    /**
     * Display the specified resource.
     */
    public function show(BinMatrix $binMatrix)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(?BinMatrix $binMatrix = null, bool $createRevision = false)
    {
        $productTypes = ProductType::select('name', 'id')->get();
        $products = Product::select('name', 'id', 'product_type_id')->get();
        $productGroups = ProductGroup::select('name', 'id', 'product_id')->get();
        $speedBatches = SpeedBatch::select(['id', 'name', 'state', 'revision', 'product_group_id'])->get()->append('label');
        $sspec_list = [];

        if (null === $binMatrix) {
            $data = new BinMatrix;
        } else {
            //Clone for new revision 
            if ($createRevision) {
                $data = $binMatrix->load(['binMatrixItems']);
                //Increase revision
                $data['revision'] = BinMatrix::getLatestRevision($binMatrix->original_id)?->revision + 1;

                //Reset value as new
                $data['state'] = STATE_WIP;
                $data['created_user_id'] = null;
                $data['state_update_user_id'] = null;
                $data['state_updated_at'] = null;
                $data['is_current'] = false;

                foreach ($data->binMatrixItems as $index => $attribute) {
                    $data->binMatrixItems[$index]['bin_matrix_id'] = null;
                }
            } else {
                $data = $binMatrix->load(['binMatrixItems', 'createdUser']);
            }

            if ($data->speed_batch_id != null) {
                $lineDatabase = new LineDatabase($data->speed_batch_id);
                $sspec_list = $lineDatabase->getSspecList();
            }
        }

        return Inertia::render('BinMatrix/Edit', [
            'data' => Inertia::always($data),
            'productTypes' => $productTypes,
            'products' => $products,
            'productGroups' => $productGroups,
            'speedBatches' => $speedBatches,
            'binTypes' => ['A' => 'A', 'B' => 'B', 'D' => 'D'], //TODO: Create a masterfile
            'createRevision' => $createRevision,
            'sspec_list' => $sspec_list,
            'states' => BinMatrix::states(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(BinMatrixUpdateRequest $request, ?BinMatrix $binMatrix = null)
    {
        $data = $request->validated();
        if (null === $binMatrix) {
            $data['created_user_id'] = auth()->id();

            $binMatrix = BinMatrix::create($data);
            $binMatrix->binMatrixItems()->createMany($data['bin_matrix_items']);
            $this->presetBomListFields($binMatrix->id);

            return Redirect::route('bin_matrix.edit', $binMatrix->id)->with('message', 'Bin Matrix created successfully');
        } else {

            //Create new revision 
            if ($data['create_revision']) {
                $binMatrix = $this->createRevision($binMatrix, $data);
                return Redirect::route('bin_matrix.edit', $binMatrix->id)->with('message', "Bin Matrix Revision {$binMatrix->revision} created successfully");
            } else {

                $store_row_ids = [];
                foreach ($data['bin_matrix_items'] as $item) {
                    $item['bin_matrix_id'] = $binMatrix->id;
                    $result = $binMatrix->binMatrixItems()->updateOrCreate(['id' => $item['id']], $item);

                    //Keep the Row ID to use for delete later
                    $store_row_ids[] = $result->id;
                }

                //Delete Removed Rows
                $binMatrix->binMatrixItems()->whereNotIn('id', $store_row_ids)->delete();
                $binMatrix->update($data);
            }

            return Redirect::route('bin_matrix.edit', $binMatrix->id)->with('message', 'Bin Matrix updated successfully');
        }
    }

    //To clone other related data
    private function createRevision($oldBinMatrix, $data)
    {
        DB::beginTransaction();
        try {
            $binMatrix = $oldBinMatrix->createNewVersion($data);
            $binMatrix->binMatrixItems()->createMany($data['bin_matrix_items']);

            $current_binMaxtrix_id = $binMatrix->id;

            $binMatrix = BinMatrix::with('binMatrixItems')->find($current_binMaxtrix_id);
            $previous_revision_id = $binMatrix->previous_revision_id;


            $binMatrixItemOldNewIds = [];
            $oldBinMatrixItems = BinMatrixItem::where('bin_matrix_id', $previous_revision_id)->get();
            $newBinMatrixItems = BinMatrixItem::where('bin_matrix_id', $current_binMaxtrix_id)->get();

            // Create a mapping where key = old ID and value = new ID
            // Using sequence to match corresponding items between old and new sets
            $oldBinMatrixItems = $oldBinMatrixItems->keyBy('sequence');
            $newBinMatrixItems = $newBinMatrixItems->keyBy('sequence');

            foreach ($oldBinMatrixItems as $sequence => $oldItem) {
                if (isset($newBinMatrixItems[$sequence])) {
                    $binMatrixItemOldNewIds[$oldItem->id] = $newBinMatrixItems[$sequence]->id;
                }
            }

            //Prepare Query
            $binMatrixFlowAttributesOldNewIds = [];
            $binMatrixFlowAttributes = BinMatrixFlowAttribute::where('bin_matrix_id', $previous_revision_id)->get();
            $bomList = BomListAttribute::with('expressions')->where('bin_matrix_id', $previous_revision_id)->get();
            $binMatrixFlows = BinMatrixFlow::with('flowAttributeValues.binMatrixFlowAttribute')->where('bin_matrix_id', $previous_revision_id)->get();


            foreach ($binMatrixFlowAttributes as $bfa) {
                $newBfa = $bfa->replicate();
                $newBfa->bin_matrix_id = $current_binMaxtrix_id;
                $newBfa->save();
                $binMatrixFlowAttributesOldNewIds[$bfa->id] = $newBfa->id;
            }

            foreach ($bomList as $l) {
                $newList = $l->replicate();
                $newList->bin_matrix_id = $current_binMaxtrix_id;
                $newList->save();
                foreach ($l->expressions as $expression) {
                    $newExp = $expression->replicate();
                    $newExp->bom_list_attribute_id = $newList->id;
                    $newExp->bin_matrix_id = $current_binMaxtrix_id;
                    $newExp->bin_matrix_item_id = $binMatrixItemOldNewIds[$expression->bin_matrix_item_id];
                    $newExp->save();
                }
            }

            foreach ($binMatrixFlows as $bf) {
                $newFlow = $bf->replicate();
                $newFlow->bin_matrix_id = $current_binMaxtrix_id;
                $newFlow->bin_matrix_item_id = $binMatrixItemOldNewIds[$bf->bin_matrix_item_id];
                $newFlow->save();

                foreach ($bf->flowAttributeValues as $fv) {
                    $newFv = $fv->replicate();
                    $newFv->bin_matrix_flow_id = $newFlow->id;
                    $newFv->bin_matrix_flow_attribute_id = $binMatrixFlowAttributesOldNewIds[$fv->bin_matrix_flow_attribute_id];
                    $newFv->save();
                }
            }
            DB::commit();

            return $binMatrix;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * User have want preset this as default
     */
    private function presetBomListFields($bin_matrix_id)
    {
        $default_bom_list_fields = config('settings.default_bom_list_fields');
        foreach ($default_bom_list_fields as $field) {
            BomListAttribute::create([
                'name' => $field['name'],
                'length' => $field['length'],
                'sequence' => $field['sequence'],
                'bin_matrix_id' => $bin_matrix_id
            ]);
        }
    }


    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BinMatrix $binMatrix)
    {
        $binMatrix->binMatrixItems()->delete();
        $binMatrix->delete();
        return Redirect::route('bin_matrix.index')->with('message', 'Bin Matrix deleted successfully');
    }

    public function postState(Request $request, BinMatrix $binMatrix)
    {
        //If Bom list empty prevent update
        $binMatrix->load('binMatrixItems');
        if ($binMatrix->binMatrixItems->isEmpty()) {
            return response()->json(['message' => 'Unable to change state, please add Bom Group.'], 400);
        }
        foreach ($binMatrix->binMatrixItems as $item) {
            if (empty($item->bom_list)) {
                return response()->json(['message' => 'Unable to change state, Bom List cannot be empty.'], 400);
            }
        }

        $binMatrix->update(['state' => $request->state, 'state_update_user_id' => auth()->id(), 'state_updated_at' => now()]);
        return response()->json(['message' => 'State updated successfully']);
    }

    public function getRevisionCreate(BinMatrix $binMatrix)
    {
        return $this->edit($binMatrix, true);
    }

    /*
    * BOM List Attribute/Field Editor
    */
    public function getBomListAttributes(int $bin_matrix_id)
    {
        $data = BomListAttribute::where('bin_matrix_id', $bin_matrix_id)->orderBy('sequence')->get();
        return response()->json(['data' => $data]);
    }

    public function postBomListAttributes(Request $request, int $bin_matrix_id)
    {
        $store_row_ids = [];
        foreach ($request->bom_list_attributes as $bom_list_attribute) {
            $bom_list_attribute['bin_matrix_id'] = $bin_matrix_id;

            $success_id = $bom_list_attribute['id'];
            if ($bom_list_attribute['id'] == null) {
                $result = BomListAttribute::create($bom_list_attribute);
                $success_id = $result->id;
            } else {
                $result = BomListAttribute::where('id', $bom_list_attribute['id'])->update($bom_list_attribute);
            }
            //Keep the Row ID to use for delete later
            $store_row_ids[] = $success_id;
        }
        //Delete Removed Rows
        BomListAttribute::whereNotIn('id', $store_row_ids)->where('bin_matrix_id', $bin_matrix_id)->delete();

        return Redirect::route('bin_matrix.edit', $bin_matrix_id)->with('message', 'Bom List Fields updated successfully');
    }

    /*
    * BOM List Expression Editor
    */
    public function getBomListExpressions(int $bin_matrix_id)
    {
        $headers = BomListAttribute::where('bin_matrix_id', $bin_matrix_id)->orderBy('sequence')->get();
        $attributeData = $headers->map(fn($attr) => [
            'id' => $attr->id,
            'length' => $attr->length
        ]);

        $binMatrixItems = BinMatrixItem::where('bin_matrix_id', $bin_matrix_id)->orderBy('sequence')->get();
        $groups = multiselect_options($binMatrixItems, 'id', 'bom_group');

        $expressions = BomListExpression::where('bin_matrix_id', $bin_matrix_id)
            ->get()
            ->groupBy('uuid');

        $data = $expressions->map(function ($group) use ($attributeData) {
            return [
                'bin_matrix_item_id' => $group->first()->bin_matrix_item_id,
                'entries' => $attributeData->map(function ($attr) use ($group) {
                    $expression = $group->firstWhere('bom_list_attribute_id', $attr['id']);
                    return [
                        'uuid' => $group->first()->uuid,
                        'id' => $expression->id ?? null,
                        'value' => $expression->value ?? '',
                        'bom_list_attribute_id' => $attr['id'],
                        'length' => $attr['length']
                    ];
                })
            ];
        })->values();

        return response()->json([
            'headers' => $headers,
            'groups' => $groups,
            'data' => $data
        ]);
    }

    public function postBomListExpressions(BomListExpressionUpdateRequest $request, int $bin_matrix_id)
    {
        $store_row_ids = [];
        foreach ($request->list as $expression) {
            foreach ($expression['entries'] as $entries) {
                $entries['bin_matrix_id'] = $bin_matrix_id;
                $entries['bin_matrix_item_id'] = $expression['bin_matrix_item_id'];

                $success_id = $entries['id'];
                if ($entries['id'] == null) {
                    $result = BomListExpression::create($entries);
                    $success_id = $result->id;
                } else {
                    BomListExpression::where('id', $entries['id'])->first()->update($entries);
                }
                //Keep the Row ID to use for delete later
                $store_row_ids[] = $success_id;
            }
        }
        //Delete Removed Rows
        BomListExpression::whereNotIn('id', $store_row_ids)->where('bin_matrix_id', $bin_matrix_id)->delete();

        $this->generateBomList($bin_matrix_id);

        return response()->json(['message' => 'Bom List Expressions updated successfully']);
    }

    private function generateBomList($bin_matrix_id)
    {
        //Each comma is a separator, it will combine all values into one string
        //Eg. ["A", "B", "C"] => "ABC", ["A", "B,C", "3"] => "AB3,AC3", ["A", "B,C", "3,4"] => "AB3,AB4,AC3,AC4"

        // Group by both bin_matrix_item_id AND uuid
        $itemGroups = BomListExpression::where('bom_list_expressions.bin_matrix_id', $bin_matrix_id)
            ->with('bomlistAttribute')
            ->join('bom_list_attributes', 'bom_list_expressions.bom_list_attribute_id', '=', 'bom_list_attributes.id')
            ->orderBy('bom_list_attributes.sequence')
            ->select('bom_list_expressions.*')
            ->get()
            ->groupBy(['bin_matrix_item_id', 'uuid']);

        $resultsByItem = [];
        foreach ($itemGroups as $itemId => $uuidGroups) {
            foreach ($uuidGroups as $uuid => $group) {
                $concat_list = $group->pluck('value')->toArray();
                $partsArrays = array_map(fn($val) => explode(',', $val), $concat_list);
                $combinations = $this->cartesianProduct($partsArrays);

                // Store combinations per item ID
                $resultsByItem[$itemId] = array_merge(
                    $resultsByItem[$itemId] ?? [],
                    array_map(fn($c) => implode('', $c), $combinations)
                );
            }
        }

        // Update BinMatrixItems with deduplicated combinations
        foreach ($resultsByItem as $itemId => $combinations) {
            $uniqueCombos = array_unique($combinations);
            BinMatrixItem::find($itemId)->update([
                'bom_list' => implode(', ', $uniqueCombos)
            ]);
        }
    }

    private function cartesianProduct(array $arrays)
    {
        $result = [[]];
        foreach ($arrays as $key => $values) {
            $temp = [];
            foreach ($result as $item) {
                foreach ($values as $value) {
                    $temp[] = array_merge($item, [trim($value)]);
                }
            }
            $result = $temp;
        }
        return $result;
    }

    /*
    * Bin Matrix Flow
    */
    public function getFlows(int $bin_matrix_item_id)
    {
        $item = BinMatrixItem::with('binMatrix')->find($bin_matrix_item_id);
        $attributes = BinMatrixFlowAttribute::where('bin_matrix_id', $item->bin_matrix_id)->orderBy('sequence')->get();
        $data = BinMatrixFlow::with('flowAttributeValues')->where('bin_matrix_item_id', $bin_matrix_item_id)->orderBy('ord')->get();
        $bom_list_attributes = BomListAttribute::where('bin_matrix_id', $item->bin_matrix_id)->orderBy('sequence')->get();
        $bom_list_expressions = BomListExpression::where('bin_matrix_item_id', $bin_matrix_item_id)
            ->orderBy('bom_list_attribute_id')
            ->pluck('value', 'bom_list_attribute_id');
        $bom_list_map = [];
        foreach ($bom_list_attributes as $attribute) {
            $bom_list_map[$attribute->name] = $bom_list_expressions[$attribute->id] ?? null;
        }

        $lineDatabase = new LineDatabase($item->binMatrix->speed_batch_id);
        $lines = $lineDatabase->getModel()->get()->keyBy('QDF/SSPEC');

        return response()->json(['data' => $data, 'attributes' => $attributes, 'lines' => $lines, 'bom_list_map' => $bom_list_map]);
    }

    public function postFlows(BinMatrixFlowUpdateRequest $request, int $bin_matrix_item_id)
    {
        //For Validation only, some field is dynamic, use $request to get other fields
        $request->validated();

        $item = BinMatrixItem::find($bin_matrix_item_id);

        $store_row_ids = [];
        foreach ($request->rows as $flow) {
            $store_fav_ids = [];

            $flow['bin_matrix_id'] = $item->bin_matrix_id;
            $flow['bin_matrix_item_id'] = $bin_matrix_item_id;

            // Remove extra spaces between items (keep only single space after comma)
            // Remove the single space after the comma
            $new_cross_ship = preg_replace('/\s*,\s*/', ',', trim($flow['cross_ship']));
            // Remove all special characters from the end
            // This pattern matches any non-letter and non-number characters at the end
            $flow['cross_ship'] = preg_replace('/[^a-zA-Z0-9]+$/', '', $new_cross_ship);

            $result = BinMatrixFlow::updateOrCreate(['bin_matrix_item_id' => $flow['bin_matrix_item_id'], 'id' => $flow['id']], $flow);
            //Save flow_attribute_values
            foreach ($flow['flow_attribute_values'] as $flow_attribute_value) {
                $flow_attribute_value['bin_matrix_flow_id'] = $result->id;
                $fav = BinMatrixFlowAttributeValue::updateOrCreate(['bin_matrix_flow_id' => $flow_attribute_value['bin_matrix_flow_id'], 'id' => $flow_attribute_value['id']], $flow_attribute_value);
                $store_fav_ids[] = $fav->id;
            }

            //Delete Removed Flow Attribute Values
            BinMatrixFlowAttributeValue::whereNotIn('id', $store_fav_ids)->where('bin_matrix_flow_id', $result->id)->delete();

            $store_row_ids[] =  $result->id;
        }
        //Delete Removed Rows
        BinMatrixFlow::whereNotIn('id', $store_row_ids)->where('bin_matrix_item_id', $bin_matrix_item_id)->delete();

        return Redirect::back()->with(['message' => 'Flows updated successfully']);
    }

    public function postFlowAttributes(Request $request, int $bin_matrix_id)
    {
        $store_row_ids = [];
        foreach ($request->rows as $row) {
            $row['attribute_name'] = $row['name']; //Make it same, posible name different want to keep original attribute name
            $result = BinMatrixFlowAttribute::updateOrCreate(['bin_matrix_id' => $bin_matrix_id, 'id' => $row['id']], $row);
            $store_row_ids[] =  $result->id;
        }
        //Delete Removed Rows
        BinMatrixFlowAttribute::whereNotIn('id', $store_row_ids)->where('bin_matrix_id', $bin_matrix_id)->delete();
        return Redirect::back()->with(['message' => 'Attributes updated successfully']);
    }

    public function getData(BinMatrix $binMatrix)
    {
        $data = $binMatrix->load(['binMatrixItems', 'createdUser']);
        return response()->json(["data" => $data]);
    }

    public function exportBinMatrixXML(BinMatrix $binMatrix)
    {
        try {
            // Initialize services with proper parameters
            $exportService = new GenerateBinMatrixXmlService($binMatrix->id);
            $filePath = $exportService->saveXMLToFile();

            return response()->download($filePath, basename($filePath), [
                'Content-Type' => 'application/xml',
                'Content-Disposition' => 'attachment; filename="' . basename($filePath) . '"',
            ])->deleteFileAfterSend(true);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}
