<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * This is a temporary table when trying to import, for show differences and do comparison
     */
    public function up(): void
    {
        Schema::create('speed_preview', function (Blueprint $table) {
            $table->id();
            $table->unsignedSmallInteger('EXTERNAL REVISION')->nullable();
            $table->string('Spec Code')->nullable();
            $table->string('Spec Sequential Number')->nullable();
            $table->string('External Step')->nullable();
            $table->string('Processor Base Code')->nullable();
            $table->string('FINISHED GOOD TYPE')->nullable();
            $table->string('INTERNAL REV STEP')->nullable();
            $table->string('IC Category Type')->nullable();
            $table->string('Item Market Name')->nullable();
            $table->integer('Pincount')->nullable();
            $table->string('Market Code Name')->nullable();
            $table->string('PREVIOUS REFERENCE ID')->nullable();
            $table->string('Product Grade')->nullable();
            $table->string('POWER GRADE')->nullable();
            $table->string('TRANSCEIVER TILE CONFIG')->nullable();
            $table->integer('TRANSCEIVER COUNT')->nullable();
            $table->integer('TRANSCEIVER SPEED RATIO')->nullable();
            $table->float('CORE SPEED RATIO')->nullable();
            $table->string('PRODUCT VARIANT')->nullable();
            $table->integer('Density')->nullable();
            $table->string('Density UOM')->nullable();
            $table->float('Voltage I/O')->nullable();
            $table->string('PROGRAMMED IND')->nullable();
            $table->string('MM TECHNOLOGY')->nullable();
            $table->string('PP Steering Committee')->nullable();
            $table->string('Pb Free')->nullable();
            $table->string('Custom Indicator')->nullable();
            $table->string('Customer Custom Product')->nullable();
            $table->string('Package Platform')->nullable();
            $table->string('Package Text')->nullable();
            $table->string('Shipment Media')->nullable();
            $table->string('Trademark/Family Name')->nullable();
            $table->integer('Fab Process')->nullable();
            $table->string('INTERNAL STEPPING')->nullable();
            $table->string('Speed Type')->nullable();
            $table->integer('External Product ID')->nullable();
            $table->string('Speed')->nullable();
            $table->string('Die Code Name')->nullable();
            $table->string('CUST PART NO')->nullable();
            $table->string('Royalty Technology')->nullable();
            $table->text('Comments')->nullable();
            $table->text('ENGINEERING EFFORTS')->nullable();
            $table->string('SUB COMPONENT CATEGORY NAME')->nullable();
            $table->integer('RLDRAM III MHz')->nullable();
            $table->float('LVDS Gbps')->nullable();
            $table->integer('MLAB Simple Dual Port MHz')->nullable();
            $table->integer('MLAB True Dual Port MHz')->nullable();
            $table->integer('LUTRAM MHz')->nullable();
            $table->integer('DSP Fixed Point Mode MHz')->nullable();
            $table->integer('DSP Floating Point Mode MHz')->nullable();
            $table->integer('MAIB MHz')->nullable();
            $table->integer('eSRAM MHz')->nullable();
            $table->integer('HPS MHz Rate')->nullable();
            $table->integer('DDR4 FREQ')->nullable();
            $table->string('External Stepping')->nullable();
            //$table->string('Internal Stepping')->nullable(); //Excel header put do not use
            $table->string('L4/Forecast Name')->nullable();
            $table->string('MM#')->nullable();
            $table->string('Package Type')->nullable();
            $table->string('QDF/SSPEC')->nullable();
            $table->string('Sample/Production Type')->nullable();
            $table->foreignId('compare_batch_id')->nullable()->index(); //When multiple people do import, this is to distinguish which batch they are importing
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('speed_preview');
    }
};
