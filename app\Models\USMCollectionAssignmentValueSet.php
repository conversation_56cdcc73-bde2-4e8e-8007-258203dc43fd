<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class USMCollectionAssignmentValueSet extends Model
{

    protected $table = 'usm_collection_assignment_value_sets';

    protected $fillable = [
        'scenario_ids',
        'value_set_id',
        'usm_collection_assignment_id',
    ];

    protected $casts = [
        'scenario_ids' => 'array'
    ];

    /**
     * Get the assignment that owns the value set.
     */
    public function assignment()
    {
        return $this->belongsTo(USMCollectionAssignment::class, 'usm_collection_assignment_id');
    }

    public function valueSet()
    {
        return $this->belongsTo(USMCollectionValueSet::class, 'value_set_id');
    }
}
