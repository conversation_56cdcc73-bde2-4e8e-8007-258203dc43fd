<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\SoftDeletes;

class BinMatrixFlow extends BaseModel
{
    use SoftDeletes;

    protected $fillable = [
        'in_bm',
        'in_avid_terra',
        'in_ffr',
        'ord',
        'flow',
        'olb_bin',
        'pass_bin',
        'reference_qdf',
        'package_type',
        'production_type',
        'mm', //hidden from UI
        'cross_ship',
        'bin_matrix_id',
        'bin_matrix_item_id',
    ];

    public function flowAttributeValues()
    {
        return $this->hasMany(BinMatrixFlowAttributeValue::class);
    }

    public function crossShipExplode(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => isset($attributes['cross_ship']) && !empty($attributes['cross_ship']) ? explode(',', str_replace(' ', '', $attributes['cross_ship'])) : []
        );
    }
}
