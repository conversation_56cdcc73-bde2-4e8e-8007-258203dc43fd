<?php

namespace App\Services;

use App\Models\Component;
use App\Models\USMCollectionMap;
use App\Models\USMScenario;

class CollectionMappingService
{
    public $usm_id;
    public $usm_module_id;
    public $scenario_mode;
    public $map;

    public function __construct(int $usm_id, int $usm_module_id, string $scenario_mode)
    {
        $this->usm_id = $usm_id;
        $this->usm_module_id = $usm_module_id;
        $this->scenario_mode = $scenario_mode;

        $scenario_ids = USMScenario::where('usm_id', $this->usm_id)->where('in_xml', true)->pluck('name', 'id')->toArray();

        $this->map = USMCollectionMap::where('usm_id', $this->usm_id)
            ->where('usm_module_id', $this->usm_module_id)
            ->where('scenario_mode', $this->scenario_mode)
            ->where('linked', true)
            ->with(['conditions.assignments.valueSets.valueSet', 'conditions.assignments.collection.values.valueSet', 'conditions.assignments.collection.componentType'])
            ->first();

        if ($this->map != null) {
            foreach ($this->map->conditions as $condition) {
                foreach ($condition->assignments as $assignment) {
                    $components = Component::whereIn('id', $assignment->component_ids)->select(['id', 'name', 'level'])->get();
                    $assignment->components = $components;

                    //Build Collection Map contain Scenario ValueSet
                    foreach ($assignment->valueSets as $valueSet) {
                        if ($valueSet->scenario_ids != null && !empty($valueSet->scenario_ids)) {
                            $collection_valuesets = [];
                            foreach ($valueSet->scenario_ids as $scenario_id) {
                                if (isset($scenario_ids[$scenario_id])) {
                                    $collection_valuesets[$scenario_ids[$scenario_id]] = $valueSet->valueSet->name;
                                }
                            }
                            $valueSet->collection_valuesets = $collection_valuesets;
                        }
                    }
                }
            }
        }
    }

    /**
     * To populate in the Values Tab showing that is is already mapped to a collection
     */
    public function getMappedCollectionForValues()
    {
        if ($this->map == null) return [];

        $mapped = [];
        foreach ($this->map->conditions as $condition) {
            foreach ($condition->assignments as $assignment) {
                //For Scenario Mapping
                $value_set_scenario_ids = [];
                $value_set_ids_for_non_scenario = [];
                foreach ($assignment->valueSets as $valueSet) {
                    $value_set_scenario_ids[$valueSet->value_set_id] = $valueSet->scenario_ids;

                    if ($valueSet->scenario_ids == null) {
                        $value_set_ids_for_non_scenario[] = $valueSet->value_set_id;
                    }
                }

                foreach ($assignment->collection->values as $value) {
                    foreach ($assignment->component_ids as $component_id) {
                        $arrayValue = $value->toArray();
                        if ($this->scenario_mode === XML_NONSCENARIO) {
                            //Only used selected value_set
                            if (in_array($value->usm_collection_value_set_id, $value_set_ids_for_non_scenario)) {
                                $mapped[$component_id][$value->usm_module_component_attribute_id][] = $arrayValue;
                            }
                        } else {
                            if (isset($value_set_scenario_ids[$value->usm_collection_value_set_id])) {
                                $arrayValue['scenario_ids'] = $value_set_scenario_ids[$value->usm_collection_value_set_id];
                            }
                            $mapped[$component_id][$value->usm_module_component_attribute_id][] = $arrayValue;
                        }
                    }
                }
            }
        }

        return $mapped;
    }

    public function getCollectionMap()
    {
        if ($this->map == null) return null;
        return $this->map;
    }

    public function getCollections()
    {
        if ($this->map == null) return null;
        $collections = [];
        foreach ($this->map->conditions as $condition) {
            foreach ($condition->assignments as $assignment) {
                $row = [
                    'name' => $assignment->collection->name,
                    'scenario_mode' => $assignment->collection->scenario_mode,
                    'source' => $assignment->collection->source,
                    'level' => $assignment->collection->level,
                    'component_type' => $assignment->collection->componentType->name,
                ];

                foreach ($assignment->collection->moduleComponentAttributes as $attribute) {
                    $row['attributes'][$attribute->id] = ['name' => $attribute->name, 'parameter' => $attribute->parameter, 'values' => []];
                }

                foreach ($assignment->collection->values as $value) {
                    if (isset($row['attributes'][$value->usm_module_component_attribute_id])) {
                        $row['attributes'][$value->usm_module_component_attribute_id]['values'][] = ['valueSet' => $value->valueSet->name, 'value' => $value->input_value];
                    }
                }

                $collections[] = $row;
            }
        }
        return $collections;
    }
}
