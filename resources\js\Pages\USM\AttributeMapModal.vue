<script setup>
import FlashAlert from '@/Components/FlashAlert.vue';
import Modal from '@/Components/Modal.vue';
import Select from '@/Components/Select.vue';
import TextInput from '@/Components/TextInput.vue';
import { useForm } from '@inertiajs/vue3';
import axios from 'axios';
import { onMounted, ref, computed } from 'vue';

const attributeMapModal = ref(null);
const modules = ref([]);

const props = defineProps({
    usm_id: {
        type: Number,
        required: true,
    },
    editDisabled: {
        type: Boolean,
        default: false,
    },
});

const form = useForm({
    rows: [],
});

onMounted(() => {
    const modalElement = document.getElementById('attributeMapModal');
    modalElement.addEventListener('show.bs.modal', () => {
        // Fetch attribute map data
        axios.get(route('usm.attribute_map', props.usm_id)).then(response => {
            form.rows = response.data.data;
        });

        // Fetch modules for this USM
        axios.get(route('usm.modules', props.usm_id)).then(response => {
            modules.value = response.data.modules;
        });
    });
});

const save = () => {
    form.post(route('usm.module_mappings.update', props.usm_id), {
        preserveScroll: true,
        onSuccess: () => closeModal(),
    });
};

const closeModal = () => {
    form.clearErrors();
    resetInput();
    attributeMapModal.value.close();
};

const resetInput = () => {
    form.reset();
};

//Computed
const buttonYes = computed(() => {
    return props.editDisabled ? null : 'Save';
});
</script>

<template>
    <Modal ref="attributeMapModal" @yesEvent="save" @noEvent="closeModal" :id="'attributeMapModal'" :title="'Template Attribute Map'" :buttonYes="buttonYes" :buttonType="'primary'" :modalClass="'modal-xl'" :form="form">
        <FlashAlert v-if="Object.keys(form.errors).length && !form.errors.file" :status="'danger'" @close="form.clearErrors()">
            <label v-for="(message, field) in form.errors" :key="field">
                {{ message }}
            </label>
        </FlashAlert>

        <div class="mt-6">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>NS/S</th>
                        <th>Level</th>
                        <th>Component Type</th>
                        <th>Attribute</th>
                        <th>Module</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(row, index) in form.rows" :key="index">
                        <td class="text-center">
                            {{ row.scenario_mode === 'NonScenarioAttributes' ? 'NS' : 'S' }}
                        </td>
                        <td>
                            {{ row.level }}
                        </td>
                        <td>
                            {{ row.component_type }}
                        </td>
                        <td>
                            {{ row.attribute }}
                        </td>
                        <td>
                            <Select v-model="row.module_id" :options="modules" :disabled="props.editDisabled" placeholder="Select module" />
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </Modal>
</template>
