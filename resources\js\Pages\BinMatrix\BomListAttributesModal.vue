<script setup>
import FlashAlert from '@/Components/FlashAlert.vue';
import Modal from '@/Components/Modal.vue';
import TextInput from '@/Components/TextInput.vue';
import { useForm } from '@inertiajs/vue3';
import axios from 'axios';
import { onMounted, ref, computed } from 'vue';

const bomListAttributesModal = ref(null);

const props = defineProps({
    id: {
        type: Number,
        required: true,
    },
    editDisabled: {
        type: Boolean,
        default: false,
    },
    viewOnly: {
        type: Boolean,
        default: true,
    },
});

const form = useForm({
    bom_list_attributes: [],
});

onMounted(() => {
    const modalElement = document.getElementById('bomListAttributesModal');
    modalElement.addEventListener('show.bs.modal', () => {
        axios.get(route('bin_matrix.bom_list_attribute', props.id)).then(response => {
            form.bom_list_attributes = response.data.data;
        })
    });
})

const save = () => {
    form.post(route('bin_matrix.bom_list_attribute', props.id), {
        preserveScroll: true,
        onSuccess: () => closeModal(),
        onFinish: () => resetInput(),
    });
};

const closeModal = () => {
    form.clearErrors();
    resetInput();
    bomListAttributesModal.value.close();
};

const resetInput = () => {
    form.reset();
};

//function on this page
const addRow = () => {
    form.bom_list_attributes.push({
        id: null,
        name: '',
        length: '',
        sequence: form.bom_list_attributes.length + 1,
    });
};

const removeRow = index => {
    form.bom_list_attributes.splice(index, 1);
    //Reupdate the sequence
    form.bom_list_attributes.forEach((row, index) => {
        row.sequence = index + 1;
    });
};

//Computed
const buttonYes = computed(() => {
    if (props.viewOnly) return null;
    return props.editDisabled ? null : 'Save';
})
</script>

<template>
    <Modal ref="bomListAttributesModal" @yesEvent="save" @noEvent="closeModal" :id="'bomListAttributesModal'"
        :title="'Bom List Field Editor'" :buttonYes="buttonYes" :buttonType="'primary'" :form="form">
        <FlashAlert v-if="Object.keys(form.errors).length && !form.errors.file" :status="'danger'"
            @close="form.clearErrors()">
            <label v-for="(message, field) in form.errors">
                {{ message }}
            </label>
        </FlashAlert>

        <div class="mt-6">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th v-if="!viewOnly" width="3%">
                            <button type="button" class="btn btn-sm btn-primary" @click="addRow"><i
                                    class="bi bi-plus"></i></button>
                        </th>
                        <th width="3%">No</th>
                        <th>Name</th>
                        <th width="20%">Length</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(row, index) in form.bom_list_attributes" :key="index">
                        <td v-if="!viewOnly" class="text-center">
                            <button type="button" class="btn btn-sm btn-danger" @click="removeRow(index)"><i
                                    class="bi bi-dash"></i></button>
                        </td>
                        <td class="text-center">
                            <input type="hidden" v-model="form.bom_list_attributes[index].sequence" />
                            {{ index + 1 }}
                        </td>
                        <td>
                            <TextInput type="text" v-model="form.bom_list_attributes[index].name"
                                :readonly="viewOnly" />
                        </td>
                        <td>
                            <TextInput type="number" v-model="form.bom_list_attributes[index].length"
                                :readonly="viewOnly" />
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </Modal>
</template>
