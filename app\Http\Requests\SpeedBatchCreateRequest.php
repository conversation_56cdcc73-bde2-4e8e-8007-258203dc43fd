<?php

namespace App\Http\Requests;

use App\Models\SpeedBatch;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SpeedBatchCreateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [];
        return  array_merge($rules, [
            //'name' => ['required','string', 'max:255', Rule::unique(SpeedBatch::class)->ignore($this->speed_batch)->where('revision', $this->input('revision'))],
            'name' => ['required','string', 'max:255'],
            'product_type_id' => ['numeric', 'max:255'],
            'product_id' => ['numeric', 'max:255'],
            'product_group_id' => ['numeric', 'max:255'],
            'file' => ['required', 'file'],
        ]);
    }
}
