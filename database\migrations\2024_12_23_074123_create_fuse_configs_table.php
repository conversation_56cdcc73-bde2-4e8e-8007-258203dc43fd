<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fuse_configs', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->boolean('active')->default(true);
            $table->boolean('import')->default(false);
            $table->string('import_file')->nullable();
            $table->unsignedBigInteger('created_user_id');
            $table->unsignedBigInteger('fuse_manager_id')->nullable();

            /*
            * Unsure keep in comment first
            $table->string('state')->nullable();
            $table->unsignedBigInteger('product_type_id')->nullable(); //Product Type
            $table->unsignedBigInteger('product_id')->nullable(); //Product Code Name
            $table->unsignedBigInteger('product_group_id')->nullable(); //Product Group
            $table->unsignedBigInteger('bin_matrix_id')->nullable();
            $table->unsignedBigInteger('state_update_user_id')->nullable();
            $table->timestamp('state_updated_at')->nullable();
            //Versioning
            $table->integer('revision')->default(0);
            $table->boolean('is_current')->default(false);
            $table->unsignedBigInteger('previous_revision_id')->nullable(); //Link this table last version
            $table->unsignedBigInteger('original_id')->nullable(); //All other revision need to link back the orignal copy id
            */

            $table->timestamps();
            $table->softDeletes();

            //Unsure keep in comment first
            //$table->index(['product_type_id', 'product_id', 'product_group_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fuse_configs');
    }
};
