<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ComponentUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [];
        return  array_merge($rules, [
            'name'=>['nullable'],
            'component_type_id'=>['nullable'],
            'notes'=>['nullable'],
            'level' => ['required'],
            'component_level_id' => ['required'],
            'parent_id' => ['nullable'],
            'usm_id' => ['required'],
        ]);
    }
}
