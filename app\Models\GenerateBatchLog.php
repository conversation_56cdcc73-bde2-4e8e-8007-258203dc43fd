<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class GenerateBatchLog extends BaseModel
{
    public $fillable = ['user_id', 'speed_batch_id', 'failed', 'exception', 'messages', 'started_at', 'completed_at'];

    public function speedBatch(): BelongsTo
    {
        return $this->belongsTo(SpeedBatch::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function scopeIsGenerating($query, $speedBatchId)
    {
        return $query->where('speed_batch_id', $speedBatchId)->whereNotNull('started_at')->whereNull('completed_at')->where('failed', false);
    }
}
