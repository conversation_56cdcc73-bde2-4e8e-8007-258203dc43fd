<?php

namespace App\Traits;

use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;


trait ModuleModelTrait
{
    /**
     * Used in Vue Excel Editor
     */
    public function buildExcelColumns()
    {
        $excludedColumns = config('settings.exclude_columns');
        $columnDetails = [];
        $columns = Schema::getColumnListing($this->getTable());
        foreach ($columns as $column) {
            if (in_array($column, $excludedColumns))
                continue;

            $type = DB::getSchemaBuilder()->getColumnType($this->getTable(), $column);
            $columnDetails[] = [
                'field' => encodeColumn($column), //From Utils.php
                'label' => $column,
                'type' => $this->mapColumnType($type),
            ];
        }

        return $columnDetails;
    }

    /**
     * Get usable columns that is display in the UI and known to the user
     * Use in non excel mode. The rest is hidden.
     */
    public function getUsableColumns()
    {
        $excludedColumns = config('settings.exclude_columns');
        $columns = Schema::getColumnListing($this->getTable());
        return array_values(array_diff($columns, $excludedColumns));
    }


    /**
     * Get usable columns with ID set to null for attribute selector
     */
    public function getUsableColumnsWithId()
    {
        $columns = $this->getTableColumns();
        return array_map(function ($column) {
            return [
                'id' => null,
                'attribute' => $column
            ];
        }, $columns);
    }


    /**
     * Since the header field is encoded, we need to encode the entire attributes,
     * so that in Vue tableData match the field name
     */
    public function encodeAllAttributes()
    {
        $encodedAttributes = collect($this->getAttributes())
            ->mapWithKeys(function ($value, $key) {
                return [encodeColumn($key) => $value];
            })
            ->all();

        $this->setRawAttributes($encodedAttributes);

        return $this;
    }

    /**
     * Map database column type to Vue Excel Editor type
     */
    private function mapColumnType($dbType)
    {
        $typeMap = [
            'string' => 'string',
            'text' => 'string',
            'integer' => 'number',
            'bigint' => 'number',
            'smallint' => 'number',
            'decimal' => 'number',
            'float' => 'number',
            'boolean' => 'check10',
            'date' => 'date',
            'datetime' => 'date',
            'timestamp' => 'date',
        ];

        return $typeMap[$dbType] ?? 'string';
    }
}
