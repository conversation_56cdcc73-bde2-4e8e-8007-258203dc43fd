<template>
    <div class="col-md-2" v-if="productTypes">
        <div class="form-floating">
            <select v-model="localValue.product_type_id" class="form-select" id="productTypeSelect">
                <option :value="null">All</option>
                <option v-for="type in productTypes" :key="type.id" :value="type.id">{{ type.name }}</option>
            </select>
            <label for="productTypeSelect">Product Types</label>
        </div>
    </div>
    <div class="col-md-2"  v-if="!productTypes">
        <div class="form-floating">
            <select v-model="localValue.product_id" class="form-select" id="productSelect" >
                <option :value="null">All</option>
                <option v-for="product in products" :key="product.id" :value="product.id">{{ product.name }}</option>
            </select>
            <label for="productSelect">Product</label>
        </div>
    </div>
    <div class="col-md-2" v-else>
        <div class="form-floating">
            <select v-model="localValue.product_id" class="form-select" id="productSelect" :disabled="!localValue.product_type_id">
                <option :value="null">All</option>
                <option v-for="product in filteredProducts" :key="product.id" :value="product.id">{{ product.name }}</option>
            </select>
            <label for="productSelect">Product</label>
        </div>
    </div>
    <div class="col-md-2">
        <div class="form-floating">
            <select v-model="localValue.product_group_id" class="form-select" id="productGroupSelect" :disabled="!localValue.product_id">
                <option :value="null">All</option>
                <option v-for="group in filteredProductGroups" :key="group.id" :value="group.id">{{ group.name }}</option>
            </select>
            <label for="productGroupSelect">Product Groups</label>
        </div>
    </div>
</template>

<script setup>
import { computed, watch} from 'vue';

const props = defineProps({
    modelValue: {
        type: Object,
        required: true,
    },
    productTypes: {
        type: Object,
    },
    products: {
        type: [Array, Object], 
        required: true,
    },
    productGroups: {
        type: [Array, Object],
        required: true,
    },
});

const emit = defineEmits(['update:modelValue']);

const localValue = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

watch(() => localValue.value.product_type_id, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    localValue.value.product_id = null;
    localValue.value.product_group_id = null;
  }
})

watch(() => localValue.value.product_id, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    localValue.value.product_group_id = null;
  }
})

const filteredProductGroups = computed(() => {
  const pid = localValue.value.product_id;
  return props.productGroups.filter(g => Number(g.product_id) === Number(pid));
})

const filteredProducts = computed(() => {
  const ptid = localValue.value.product_type_id;
  return props.products.filter(p => Number(p.product_type_id) === Number(ptid));
})
</script>