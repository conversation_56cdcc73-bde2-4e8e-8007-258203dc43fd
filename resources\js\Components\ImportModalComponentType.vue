<script setup>
import FlashAlert from '@/Components/FlashAlert.vue';
import InputError from '@/Components/InputError.vue';
import Modal from '@/Components/Modal.vue';
import { useForm } from '@inertiajs/vue3';
import { ref } from 'vue';
import Select from './Select.vue';

const props = defineProps({
    url: {
        type: String,
    },
    title: {
        type: String,
        default: 'Excel File',
    },
    sourceTypes: {
        type: Object,
        default: () => ({}),
    },
    files: {
        type: Object,
        default: () => ({}),
    },
    fileTypes: {
        type: String,
        default: '',
    },
});

// To access importModal function
const importModal = ref(null);
const inputFile = ref(null);

const form = useForm({
    file: null,
    source: null,
});

const upload = () => {
    form.post(props.url, {
        preserveScroll: true,
        onSuccess: () => closeModal(),
        onFinish: () => resetInput(),
    });
};

const closeModal = () => {
    importModal.value.close();
    form.clearErrors();
    resetInput();
};

const resetInput = () => {
    form.reset();
    inputFile.value.value = null; //Form Reset not working for type=file
};
</script>

<template>
    <Modal ref="importModal" @yesEvent="upload" @noEvent="closeModal" :id="'importModal'" :title="title" :buttonYes="'Upload'" :buttonType="'primary'" :form="form">
        <FlashAlert v-if="Object.keys(form.errors).length && !form.errors.file" :status="'danger'" @close="form.clearErrors()">
            <label v-for="(message, field) in form.errors" :key="field">
                {{ message }}
            </label>
        </FlashAlert>

        <div>
            <div class="row g-3">
                <div class="col-md-12">
                    <label for="file_input">File</label>
                    <input ref="inputFile" type="file" id="file_input" @input="form.file = $event.target.files[0]" :class="{ 'is-invalid': form.errors.file }" class="form-control" />
                    <InputError :message="form.errors.file" class="mt-2" />
                </div>
                <!-- May need to add source selection in the future -->
                <!-- <div class="col-md-12">
                    <label for="source">Source</label>
                    <Select v-model="form.source" :options="sourceTypes" required :invalid="form.errors.source" />
                    <InputError :message="form.errors.source" class="mt-2" />
                </div> -->
            </div>
        </div>

        <div class="mt-2">
            <label>Uploaded files</label>
            <ul class="list-group">
                <li v-for="(file, index) in files[fileTypes]" :key="index" class="list-group-item">
                    <a :href="file" target="_blank">File {{ index + 1 }}</a>
                </li>
            </ul>
        </div>
    </Modal>
</template>
