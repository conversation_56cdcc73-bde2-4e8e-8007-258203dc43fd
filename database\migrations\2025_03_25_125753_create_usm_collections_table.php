<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('usm_collections', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('scenario_mode'); //NonScenarioAttributes or ScenarioAttributes;
            $table->string('source')->nullable(); //No purpose for now
            $table->string('level');
            $table->smallInteger('sequence')->default(0);
            $table->unsignedBigInteger('component_level_id');
            $table->unsignedBigInteger('component_type_id');
            $table->unsignedBigInteger('usm_module_id')->nullable();
            // $table->unsignedBigInteger('usm_scenario_id')->nullable(); //Null = NonScenarioAttributes 
            $table->unsignedBigInteger('usm_id');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('usm_collections');
    }
};
