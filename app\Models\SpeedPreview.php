<?php

namespace App\Models;

use App\Traits\ModuleModelTrait;
use Illuminate\Support\Facades\Schema;

/**
 * A temporary table for preview before import to Speed
 */
class SpeedPreview extends BaseModel
{
    use ModuleModelTrait;

    protected $table = 'speed_preview';

    public function getTableColumns()
    {
        $excludedColumns = ['id', 'created_at', 'updated_at', 'deleted_at', 'batch_id'];
        $columns = Schema::getColumnListing($this->getTable());
        return array_values(array_diff($columns, $excludedColumns));
    }
}
