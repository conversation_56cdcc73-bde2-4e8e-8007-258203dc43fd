<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class USMScenario extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'usm_scenarios';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'in_xml',
        'name',
        'sequence',
        'expression',
        'usm_id'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'in_xml' => 'boolean',
    ];

    /**
     * Get the USM that owns the scenario.
     */
    public function usm()
    {
        return $this->belongsTo(USM::class);
    }

    public function components()
    {
        return $this->belongsToMany(Component::class, 'component_usm_module_usm_scenario', 'usm_scenario_id', 'component_id')
            ->withPivot('usm_module_id');
    }
}
