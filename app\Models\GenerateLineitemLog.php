<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class GenerateLineitemLog extends BaseModel
{
    public $fillable = ['user_id', 'lineitem_manager_id', 'failed', 'exception', 'messages', 'started_at', 'completed_at'];

    public function lineitemManager(): BelongsTo
    {
        return $this->belongsTo(LineitemManager::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function scopeIsGenerating($query, $lineitemManagerId)
    {
        return $query->where('lineitem_manager_id', $lineitemManagerId)->whereNotNull('started_at')->whereNull('completed_at')->where('failed', false);
    }
}
