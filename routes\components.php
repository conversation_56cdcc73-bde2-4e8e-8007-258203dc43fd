<?php

use App\Http\Controllers\ComponentController;
use Illuminate\Support\Facades\Route;

// Component routes
Route::prefix('components')->name('components.')->group(function () {
    Route::get('/{usm_id}', [ComponentController::class, 'getComponents'])->name('get');
    Route::post('/', [ComponentController::class, 'store'])->name('store');
    Route::put('/{id}', [ComponentController::class, 'update'])->name('update');
    Route::delete('/{id}', [ComponentController::class, 'destroy'])->name('destroy');

    // Special query routes
    // Route::get('/level/{level}', [ComponentController::class, 'getByLevel']);
    // Route::get('/component-level/{componentLevelId}', [ComponentController::class, 'getByComponentLevel']);
    // Route::get('/{id}/children', [ComponentController::class, 'getChildren']);
    // Route::get('/{id}/descendants', [ComponentController::class, 'getDescendants']);
    // Route::get('/{id}/parent', [ComponentController::class, 'getParent']);
    // Route::get('/{id}/ancestors', [ComponentController::class, 'getAncestors']);
    // Route::get('/{id}/siblings', [ComponentController::class, 'getSiblings']);

    //Update component level
    Route::get('/levels/{usm_id}', [ComponentController::class, 'getComponentLevels'])->name('levels');
    Route::post('/levels/{usm_id}', [ComponentController::class, 'postComponentLevels'])->name('levels');
});