<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lineitem_managers', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->string('module'); //Master Data (Speed,Treadmill,Fuse,TP Flow Matrix)
            // $table->unsignedBigInteger('product_type_id'); //Product Type
            // $table->unsignedBigInteger('product_id'); //Product Code Name
            // $table->unsignedBigInteger('product_group_id'); //Product Group
            $table->unsignedBigInteger('speed_batch_id')->nullable();
            $table->boolean('approved')->default(false);
            $table->unsignedBigInteger('approved_user_id')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->unsignedBigInteger('created_user_id');
            $table->boolean('active')->default(true);
            //Versioning
            // $table->integer('revision')->default(0);
            // $table->boolean('is_current')->default(false);
            // $table->unsignedBigInteger('previous_revision_id')->nullable(); //Link this table last version
            // $table->unsignedBigInteger('original_id')->nullable(); //All other revision need to link back the orignal copy id
            $table->timestamp('generated_at')->nullable();
            $table->timestamps();
            $table->softDeletes();

            //$table->index(['product_type_id', 'product_id', 'product_group_id']);
            $table->index(['speed_batch_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lineitem_managers');
    }
};
