<?php

namespace App\Console\Commands;

use App\Ldap\LdapUser;
use Illuminate\Console\Command;
use LdapRecord\Container;

class TestLdap extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-ldap';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test LDAP';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->comment('Start Test LDAP');
        $user = LdapUser::where('uid', '=', 'boyle')->first();
        if ($user) {
            $this->comment("DN: " . $user->getDN());
            $this->comment("CN: " . $user->getFirstAttribute('cn'));
            $this->comment("Mail: " . $user->getFirstAttribute('mail'));
            $this->comment("UID: " . $user->getFirstAttribute('uid'));
        }

        $connection = Container::getConnection('default');
        $user = LdapUser::findByOrFail('uid', 'boyle');
        if ($connection->auth()->attempt($user->getDn(), 'password')) {
            // Credentials are valid!
            $this->comment('Success');
        } else {
            $this->comment('Failed');
        }
    }
}
