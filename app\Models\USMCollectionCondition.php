<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class USMCollectionCondition extends Model
{
    use SoftDeletes;

    protected $table = 'usm_collection_conditions';

    protected $fillable = [
        'usm_collection_map_id',
        'usm_id',
        'expression',
        'comment',
        'sequence'
    ];

    /**
     * Get the collection map that owns the condition.
     */
    public function collectionMap()
    {
        return $this->belongsTo(USMCollectionMap::class, 'usm_collection_map_id');
    }

    /**
     * Get the assignments for the condition.
     */
    public function assignments()
    {
        return $this->hasMany(USMCollectionAssignment::class, 'usm_collection_condition_id')
            ->orderBy('sequence', 'asc');
    }
}
