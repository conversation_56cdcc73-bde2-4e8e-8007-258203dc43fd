/* Global styles for CrossShipSuggestions dropdown */
.crossship-suggestions-dropdown {
    position: fixed !important;
    z-index: 9999 !important;
    max-height: 200px;
    overflow-y: auto;
    background: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Override Bootstrap modal styles to allow scrolling while keeping dropdowns visible */
.modal {
    overflow-x: hidden !important;
    overflow-y: auto !important;
}

.modal-open {
    padding-right: 0 !important;
}

/* Ensure the modal dialog and content allow vertical scrolling */
.modal-dialog {
    max-height: 90vh;
    overflow: visible !important;
}

.modal-content {
    overflow: visible !important;
}

.modal-body {
    overflow-x: visible !important;
    overflow-y: auto !important;
    max-height: calc(90vh - 120px); /* Adjust for header and footer */
}
