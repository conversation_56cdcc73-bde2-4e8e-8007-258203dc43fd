<?php

namespace App\Models;

//MYSQL use this
use App\Models\Model;
//MongoDB use this
// use App\Traits\MongoAppendsTrait;
// use Moloquent;


class BaseModel extends Model #Model(MySQL) or Moloquent(MONGO) 
{
    public function scopeFilterSort($query, $filters)
    {
        return $query->when(!empty($filters['sort']['field']), function ($q) use ($filters) {
            $q->orderBy($filters['sort']['field'], $filters['sort']['direction']);
        });
    }

    //Index filter for different products
    public function scopeByProducts($query, $filters)
    {
        return $query->when(!empty($filters['product_type_id']), function ($q) use ($filters) {
            $q->where('product_type_id', $filters['product_type_id']);
        })->when(!empty($filters['product_id']), function ($q) use ($filters) {
            $q->where('product_id', $filters['product_id']);
        })->when(!empty($filters['product_group_id']), function ($q) use ($filters) {
            $q->where('product_group_id', $filters['product_group_id']);
        });
    }

    public function scopeWhereProducts($query, $product_type_id, $product_id, $product_group_id)
    {
        return $query->where('product_type_id', $product_type_id)
            ->where('product_id', $product_id)
            ->where('product_group_id', $product_group_id);
    }

    /**
     * Bottom is Mongodb related functions, hide this if using MySQL
     */
    // use MongoAppendsTrait;

    // public function __construct(array $attributes = [])
    // {
    //     parent::__construct($attributes);

    //     // Initialize the trait
    //     $this->initializeHasGlobalAppends();
    // }
}
