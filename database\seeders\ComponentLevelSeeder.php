<?php

namespace Database\Seeders;

use App\Models\ComponentLevel;
use Illuminate\Database\Seeder;

class ComponentLevelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample component levels
        $levels = [
            ['level' => 1, 'name' => 'Category','usm_id'=>1],
            ['level' => 2, 'name' => 'Subcategory','usm_id'=>1],
            ['level' => 3, 'name' => 'Component','usm_id'=>1],
            ['level' => 4, 'name' => 'Subcomponent','usm_id'=>1],
        ];

        foreach ($levels as $level) {
            ComponentLevel::create($level);
        }
    }
}
