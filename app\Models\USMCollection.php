<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class USMCollection extends BaseModel
{
    use SoftDeletes;

    protected $table = 'usm_collections';

    protected $fillable = [
        'name',
        'scenario_mode',
        'source',
        'level',
        'sequence',
        'component_level_id',
        'component_type_id',
        'usm_module_id',
        'usm_scenario_id',
        'usm_id',
    ];

    /**
     * The component type this collection belongs to
     */
    public function componentType()
    {
        return $this->belongsTo(ComponentType::class, 'component_type_id');
    }

    /**
     * The component level this collection belongs to
     */
    public function componentLevel()
    {
        return $this->belongsTo(ComponentLevel::class, 'component_level_id');
    }

    /**
     * The module this collection belongs to
     */
    public function module()
    {
        return $this->belongsTo(USMModule::class, 'usm_module_id');
    }

    /**
     * The scenario this collection belongs to
     */
    public function scenario()
    {
        return $this->belongsTo(USMScenario::class, 'usm_scenario_id');
    }

    /**
     * The USM this collection belongs to
     */
    public function usm()
    {
        return $this->belongsTo(USM::class, 'usm_id');
    }

    /**
     * The value sets for this collection
     */
    public function valueSets()
    {
        return $this->hasMany(USMCollectionValueSet::class, 'usm_collection_id');
    }

    /**
     * The values for this collection (through value sets)
     */
    public function values()
    {
        return $this->hasManyThrough(
            USMCollectionValue::class,
            USMCollectionValueSet::class,
            'usm_collection_id',
            'usm_collection_value_set_id'
        );
    }

    /**
     * The module component attributes associated with this collection
     */
    public function moduleComponentAttributes()
    {
        return $this->belongsToMany(
            USMModuleComponentAttribute::class,
            'usm_module_component_attribute_usm_collection',
            'usm_collection_id',
            'usm_module_component_attribute_id'
        );
    }
}
