<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class USMCollectionMap extends Model
{
    use SoftDeletes;

    protected $table = 'usm_collection_maps';

    protected $fillable = [
        'name',
        'scenario_mode',
        'linked',
        // 'usm_scenario_id',
        'usm_module_id',
        'usm_id'
    ];

    protected $casts = [
        'linked' => 'boolean',
    ];

    /**
     * Get the USM that owns the collection map.
     */
    public function usm()
    {
        return $this->belongsTo(USM::class);
    }

    /**
     * Get the module that owns the collection map.
     */
    public function module()
    {
        return $this->belongsTo(USMModule::class, 'usm_module_id');
    }

    /**
     * Get the scenario that owns the collection map.
     */
    // public function scenario()
    // {
    //     return $this->belongsTo(USMScenario::class, 'usm_scenario_id');
    // }

    /**
     * Get the conditions for the collection map.
     */
    public function conditions()
    {
        return $this->hasMany(USMCollectionCondition::class, 'usm_collection_map_id')
            ->orderBy('sequence', 'asc');
    }
}
