<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class USMModuleComponentAttribute extends BaseModel
{
    use SoftDeletes;

    protected $table = 'usm_module_component_attributes';

    public $fillable = [
        'attribute',
        'level',
        'component_level_id',
        'scenario_mode', //NonScenarioAttributes or ScenarioAttributes
        'is_complex',
        'name',
        'parameter',
        'datatype',
        'units',
        'description',
        'complex_description',
        'component_type_id',
        'usm_module_id',
        'usm_id',
    ];

    public function usm()
    {
        return $this->belongsTo(USM::class, 'usm_id');
    }

    public function module()
    {
        return $this->belongsTo(USMModule::class, 'usm_module_id');
    }

    public function componentType()
    {
        return $this->belongsTo(ComponentType::class, 'component_type_id');
    }

    public function values()
    {
        return $this->hasMany(USMModuleComponentAttributeValue::class, 'usm_module_component_attribute_id');
    }

    /**
     * The collections associated with this module component attribute
     */
    public function collections()
    {
        return $this->belongsToMany(
            USMCollection::class,
            'usm_module_component_attribute_usm_collection',
            'usm_module_component_attribute_id',
            'usm_collection_id'
        );
    }
}
