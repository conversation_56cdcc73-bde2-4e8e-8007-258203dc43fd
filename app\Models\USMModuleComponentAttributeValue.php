<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class USMModuleComponentAttributeValue extends BaseModel
{
    use SoftDeletes;

    protected $table = 'usm_module_component_attribute_values';

    public $fillable = [
        'input_value',
        'value',
        'component_id',
        'usm_module_component_attribute_id',
        'usm_scenario_id',
        'usm_id',
        'collection_id' //When use collection will not use value
    ];

    public function usm()
    {
        return $this->belongsTo(USM::class, 'usm_id');
    }

    public function module()
    {
        return $this->belongsTo(USMModule::class, 'usm_module_id');
    }

    public function componentType()
    {
        return $this->belongsTo(ComponentType::class, 'component_type_id');
    }

    public function moduleComponentAttribute()
    {
        return $this->belongsTo(USMModuleComponentAttribute::class, 'usm_module_component_attribute_id');
    }

    public function scenario()
    {
        return $this->belongsTo(USMScenario::class, 'usm_scenario_id');
    }
}
