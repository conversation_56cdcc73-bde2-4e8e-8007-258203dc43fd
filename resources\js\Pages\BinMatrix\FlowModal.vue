<script setup>
import FlashAlert from '@/Components/FlashAlert.vue';
import Modal from '@/Components/Modal.vue';
import TextInput from '@/Components/TextInput.vue';
import Checkbox from '@/Components/Checkbox.vue';
import CrossShipSuggestions from '@/Components/CrossShipSuggestions.vue';
import { useForm } from '@inertiajs/vue3';
import axios from 'axios';
import { onMounted, ref, watch, computed } from 'vue';
import Multiselect from 'vue-multiselect';

const flowModal = ref(null);
const crossShipRefs = ref([]);
const attributes = ref([]);
const lines = ref(null);
const bomListMap = ref([]);

const props = defineProps({
    bin_matrix_item_id: {
        type: Number,
    },
    item: {
        type: Object,
    },
    sspec_list: {
        type: Array,
    },
    editDisabled: {
        type: Boolean,
    },
});

const form = useForm({
    rows: [],
});

// Watch for changes in bin_matrix_item_id and fetch data
watch(
    () => props.bin_matrix_item_id,
    newId => {
        if (newId) {
            fetchFlowData(newId);
        }
    },
    { immediate: true },
);

const fetchFlowData = id => {
    if (!id) {
        return;
    }

    axios.get(route('bin_matrix.flows', id)).then(response => {
        attributes.value = response.data.attributes;
        lines.value = response.data.lines;
        bomListMap.value = response.data.bom_list_map;

        // Process each flow to ensure it has all attributes
        form.rows = response.data.data.map(flow => {
            // Create a map of existing values by attribute id
            const existingValues = {};
            flow.flow_attribute_values.forEach(value => {
                existingValues[value.bin_matrix_flow_attribute_id] = value;
            });

            // Ensure each flow has all attributes
            const completeAttributeValues = attributes.value.map((attr, index) => {
                // Use existing value if available, otherwise create new one
                return (
                    existingValues[attr.id] || {
                        id: null,
                        value: '',
                        bin_matrix_flow_attribute_id: attr.id,
                        bin_matrix_flow_id: flow.id,
                        _index: index,
                    }
                );
            });

            return {
                ...flow,
                flow_attribute_values: completeAttributeValues,
            };
        });
    });
};

onMounted(() => {
    const modalElement = document.getElementById('flowModal');
    modalElement.addEventListener('show.bs.modal', () => { });
});

const save = () => {
    // Check for duplicates in any CrossShipSuggestions
    const hasInvalidCrossShip = form.rows.some((_, index) => {
        return crossShipRefs.value[index]?.isInvalid;
    });

    if (hasInvalidCrossShip) {
        alert('Please remove duplicate entries in Cross Ship field before saving');
        return;
    }

    form.post(route('bin_matrix.flows', props.bin_matrix_item_id), {
        preserveScroll: true,
        onSuccess: () => closeModal(),
    });
};

const closeModal = () => {
    flowModal.value.close();
};

const onSelectedReferenceQdf = (selectedOption, row) => {
    if (selectedOption) {
        const line = lines.value[selectedOption];

        if (line) {
            //Fix Column
            //row.package_type = line['Package Type']; //Currently let user key in self
            //row.production_type = line['Sample/Production Type']; //Currently let user key in self
            row.mm = line['MM#'];

            row.flow_attribute_values.forEach((value, index) => {
                const attribute = attributes.value.find(attr => attr.id === value.bin_matrix_flow_attribute_id);
                value.value = line[attribute.attribute_name];
            });
        }
    }
}

//function on this page
const addRow = () => {
    //Create empty attributes to flow_attribute_values
    const flow_attribute_values = attributes.value.map((attr, index) => ({
        id: null,
        value: '',
        bin_matrix_flow_attribute_id: attr.id,
        bin_matrix_flow_id: null,
        _index: index, // Add index for easy lookup
    }));

    // Calculate the appropriate bin numbers
    const newFlowIndex = form.rows.length + 1;
    //Request to be editable 6April2025-PhooiShan
    const olbBin = ""; //newFlowIndex; // Start from 1
    const passBin = ""; //1000 + newFlowIndex; // Start from 1001
    const packageValue = bomListMap.value['Package'];
    const sampleValue = bomListMap.value['Sample-4|Production-8'];

    form.rows.push({
        id: null,
        in_bm: true,
        in_avid_terra: true,
        in_ffr: true,
        ord: form.rows.length * 10,
        flow: newFlowIndex,
        olb_bin: olbBin,
        pass_bin: passBin,
        reference_qdf: '',
        package_type: packageValue,
        production_type: sampleValue,
        mm: '',
        cross_ship: '',
        flow_attribute_values: flow_attribute_values,
    });
};

const removeRow = index => {
    form.rows.splice(index, 1);
    //Reupdate the sequence
    form.rows.forEach((row, index) => {
        row.ord = index * 10;
        row.flow = index + 1;
        // row.olb_bin = index + 1;
        // row.pass_bin = 1000 + (index + 1);
    });
};

const modalTitle = computed(() => {
    return props.item != null ? props.item.bom_group + ' Flow Editor' : 'Flow Editor';
});

const buttonYes = computed(() => {
    return props.editDisabled ? null : 'Save';
});

// Copy cross_ship from the first row to all other rows
const copyCrossShip = () => {
    if (form.rows.length === 0) return;
    const valueToCopy = form.rows[0].cross_ship;
    form.rows.forEach((row, idx) => {
        if (idx !== 0) {
            row.cross_ship = valueToCopy;
        }
    });
};
</script>

<template>
    <Modal ref="flowModal" @yesEvent="save" @noEvent="closeModal" :id="'flowModal'" :title="modalTitle"
        :buttonYes="buttonYes" :buttonType="'primary'" :form="form" :modalClass="'modal-xl'" :style="'max-width: 90%;'">
        <FlashAlert v-if="Object.keys(form.errors).length && !form.errors.file" :status="'danger'"
            @close="form.clearErrors()">
            <ul>
                <li v-for="(message, field) in form.errors" :key="field">{{ message }}</li>
            </ul>
        </FlashAlert>

        <div v-if="item != null">
            <p>
                <b>Bin Type:</b> {{ item.bin_type }} <br />
                <b>Bom List:</b><br />
                {{ item.bom_list }}
            </p>
        </div>

        <div class="flow-table-container">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr style="font-size: 10px">
                            <th width="3%" class="sticky-column">
                                <button type="button" class="btn btn-sm btn-primary" @click="addRow"><i
                                        class="bi bi-plus"></i></button>
                            </th>
                            <th width="3%">In BM</th>
                            <th width="3%">In AVID / Terra</th>
                            <th width="3%">In FFR</th>
                            <th>Ord</th>
                            <th>Flow</th>
                            <th>OLB Bin</th>
                            <th>Pass Bin</th>
                            <th>Reference QDF</th>
                            <th>Package Type</th>
                            <th>Sample / Production Type</th>
                            <th width="25%">
                                <span>CrossShip</span>
                                <button 
                                    type="button" :title="'Copy CrossShip of first row to other rows'" 
                                    class="btn btn-sm btn-primary mx-2 tiny-button"
                                    @click="copyCrossShip(row)">
                                    <i class="bi bi-clipboard-plus-fill"></i>
                                </button>
                            </th>
                            <th v-for="attribute in attributes" :key="attribute.id">{{ attribute.name }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(row, index) in form.rows" :key="index">
                            <td class="text-center sticky-column">
                                <button type="button" class="btn btn-sm btn-danger" @click="removeRow(index)"><i
                                        class="bi bi-dash"></i></button>
                            </td>
                            <td>
                                <Checkbox id="bmCheck" v-model:checked="row.in_bm" />
                            </td>
                            <td>
                                <Checkbox id="avidCheck" v-model:checked="row.in_avid_terra" />
                            </td>
                            <td>
                                <Checkbox id="ffrCheck" v-model:checked="row.in_ffr" />
                            </td>
                            <td>
                                <TextInput v-model="row.ord" class="form-control-sm" size="5" readonly />
                            </td>
                            <td>
                                <TextInput v-model="row.flow" class="form-control-sm" size="3" readonly />
                            </td>
                            <td>
                                <TextInput v-model="row.olb_bin" class="form-control-sm"
                                    :invalid="form.errors[`rows.${index}.olb_bin`]" />
                                <div class="invalid-feedback">{{ form.errors[`rows.${index}.olb_bin`] }}</div>
                            </td>
                            <td>
                                <TextInput v-model="row.pass_bin" class="form-control-sm"
                                    :invalid="form.errors[`rows.${index}.pass_bin`]" />
                                <div class="invalid-feedback">{{ form.errors[`rows.${index}.pass_bin`] }}</div>
                            </td>
                            <td>
                                <!-- <TextInput v-model="row.reference_qdf" class="form-control-sm" /> -->
                                <Multiselect v-model="row.reference_qdf" :options="sspec_list" :searchable="true"
                                    :close-on-select="true" :show-labels="false" :placeholder="'Search'"
                                    @select="onSelectedReferenceQdf($event, row)">
                                </Multiselect>

                                <!-- Hide from UI, use in Generate XML-->
                                <input type="hidden" v-model="row.mm" />
                            </td>
                            <td>
                                <TextInput v-model="row.package_type" class="form-control-sm" />
                            </td>
                            <td>
                                <TextInput v-model="row.production_type" class="form-control-sm" />
                            </td>
                            <td class="position-relative overflow-visible crossship-cell">
                                <CrossShipSuggestions v-model="row.cross_ship" :suggestions="sspec_list"
                                    :ref="el => (crossShipRefs[index] = el)"
                                    :invalid="form.errors[`rows.${index}.cross_ship`]" />
                            </td>
                            <!--Dynamic attributes flow_attribute_values field-->
                            <td v-for="(attribute, attrIndex) in attributes" :key="attribute.id">
                                <TextInput v-model="row.flow_attribute_values[attrIndex].value"
                                    class="form-control-sm" />
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </Modal>
</template>

<style scoped>
/* Using scoped style to avoid affecting other components */
/* Container for the table with fixed height and width */
.flow-table-container {
    width: 100%;
    position: relative;
    max-height: 60vh;
}

/* Enable horizontal scrolling for the table */
.table-responsive {
    overflow-x: auto !important;
    /* Critical: This allows dropdowns to appear outside */
    overflow-y: auto !important;
    max-width: 100%;
    max-height: 60vh;
}

/* Make the header sticky */
thead {
    position: sticky;
    top: 0;
    background-color: white;
    z-index: 2;
}

/* Ensure table cells can handle overflow content */
.table td.position-relative.overflow-visible {
    overflow: visible !important;
    position: relative !important;
}

/* Special styling for the CrossShip column to ensure dropdowns appear outside */
.crossship-cell {
    position: relative !important;
    overflow: visible !important;
}

/* Make the first column sticky */
.sticky-column {
    position: sticky;
    left: 0;
    background-color: #fff;
    z-index: 1;
    border-right: 1px solid #dee2e6;
}

/* Set a minimum width for each column to ensure they're not too narrow */
.table th,
.table td {
    min-width: 100px;
    white-space: nowrap;
}

/* Except for the first column which can be narrower */
.table th.sticky-column,
.table td.sticky-column {
    min-width: 50px;
}

/* Ensure the CrossShip column has enough width */
.table th:nth-child(12),
.table td:nth-child(12) {
    min-width: 200px;
}

/* Set the size of CrossShip copy button */ 
.tiny-button {
  width: 16px;
  height: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.tiny-button i {
  font-size: 9px;
}
</style>
