<script setup>
import DynamicTable from '@/Components/DynamicTable.vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, Link } from '@inertiajs/vue3';
import { ref } from 'vue';
import { setFlashMessage, setFlashError } from '@/helper';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    isSpeedOnly: {
        type: Boolean,
        default: true
    }
});

const routeGroupName = 'speedbatch';
const headerTitle = ref('Speed');
const localData = ref({ ...props.data });
const isReadOnly = ref(true);

const postApprove = () => {
    var x = confirm('Are you sure you want to approve this Speed?');
    if (x) {
        axios
            .post(route(routeGroupName + '.approve', props.data.id))
            .then(response => {
                localData.value = response.data.data;
                setFlashMessage(response.data.message);
            })
            .catch(error => {
                setFlashError(error.response.data.message);
            });
    }
};
</script>

<template>

    <Head :title="headerTitle" />

    <AuthenticatedLayout>
        <template #header>
            {{ headerTitle }}
        </template>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <ul class="nav nav-tabs card-header-tabs">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="tab" href="#tab_1">Table</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#tab_2">Lineitem Table</a>
                    </li>
                </ul>
                <div class="text-end">
                    <div class="dropdown">
                        <button class="btn btn-sm btn-info dropdown-toggle" type="button" data-bs-toggle="dropdown"
                            aria-expanded="false">
                            <i class="bi bi-three-dots-vertical"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu">
                            <li>
                                <button type="button" class="dropdown-item" @click="postApprove"
                                    :disabled="localData.approved">
                                    <span v-if="localData.approved_user"> {{ localData.approved_user.name }} approved at
                                        {{
                                            localData.approved_at }} </span>
                                    <span v-else>Approve</span>
                                </button>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="tab-content">
                    <div class="tab-pane fade pt-10 show active" id="tab_1" role="tabpanel" aria-labelledby="tab_1">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <InputLabel for="name" value="Name" />
                                <TextInput id="name" type="text" v-model="data.name" :readonly="isReadOnly" disabled />
                            </div>
                            <div class="col-md-3">
                                <InputLabel for="product_type" value="Product Type" />
                                <TextInput id="product_type" type="text" v-model="data.product_type.name"
                                    :readonly="isReadOnly" disabled />
                            </div>
                            <div class="col-md-3">
                                <InputLabel for="product" value="Product Name" />
                                <TextInput id="product" type="text" v-model="data.product.name" :readonly="isReadOnly"
                                    disabled />
                            </div>
                            <div class="col-md-3">
                                <InputLabel for="product_group" value="Product Group" />
                                <TextInput id="product_group" type="text" v-model="data.product_group.name"
                                    :readonly="isReadOnly" disabled />
                            </div>

                            <div class="col-12">
                                <div class="text-end">
                                    Revision: {{ data.revision }} <br />
                                    <template v-if="data.created_user">Created By: {{ data.created_user.name }}
                                    </template>
                                </div>
                            </div>
                        </div>
                        <div class="row g-3">
                            <DynamicTable :batchId="data.id" tableType="speed" :editable="false" />
                        </div>
                    </div>
                    <div class="tab-pane fade pt-10" id="tab_2" role="tabpanel" aria-labelledby="tab_2">
                        <label v-if="isSpeedOnly">No lineitem found, please generate first.
                            <Link :href="route(routeGroupName + '.edit', data.id)">
                            Go to Edit
                            </Link>
                        </label>
                        <div v-else class="row g-3">
                            <DynamicTable :batchId="data.id" :editable="false" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="col-12">
                    <Link class="btn btn-secondary me-2" :href="route(routeGroupName + '.index')">Back</Link>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>
