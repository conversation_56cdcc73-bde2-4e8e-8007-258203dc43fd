<?php

use App\Http\Controllers\BinMatrixController;
use App\Http\Controllers\ComponentTypeController;
use App\Http\Controllers\DynamicTableController;
use App\Http\Controllers\FuseConfigContoller;
use App\Http\Controllers\FuseManagerContoller;
use App\Http\Controllers\LineItemManagerController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ProductGroupController;
use App\Http\Controllers\ProductTypeController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\SpeedBatchController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\USMCollectionMapController;
use App\Http\Controllers\USMController;
use App\Models\FuseConfig;
use Illuminate\Console\View\Components\Line;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return redirect('/login');
    // return Inertia::render('Welcome', [
    //     'canLogin' => Route::has('login'),
    //     'canRegister' => Route::has('register'),
    //     'laravelVersion' => Application::VERSION,
    //     'phpVersion' => PHP_VERSION,
    // ]);
});

Route::get('/dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth', 'admin')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    Route::prefix('users')->name('users.')->group(function () {
        //Do Something later
        Route::patch('menu/{id}', [UserController::class, 'patchMenu'])->name('menu.update');
        Route::patch('product_permission/{id}', [UserController::class, 'patchProductPermission'])->name('product_permission.update');
    });
    Route::resource('users', UserController::class);
    Route::resource('product_types', ProductTypeController::class);

    Route::prefix('component_types')->name('component_types.')->group(function () {
        Route::post('import/complex_type', [ComponentTypeController::class, 'postImportComplexTypes'])->name('import.complex_type');
        Route::post('import/complex_type_detail', [ComponentTypeController::class, 'postImportComplexTypeDetails'])->name('import.details');
    });
    Route::resource('component_types', ComponentTypeController::class);
    Route::resource('products', ProductController::class);
    Route::resource('product_groups', ProductGroupController::class);

    Route::prefix('speedbatch')->name('speedbatch.')->group(function () {
        Route::post('different', [SpeedBatchController::class, 'different'])->name('different');
        Route::post('import', [SpeedBatchController::class, 'import'])->name('import');
        Route::post('approve/{speed_batch}', [SpeedBatchController::class, 'postApprove'])->name('approve');
        //Update State
        Route::post('state/{speed_batch}', [SpeedBatchController::class, 'postState'])->name('state.update');
        Route::get('format/{speed_batch}', [SpeedBatchController::class, 'getFormat'])->name('format.get');
        Route::get('logs/{speed_batch}', [SpeedBatchController::class, 'getLogs'])->name('logs');
    });

    Route::resource('speedbatch', SpeedBatchController::class);

    Route::prefix('lineitem_managers')->name('lineitem_managers.')->group(function () {
        Route::get('revision/create/{lineitem_manager}', [LineItemManagerController::class, 'getRevisionCreate'])->name('revision.create');
        Route::post('approve/{lineitem_manager}', [LineItemManagerController::class, 'postApprove'])->name('approve');
        Route::post('generate/{lineitem_manager}', [LineItemManagerController::class, 'postGenerate'])->name('generate');
        Route::post('compare', [LineItemManagerController::class, 'getComparision'])->name('compare');
        Route::get('logs/{lineitem_manager}', [LineItemManagerController::class, 'getLogs'])->name('logs');
    });
    Route::resource('lineitem_managers', LineItemManagerController::class);

    Route::prefix('fuse_configs')->name('fuse_configs.')->group(function () {
        Route::get('revision/create/{fuse_config}', [FuseConfigContoller::class, 'getRevisionCreate'])->name('revision.create');
        Route::get('import', [FuseConfigContoller::class, 'getImport'])->name('import');
        Route::post('import', [FuseConfigContoller::class, 'postImport'])->name('import');
        Route::post('state/{fuse_config}', [FuseConfigContoller::class, 'postState'])->name('state.update');
        Route::get('lineitems/{fuse_config_id}', [FuseConfigContoller::class, 'getFuseConfigLineitems'])->name('lineitems');
        Route::post('lineitems/{fuse_config_id}', [FuseConfigContoller::class, 'postFuseConfigLineitems'])->name('lineitems');
        Route::get('fuse_def/{fuse_config_id}', [FuseConfigContoller::class, 'getFuseDefinition'])->name('fuse_def');
        Route::get('fuse_sspec/{fuse_config_id}', [FuseConfigContoller::class, 'getSSPEC'])->name('fuse_sspec');
    });
    Route::resource('fuse_configs', FuseConfigContoller::class);


    Route::prefix('fuse_managers')->name('fuse_managers.')->group(function () {
        Route::get('revision/create/{fuse_config}', [FuseConfigContoller::class, 'getRevisionCreate'])->name('revision.create');
        Route::post('state/{fuse_config}', [FuseConfigContoller::class, 'postState'])->name('state.update');
        Route::get('fuse_def/{fuse_config_id}', [FuseConfigContoller::class, 'getFuseDefinition'])->name('fuse_def');
        Route::get('fuse_sspec/{fuse_config_id}', [FuseConfigContoller::class, 'getSSPEC'])->name('fuse_sspec');
    });
    Route::resource('fuse_managers', FuseManagerContoller::class);

    Route::prefix('dynamic')->name('dynamic.')->group(function () {
        Route::get('/', [DynamicTableController::class, 'index'])->name('index');
        Route::post('/', [DynamicTableController::class, 'update'])->name('update');
        Route::delete('/', [DynamicTableController::class, 'destroy'])->name('destroy');
        Route::post('/delete', [DynamicTableController::class, 'delete'])->name('delete');
        Route::post('/store', [DynamicTableController::class, 'store'])->name('store');
    });

    Route::prefix('bin_matrix')->name('bin_matrix.')->group(function () {
        Route::get('revision/create/{bin_matrix}', [BinMatrixController::class, 'getRevisionCreate'])->name('revision.create');
        //Bom List Attribute
        Route::get('bom_list_attributes/{bin_matrix_id}', [BinMatrixController::class, 'getBomListAttributes'])->name('bom_list_attribute');
        Route::post('bom_list_attributes/{bin_matrix_id}', [BinMatrixController::class, 'postBomListAttributes'])->name('bom_list_attribute.update');
        //Bom List Expression
        Route::get('bom_list_expressions/{bin_matrix_id}', [BinMatrixController::class, 'getBomListExpressions'])->name('bom_list_expression');
        Route::post('bom_list_expressions/{bin_matrix_id}', [BinMatrixController::class, 'postBomListExpressions'])->name('bom_list_expression.update');
        //Flows
        Route::get('flows/{bin_matrix_item_id}', [BinMatrixController::class, 'getFlows'])->name('flows');
        Route::post('flows/{bin_matrix_item_id}', [BinMatrixController::class, 'postFlows'])->name('flows.update');
        //Flow Attributes, add each line of attribute
        Route::post('flow_attributes/{bin_matrix_id}', [BinMatrixController::class, 'postFlowAttributes'])->name('flow_attributes.update');
        //Update State
        Route::post('state/{bin_matrix}', [BinMatrixController::class, 'postState'])->name('state.update');
        Route::get('data/{bin_matrix}', [BinMatrixController::class, 'getData'])->name('data');
        Route::get('binmatrix_export_xml/{bin_matrix}', [BinMatrixController::class, 'exportBinMatrixXML'])->name('export_binmatrix_xml');
    });
    Route::resource('bin_matrix', BinMatrixController::class);

    Route::prefix('usm')->name('usm.')->group(function () {
        Route::get('revision/create/{usm}', [USMController::class, 'getRevisionCreate'])->name('revision.create');
        Route::post('state/{usm}', [USMController::class, 'postState'])->name('state.update');
        Route::get('attribute_map/{usm}', [USMController::class, 'getAttributeMap'])->name('attribute_map');
        Route::get('modules/{usm}', [USMController::class, 'getModules'])->name('modules');
        Route::post('module_mappings/{usm}', [USMController::class, 'postModuleMappings'])->name('module_mappings.update');
        Route::post('module_component_type_values', [USMController::class, 'postModuleComponentTypeValues'])->name('module_component_type_values.update');
        Route::get('module_component_data', [USMController::class, 'getModuleComponentData'])->name('module_component_data');
        Route::get('collection_editor', [USMController::class, 'getCollectionEditor'])->name('collection_editor');
        Route::post('collection_editor', [USMController::class, 'postCollectionEditor'])->name('collection_editor');
        Route::post('collection_values', [USMController::class, 'postCollectionValues'])->name('collection_values.update');
        Route::get('usm_export_xml/{usm}', [USMController::class, 'exportUSMXML'])->name('export_usm_xml');
        Route::get('ups_export_xml/{usm}', [USMController::class, 'exportUPSXML'])->name('export_ups_xml');
        Route::get('structure_export_xml/{usm}', [USMController::class, 'exportStructureXML'])->name('export_structure_xml');
        Route::get('lineitemdata_export_xml/{usm}', [USMController::class, 'exportLineItemDataXML'])->name('export_lineitemdata_xml');
    });
    Route::resource('usm', USMController::class);

    Route::prefix('usm_collection_map')->name('usm_collection_map.')->group(function () {
        Route::get('edit', [USMCollectionMapController::class, 'edit'])->name('edit');
        Route::post('update', [USMCollectionMapController::class, 'update'])->name('update');
    });

    Route::prefix('search')->name('search.')->group(function () {
        Route::get('/lineitem_family_options', [SearchController::class, 'getLineitemFamilyOptions'])->name('lineitem_family_options');
        Route::get('/attributes_selector_options', [SearchController::class, 'getAttributesSelectorOptions'])->name('attributes_selector_options');
        Route::get('/bin_matrix_options', [SearchController::class, 'getBinMatrixOptions'])->name('bin_matrix_options');
    });
});

require __DIR__ . '/auth.php';
require __DIR__ . '/components.php';
