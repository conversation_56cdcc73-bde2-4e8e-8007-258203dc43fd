<script setup>
import FlashAlert from '@/Components/FlashAlert.vue';
import Modal from '@/Components/Modal.vue';
import TextInput from '@/Components/TextInput.vue';
import { useForm } from '@inertiajs/vue3';
import axios from 'axios';
import { onMounted, ref, computed } from 'vue';

const componentLevelModal = ref(null);

const props = defineProps({
    usm_id: {
        type: Number,
        required: true,
    },
    editDisabled: {
        type: Boolean,
        default: false,
    },
    viewOnly: {
        type: Boolean,
        default: true,
    },
});

const form = useForm({
    rows: [],
});

onMounted(() => {
    const modalElement = document.getElementById('componentLevelModal');
    modalElement.addEventListener('show.bs.modal', () => {
       axios.get(route('components.levels', props.usm_id)).then(response => {
           form.rows = response.data.data;
       })
    });
})

const save = () => {
    form.post(route('components.levels', props.usm_id), {
        preserveScroll: true,
        onSuccess: () => closeModal(),
    });
};

const closeModal = () => {
    form.clearErrors();
    resetInput();
    componentLevelModal.value.close();
};

const resetInput = () => {
    form.reset();
};

//function on this page
const addRow = () => {
    form.rows.push({
        id: null,
        name: '',
        level: form.rows.length + 1,
    });
};

const removeRow = index => {
    form.rows.splice(index, 1);
    //Reupdate the sequence
    form.rows.forEach((row, index) => {
        row.level = index + 1;
    });
};

//Computed
const buttonYes = computed(() => {
    if (props.viewOnly) return null;
    return props.editDisabled ? null : 'Save';
})
</script>

<template>
    <Modal ref="componentLevelModal" @yesEvent="save" @noEvent="closeModal" :id="'componentLevelModal'" :title="'Components Levels'" :buttonYes="buttonYes" :buttonType="'primary'" :form="form">
        <FlashAlert v-if="Object.keys(form.errors).length && !form.errors.file" :status="'danger'" @close="form.clearErrors()">
            <label v-for="(message, field) in form.errors" :key="field">
                {{ field }}: {{ message }}
                {{ message }}
            </label>
        </FlashAlert>

        <div class="mt-6">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th v-if="!viewOnly" width="3%">
                            <button type="button" class="btn btn-sm btn-primary" @click="addRow"><i class="bi bi-plus"></i></button>
                        </th>
                        <th width="20%">Level</th>
                        <th>Name</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(row, index) in form.rows" :key="index">
                        <td v-if="!viewOnly" class="text-center">
                            <button type="button" class="btn btn-sm btn-danger" @click="removeRow(index)"><i class="bi bi-dash"></i></button>
                        </td>
                        <td class="text-center">
                            <input type="hidden" v-model="row.id" />
                            <TextInput type="text" v-model="row.level" readonly />
                        </td>
                        <td>
                            <TextInput type="text" v-model="row.name" readonly />
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </Modal>
</template>
