<?php

namespace App\Http\Controllers;

use App\Models\LineitemManager;
use App\Models\SpeedBatch;
use Illuminate\Http\Request;

class DynamicTableController extends Controller
{
    public function index(Request $request)
    {
        if (isset($request->lineitem_id)) {
            $line = LineitemManager::find($request->lineitem_id);
            $model = $line->getTableModal();
            $control = $this->excelControl($request->tableType, $line->approved);
        } else if (isset($request->batch_id)) {
            $batch = SpeedBatch::find($request->batch_id);
            if ($request->tableType == DYN_TABLE_SPEED) {
                $model = $batch->getBasedModal();
            } else {
                $model = $batch->getViewModal();
            }
            $control = $this->excelControl($request->tableType, $batch->isSnapshot);
        }


        $data = $model->get();
        $columnDetails = $model->buildExcelColumns();
        $data = $data->map->encodeAllAttributes();

        return response()->json([
            'data' => $data,
            'columns' => $columnDetails,
            'control' => $control,
        ]);
    }

    public function store(Request $request)
    {
        //Only Speed Based Table can be add new row
        if ($request->tableType != DYN_TABLE_SPEED) return;

        $batch = SpeedBatch::find($request->batch_id);
        $newRow = $batch->getBasedModal();
        $newRow->save();

        return response()->json([
            'id' => $newRow->id,
        ]);
    }

    public function update(Request $request)
    {
        //Only Speed Based Table can edit row
        if ($request->tableType != DYN_TABLE_SPEED) return;
        $updateData = $request->input('updates');

        $batch = SpeedBatch::find($request->batch_id);
        $dynamicTable = $batch->getBasedModal();

        foreach ($updateData as $update) {
            $id = $update['keys'][0];
            $column = decodeColumn($update['name']);
            $newValue = $update['newVal'];

            $dynamicTable->where('id', $id)->update([$column => $newValue]);
        }

        return response()->json([
            'message' => 'Updates saved successfully',
        ]);
    }

    public function delete(Request $request)
    {
        //Only Speed Based Table can delete row
        if ($request->tableType != DYN_TABLE_SPEED) return;
        $deleteData = $request->input('deletes');

        $batch = SpeedBatch::find($request->batch_id);
        $dynamicTable = $batch->getBasedModal();

        foreach ($deleteData as $delete) {
            $dynamicTable->where('id', $delete['id'])->delete();
        }
    }

    public function destroy(Request $request)
    {
        //Only Speed Based Table can destroy row
        if ($request->tableType != DYN_TABLE_SPEED) return;
        $deleteData = $request->input('deletes');

        $batch = SpeedBatch::find($request->batch_id);
        $dynamicTable = $batch->getBasedModal();

        foreach ($deleteData as $delete) {
            $dynamicTable->where('id', $delete['id'])->delete();
        }
    }

    private function excelControl($tableType, bool $isApproved = false)
    {
        $control['isUpdateable'] = false;
        $control['isDeleteable'] = false;
        $control['isAddable'] = false;

        //Handle neccessary logic here
        if ($tableType == DYN_TABLE_LINEITEM) return $control; //LineItem cannot edit
        if ($isApproved) return $control; //Approved cannot edit

        if ($tableType == DYN_TABLE_SPEED) {
            $control['isUpdateable'] = true;
            $control['isDeleteable'] = true;
            $control['isAddable'] = true;
        }

        return $control;
    }
}
