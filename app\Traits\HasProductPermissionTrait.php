<?php

namespace App\Traits;

use App\Models\User;

trait HasProductPermissionTrait
{
    public function getAllowedIds(User $user)
    {
        $modelType = get_class($this);

        // Get directly permitted IDs
        $directIds = $user->productPermissions()
            ->where('accessible_type', $modelType)
            ->pluck('accessible_id');

        // If this is a product or product_group, also check parent permissions
        if ($modelType === Product::class) {
            // Include products from permitted product_types
            $parentTypeIds = $user->productPermissions()
                ->where('accessible_type', ProductType::class)
                ->pluck('accessible_id');

            if ($parentTypeIds->isNotEmpty()) {
                $directIds = $directIds->merge(
                    Product::whereIn('product_type_id', $parentTypeIds)->pluck('id')
                );
            }
        } elseif ($modelType === ProductGroup::class) {
            // Include groups from permitted products and product_types
            $permittedProductIds = $user->productPermissions()
                ->where('accessible_type', Product::class)
                ->pluck('accessible_id');

            $permittedTypeIds = $user->productPermissions()
                ->where('accessible_type', ProductType::class)
                ->pluck('accessible_id');

            if ($permittedProductIds->isNotEmpty()) {
                $directIds = $directIds->merge(
                    ProductGroup::whereIn('product_id', $permittedProductIds)->pluck('id')
                );
            }

            if ($permittedTypeIds->isNotEmpty()) {
                $productIds = Product::whereIn('product_type_id', $permittedTypeIds)->pluck('id');
                $directIds = $directIds->merge(
                    ProductGroup::whereIn('product_id', $productIds)->pluck('id')
                );
            }
        }

        return $directIds->unique();
    }
}
