import './bootstrap'; //Initial Require Plugin
import '../sass/app.scss'; //Boostrap 5
import 'vue-multiselect/dist/vue-multiselect.min.css'; //Multiselect
import '../css/app.css'; //Custom CSS
import '../css/crossship-dropdown.css'; //CrossShip Dropdown CSS

import { createApp, h } from 'vue';
import { createInertiaApp } from '@inertiajs/vue3';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { ZiggyVue } from '../../vendor/tightenco/ziggy/dist/vue.m';

import VueExcelEditor from 'vue3-excel-editor'

const appName = window.document.getElementsByTagName('title')[0]?.innerText || 'Laravel';

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) => resolvePageComponent(`./Pages/${name}.vue`, import.meta.glob('./Pages/**/*.vue')),
    setup({ el, App, props, plugin }) {
        return createApp({ render: () => h(App, props) })
            .use(plugin)
            .use(ZiggyVue, Ziggy)
            .use(VueExcelEditor)
            .mount(el);
    },
    progress: {
        color: '#0288D1',
    },
});
