<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //This is to keep the list of scenarios for each module when the scenario_mode is ScenarioAttributes
        Schema::create('usm_scenarios', function (Blueprint $table) {
            $table->id();
            $table->boolean('in_xml')->default(true);
            $table->string('name');
            $table->text('expression');
            $table->smallInteger('sequence')->default(0);
            $table->unsignedBigInteger('usm_id');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('usm_scenarios');
    }
};
