<?php

namespace App\Http\Controllers;

use App\Http\Requests\LineItemManagerUpdateRequest;
use App\Jobs\GenerateLineitemTableJob;
use App\Models\GenerateLineitemLog;
use App\Models\LineitemManager;
use App\Models\SpeedBatch;
use App\Models\SpeedDynamic;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;

class LineItemManagerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $list = LineitemManager::query()->with(['createdUser'])
            ->where('speed_batch_id', $request->speed_batch_id)
            ->orderBy('created_at', 'desc')->get();

        return response()->json([
            'header' => LineitemManager::header(),
            'list' => $list,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        return $this->edit(null, $request);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(LineItemManagerUpdateRequest $request)
    {
        return $this->update($request, null);
    }

    /**
     * Display the specified resource.
     */
    public function show(LineitemManager $lineitemManager)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(?LineitemManager $lineitemManager = null,  Request $request)
    {
        $speed_batch_id = $request->speed_batch_id;
        $modules = config('settings.modules');
        $datatypes = config('settings.dataTypes');
        $ownAttributes = [];

        if (null === $lineitemManager) {
            $data = new LineitemManager;
            $speedBatch = SpeedBatch::find($speed_batch_id);
            $data->speed_batch_id = $speed_batch_id;
            $data->speed_batch = $speedBatch;
        } else {
            $data = $lineitemManager->load(['lineitemAttributes', 'speedBatch', 'createdUser', 'approvedUser'])->append('is_generating');
            $speedBatch = $data->speedBatch;
            $speed_batch_id = $data->speed_batch_id;
            //This is use to remove from existing columns,own attribute no need to prompt error
            foreach ($data->lineitemAttributes as $attribute) {
                $ownAttributes[] = $attribute->attribute;
            }
        }

        //Get all columns from speed tables
        $columns = $speedBatch->getBasedModal()->getTableColumns();

        //Find other lineitemattribute
        $batchLineItemManagers = LineitemManager::with('lineitemAttributes')->where('speed_batch_id', $speed_batch_id)->get();
        foreach ($batchLineItemManagers as $batchLineItemManager) {
            foreach ($batchLineItemManager->lineitemAttributes as $attribute) {
                $columns[] = $attribute->attribute;
            }
        }

        //Remove own attributes from existing columns
        $existingColumns = array_values(array_diff($columns, $ownAttributes));

        return Inertia::render('LineitemManager/Edit', [
            'data' => Inertia::always($data),
            'modules' => $modules,
            'datatypes' => $datatypes,
            'columns' => $columns,
            'existingColumns' => $existingColumns,
            'expressionHtml' => config('settings.expressionHtml'),
            'logs' => $lineitemManager ? $this->getGenerateLineitemLogs($lineitemManager->id) : [],
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(LineItemManagerUpdateRequest $request, ?LineitemManager $lineitemManager = null)
    {
        $data = $request->validated();
        if (null === $lineitemManager) {
            $data['created_user_id'] = auth()->id();
            // $data['speed_batch_id'] = $this->getBatchId($data);

            $lineitemManager = LineitemManager::create($data);
            $lineitemManager->lineitemAttributes()->createMany($data['attributes']);

            return Redirect::route('lineitem_managers.edit', $lineitemManager->id)->with('message', 'Line Item created successfully');
        } else {

            //Create new revision
            // if ($data['create_revision']) {
            //     $lineitemManager = $lineitemManager->createNewVersion($data);
            //     $lineitemManager->attributes()->createMany($data['attributes']);

            //     return Redirect::route('lineitem_managers.edit', $lineitemManager->id)->with('message', "Line Item Revision {$lineitemManager->revision} created successfully");
            // } else {

            $store_row_ids = [];
            foreach ($data['attributes'] as $attribute) {
                $attribute['lineitem_manager_id'] = $lineitemManager->id;
                $result = $lineitemManager->lineitemAttributes()->updateOrCreate(['id' => $attribute['id']], $attribute);

                //Keep the Row ID to use for delete later
                $store_row_ids[] = $result->id;
            }

            //Delete Removed Rows
            $lineitemManager->lineitemAttributes()->whereNotIn('id', $store_row_ids)->delete();

            // if ($lineitemManager->speed_batch_id == null) {
            //     $data['speed_batch_id'] = $this->getBatchId($data);
            // }
            $lineitemManager->update($data);
            //}

            return Redirect::route('lineitem_managers.edit', $lineitemManager->id)->with('message', 'Line Item updated successfully');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(LineitemManager $lineitemManager)
    {
        $oldLineitemManagerId = $lineitemManager->id;
        $oldName = $lineitemManager->name;

        $speedBatchId = $lineitemManager->speed_batch_id;
        $lineitemManager->lineitemAttributes()->delete();
        $lineitemManager->delete();

        //Find next available LineItemManager to regenerate
        $nextLineitemManager = LineitemManager::where('speed_batch_id', $speedBatchId)->first();
        if ($nextLineitemManager) {
            $message = "LineitemManager $oldName ($oldLineitemManagerId) deleted by " . auth()->user()->name . ', regenerating view table.';
            $formattedMessage = "[" . now()->format('Y-m-d H:i:s') . "] " . $message . "\n";
            $generate = GenerateLineitemLog::create([
                'lineitem_manager_id' => $nextLineitemManager->id,
                'started_at' => now(),
                'user_id' => auth()->id(),
                'messages' => $formattedMessage
            ]);

            GenerateLineitemTableJob::dispatch($generate->id);
        } else {
            //No more lineitemmanager, drop the view table
            \Log::info("No more LineitemManager found, last LineitemManager $oldName ($oldLineitemManagerId) deleted by " . auth()->user()->name . ', drop view table.');
            $speedBatch = SpeedBatch::find($speedBatchId);
            $speedBatch->dropGenerateViewTable();
        }

        return response()->json([
            'message' => 'Line Item deleted successfully'
        ]);
    }

    // private function getBatchId($data)
    // {
    //     $batch = SpeedBatch::where('is_current', true)
    //         ->whereProducts($data['product_type_id'], $data['product_id'], $data['product_group_id'])
    //         ->first();

    //     if ($batch) {
    //         return $batch->id;
    //     }
    //     return null;
    // }

    public function postApprove(LineitemManager $lineitemManager)
    {
        $lineitemManager->setApprove();

        //Re-query
        $data = LineitemManager::with('approvedUser')->find($lineitemManager->id);
        return response()->json(['message' => 'Line Item approved successfully', 'data' => $data]);
    }

    public function postGenerate(LineitemManager $lineitemManager)
    {
        //Protection
        $exists = GenerateLineitemLog::isGenerating($lineitemManager->id)->first();
        if ($exists) {
            return response()->json(['message' => 'Generating in progress, please check back again later.'], 400);
        }

        $generate = GenerateLineitemLog::create([
            'lineitem_manager_id' => $lineitemManager->id,
            'started_at' => now(),
            'user_id' => auth()->id()
        ]);

        GenerateLineitemTableJob::dispatch($generate->id);

        return response()->json(['message' => 'Start preparing generation, please check back again later.']);
    }

    public function getComparision(Request $request)
    {
        $speed1 = new SpeedDynamic;
        $speed1->setTable($request->table1);

        $speed2 = new SpeedDynamic;
        $speed2->setTable($request->table2);

        $results = $this->compareAllRowsSequentially($speed1, $speed2);

        return response()->json($results);
    }

    function compareAllRowsSequentially(SpeedDynamic $model1, SpeedDynamic $model2): array
    {
        $exclude_columns = ['id', 'batch_id', 'created_at', 'line_item_id', 'updated_at', 'deleted_at'];
        $differences = [];
        $columnsWithDifferences = []; // Track columns with differences

        // Fetch all rows from both models
        $rows1 = $model1->get();
        $rows2 = $model2->get();

        // Determine the number of rows to compare
        $rowCount = max($rows1->count(), $rows2->count());

        // Get all columns from both tables (excluding excluded columns)
        $columns1 = array_diff(array_keys($rows1->first()->getAttributes()), $exclude_columns);
        $columns2 = array_diff(array_keys($rows2->first()->getAttributes()), $exclude_columns);
        $allColumns = array_unique(array_merge($columns1, $columns2)); // Union of all columns

        // Iterate through the rows sequentially
        for ($i = 0; $i < $rowCount; $i++) {
            $row1 = $rows1[$i] ?? null;
            $row2 = $rows2[$i] ?? null;

            if ($row1 && $row2) {
                // Compare attributes of the two rows
                $rowDifferences = [];
                foreach ($allColumns as $column) {
                    $value1 = $row1->$column ?? null;
                    $value2 = $row2->$column ?? null;

                    if ($value1 !== $value2) {
                        $rowDifferences[$column] = [
                            'row1' => $value1,
                            'row2' => $value2,
                        ];
                        // Track columns with differences
                        if (!in_array($column, $columnsWithDifferences)) {
                            $columnsWithDifferences[] = $column;
                        }
                    }
                }

                // If differences exist, add them to the result
                if (!empty($rowDifferences)) {
                    $differences[$i] = $rowDifferences;
                }
            } elseif ($row1) {
                // Row exists in $rows1 but not in $rows2
                $differences[$i] = [
                    'status' => 'Row missing in Table 2',
                    'row1' => $row1->getAttributes(),
                    'row2' => null,
                ];
            } elseif ($row2) {
                // Row exists in $rows2 but not in $rows1
                $differences[$i] = [
                    'status' => 'Row missing in Table 1',
                    'row1' => null,
                    'row2' => $row2->getAttributes(),
                ];
            }
        }

        return [
            'differences' => $differences,
            'columnsWithDifferences' => $columnsWithDifferences,
        ];
    }

    public function getLogs(LineitemManager $lineitemManager)
    {
        $logs = $this->getGenerateLineitemLogs($lineitemManager->id);
        return response()->json($logs);
    }

    private function getGenerateLineitemLogs($lineitemManager_id)
    {
        $logs = GenerateLineitemLog::with('user')->where('lineitem_manager_id', $lineitemManager_id)->orderBy('created_at', 'desc')->limit(6)->get();

        return $logs;
    }
}
