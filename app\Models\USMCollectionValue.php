<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class USMCollectionValue extends BaseModel
{
    use SoftDeletes;

    protected $table = 'usm_collection_values';

    protected $fillable = [
        'input_value',
        'value',
        'usm_collection_id',
        'usm_collection_value_set_id',
        'usm_module_component_attribute_id',
        'usm_id',
    ];

    /**
     * The collection this value belongs to
     */
    public function collection()
    {
        return $this->belongsTo(USMCollection::class, 'usm_collection_id');
    }

    /**
     * The value set this value belongs to
     */
    public function valueSet()
    {
        return $this->belongsTo(USMCollectionValueSet::class, 'usm_collection_value_set_id');
    }

    /**
     * The module component attribute this value is for
     */
    public function moduleComponentAttribute()
    {
        return $this->belongsTo(USMModuleComponentAttribute::class, 'usm_module_component_attribute_id');
    }

    /**
     * The USM this value belongs to
     */
    public function usm()
    {
        return $this->belongsTo(USM::class, 'usm_id');
    }
}
