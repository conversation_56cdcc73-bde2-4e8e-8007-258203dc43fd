<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Many to Many
     */
    public function up(): void
    {
        //Many to Many relationship between usm_module_component_attributes and usm_collections
        Schema::create('usm_module_component_attribute_usm_collection', function (Blueprint $table) {
            $table->unsignedBigInteger('usm_module_component_attribute_id');
            $table->unsignedBigInteger('usm_collection_id');
            $table->primary(['usm_module_component_attribute_id', 'usm_collection_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('usm_module_component_attribute_usm_collection');
    }
};
