<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bin_matrix_items', function (Blueprint $table) {
            $table->id();
            $table->string('bom_group')->nullable();
            $table->string('bin_type')->nullable();
            $table->smallInteger('sequence')->default(0);
            $table->text('bom_list')->nullable(); //Multiple BOM List, auto generate
            $table->unsignedBigInteger('bin_matrix_id');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bin_matrix_items');
    }
};
