<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('usm_collection_values', function (Blueprint $table) {
            $table->id();
            $table->text('input_value')->nullable(); //can be value or expression
            $table->string('value')->nullable(); //actual 
            $table->unsignedBigInteger('usm_collection_id');
            $table->unsignedBigInteger('usm_collection_value_set_id');
            $table->unsignedBigInteger('usm_module_component_attribute_id');
            $table->unsignedBigInteger('usm_id');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('usm_collection_values');
    }
};
