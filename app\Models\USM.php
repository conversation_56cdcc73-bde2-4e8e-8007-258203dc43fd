<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class USM extends BaseModel
{
    use SoftDeletes;

    protected $table = 'universal_spec_managers';

    public $fillable = [
        'name',
        'state',
        'product_type_id',
        'product_id',
        'product_group_id',
        'created_user_id',
        'active',
        'state_update_user_id',
        'state_updated_at',
        'bin_matrix_id',
        // 'revision',
        // 'is_current',
        // 'previous_revision_id',
        // 'original_id',
    ];

    protected $attributes = [
        'active' => true,
        'state' => STATE_WIP
    ];

    public function productType(): BelongsTo
    {
        return $this->belongsTo(ProductType::class, 'product_type_id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function productGroup(): BelongsTo
    {
        return $this->belongsTo(ProductGroup::class, 'product_group_id');
    }

    public function createdUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_user_id');
    }

    public function modules(): HasMany
    {
        return $this->hasMany(USMModule::class, 'usm_id')->orderBy('sequence');
    }

    public function binMatrix(): BelongsTo
    {
        return $this->belongsTo(BinMatrix::class, 'bin_matrix_id');
    }

    /**
     * Get the scenarios associated with the USM.
     */
    public function scenarios(): HasMany
    {
        return $this->hasMany(USMScenario::class, 'usm_id', 'id');
    }

    /**
     * Get the fuse configs associated with the USM.
     */
    public function fuseManagers(): BelongsToMany
    {
        return $this->belongsToMany(FuseManager::class, 'fuse_manager_usm', 'usm_id', 'fuse_manager_id');
    }

    public function components(): HasMany
    {
        return $this->hasMany(Component::class, 'usm_id');
    }

    //Scope function Below
    // Scope to filter by user permissions
    public function scopeAccessibleBy($query, User $user)
    {
        $permissions = $user->getAllProductPermissions();

        return $query->where(function ($query) use ($permissions) {
            // Case 1: When there are specific product group permissions
            if (!empty($permissions['productGroups'])) {
                // Only allow specifically permitted product groups
                $query->whereIn('product_group_id', array_keys($permissions['productGroups']));
            } else {
                // Case 2: When no specific product group permissions
                if (!empty($permissions['products'])) {
                    $query->where(function ($q) use ($permissions) {
                        $q->whereIn('product_id', array_keys($permissions['products']));
                    });
                }

                if (!empty($permissions['productTypes'])) {
                    $query->where(function ($q) use ($permissions) {
                        $q->whereIn('product_type_id', array_keys($permissions['productTypes']));
                    });
                }
            }
        });
    }

    //Static Function Belows
    public static function header()
    {
        $headers = [];
        return array_merge($headers, [
            ['field' => 'name', 'title' => 'Name', 'sortable' => false],
            ['field' => 'state', 'title' => 'State', 'sortable' => false],
            ['field' => '', 'title' => 'Product Code Name', 'sortable' => false],
            ['field' => '', 'title' => 'Product Group', 'sortable' => false],
            ['field' => '', 'title' => 'Created By'],
            ['field' => 'created_at', 'title' => 'Created At'],
        ]);
    }

    public static function states()
    {
        return [STATE_SNAPSHOT];
    }

    // Version history methods
    public function createNewVersion(array $data): self
    {
        $latest_revision = self::getLatestRevision($this->original_id ?? $this->id);

        $data = array_merge($data, [
            'revision' => $latest_revision == null ? 1 : $latest_revision->revision + 1,
            'is_current' => false,
            'previous_revision_id' => $this->id,
            'created_user_id' => auth()->id(),
            'original_id' => $latest_revision == null ? $this->id : $latest_revision->original_id,
        ]);

        // Create new version
        return self::create($data);
    }

    public static function getRevisionHistory(int $id)
    {
        return self::where('id', $id)
            ->orWhere(function ($query) use ($id) {
                $query->where(function ($q) use ($id) {
                    $manager = self::find($id);
                    $previousIds = [];
                    while ($manager && $manager->previous_revision_id) {
                        $previousIds[] = $manager->previous_revision_id;
                        $manager = $manager->previousVersion;
                    }
                    if (!empty($previousIds)) {
                        $q->whereIn('id', $previousIds);
                    }
                });
            })
            ->orderBy('revision', 'desc')
            ->get();
    }

    public static function getLatestRevision(int $id)
    {
        return self::where('original_id', $id)
            ->orderBy('revision', 'desc')
            ->select('revision', 'original_id')
            ->first();
    }
}
