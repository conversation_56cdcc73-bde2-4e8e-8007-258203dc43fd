<?php

namespace App\Models;

use App\Traits\ModuleModelTrait;
use Illuminate\Support\Facades\Schema;

class LineDynamic extends BaseModel
{
    use ModuleModelTrait;

    protected $table = null; // Need to assign name table name

    public function getTable()
    {
        return $this->table;
    }

    // Optional: Allow setting a custom table name dynamically
    public function setTable($table)
    {
        $this->table = $table;
        return $this;
    }

    public function getTableColumns()
    {
        $excludedColumns = ['speed_id', 'speed_batch_id'];
        $columns = Schema::getColumnListing($this->getTable());
        return array_values(array_diff($columns, $excludedColumns));
    }
}
