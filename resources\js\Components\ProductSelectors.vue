<template>
    <div class="col-md-3">
        <InputLabel for="product_type_id" value="Product Type" />
        <div class="d-flex">
            <Select id="product_type_id" v-model="modelValue.product_type_id" :invalid="errors?.product_type_id" :options="productTypes" :placeholder="'Select Product Type'" required class="flex-grow-1" />
        </div>
        <InputError :message="errors?.product_type_id" />
    </div>
    <div class="col-md-3">
        <InputLabel for="product_id" value="Product" />
        <div class="d-flex">
            <Select id="product_id" v-model="modelValue.product_id" :invalid="errors?.product_id" :options="filteredProducts" :placeholder="'Select Product'" required class="flex-grow-1" />
        </div>
        <InputError :message="errors?.product_id" />
    </div>
    <div class="col-md-3">
        <InputLabel for="product_group_id" value="Product Group" />
        <Select id="product_group_id" v-model="modelValue.product_group_id" :invalid="errors?.product_group_id" :options="filteredProductGroups" :placeholder="'Select Product Group'" required />
        <InputError :message="errors?.product_group_id" />
    </div>
</template>

<script setup>
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import Select from '@/Components/Select.vue';
import { computed } from 'vue';

const props = defineProps({
    modelValue: {
        type: Object,
        required: true,
    },
    productTypes: {
        type: Array,
        default: () => [],
    },
    products: {
        type: Array,
        default: () => [],
    },
    productGroups: {
        type: Array,
        default: () => [],
    },
    errors: {
        type: Object,
        default: () => ({}),
    },
});

const emit = defineEmits(['update:modelValue']);

// Simple computed properties for filtering without any side effects
const filteredProducts = computed(() => {
    if (!props.modelValue.product_type_id) return [];
    return props.products.filter(product => product.product_type_id == props.modelValue.product_type_id);
});

const filteredProductGroups = computed(() => {
    if (!props.modelValue.product_id) return [];
    return props.productGroups.filter(group => group.product_id == props.modelValue.product_id);
});
</script>
