<?php

namespace App\Services;

use App\Helpers\ComponentHelper;
use App\Models\USM;
use App\Models\USMCollection;
use App\Models\USMCollectionMap;
use App\Models\USMCollectionValue;
use App\Models\USMModule;
use App\Models\Component;
use App\Models\ComponentLevel;
use App\Models\USMModuleComponentAttribute;

class GenerateStructureXmlService
{
    public $usm_id;
    protected $usm;
    protected $tree;
    protected $component_levels;

    public function __construct(int $usm_id)
    {
        $this->usm_id = $usm_id;
        $this->usm = USM::with(['productGroup', 'createdUser'])->find($usm_id);

        $this->tree = (new ComponentHelper)->getComponentTree($usm_id, true);
        $this->component_levels = ComponentLevel::where('usm_id', $usm_id)->orderBy('level')->pluck('name', 'id')->toArray();
    }

    /**
     * Generate the USM export XML file
     * 
     * @return string The XML document as a string
     */
    public function generateStructureXML()
    {
        if (!$this->usm) {
            throw new \Exception("USM with ID {$this->usm_id} not found");
        }

        // Create a new XML document
        $dom = new \DOMDocument('1.0', 'utf-8');
        $dom->formatOutput = true;

        // Create the root element <Suite>
        $rootElement = $dom->createElement('UpsStructure');
        $dom->appendChild($rootElement);

        // Create Info element
        $infoElement = $dom->createElement('Info');
        $rootElement->appendChild($infoElement);

        // Add child elements to Info - these would be replaced with actual data
        $infoFields = [
            'Name' => $this->usm->productGroup->name,
            'FullName' => $this->usm->productGroup->name,
            'Description' => '',
            'CreatedBy' => $this->usm->createdUser ? $this->usm->createdUser->name : '',
            'CreatedDateTime' => $this->usm->created_at ? $this->usm->created_at->format('n/j/Y g:i:s A \U\T\C') : date('n/j/Y g:i:s A \U\T\C'),
            'FileFormat' => '2'
        ];

        foreach ($infoFields as $fieldName => $fieldValue) {
            $fieldElement = $dom->createElement($fieldName);
            $fieldElement->appendChild($dom->createTextNode($fieldValue));
            $infoElement->appendChild($fieldElement);
        }

        //Structure Map
        $structureElement = $dom->createElement('Structure');
        $structureElement->setAttribute('name', $this->usm->productGroup->name);
        $rootElement->appendChild($structureElement);

        //Build Tree
        $this->tree($structureElement, $dom);

        //Hardcode Category Recovery
        $categoryElement = $dom->createElement('Category');
        $categoryElement->setAttribute('name', 'Recovery');
        $structureElement->appendChild($categoryElement);
        $groupElement = $dom->createElement('Group');
        $groupElement->setAttribute('name', 'DummyRecovery');
        $groupElement->setAttribute('type', 'BasicRecovery');
        $groupElement->setAttribute('priority', '1');
        $categoryElement->appendChild($groupElement);

        return $dom->saveXML();
    }

    private function tree(\DOMElement $parentElement, \DOMDocument $dom, ?array $nodes = null): void
    {
        $nodes = $nodes ?? $this->tree;
        foreach ($nodes as $obj) {
            $levelElement = $dom->createElement('Level');
            $levelElement->setAttribute('name', $this->component_levels[$obj['component_level_id']] ?? '');
            $parentElement->appendChild($levelElement);

            $type = "";
            if (isset($obj['component_type']) && !empty($obj['component_type'])) {
                $ct = $obj['component_type'];

                $ct_levelName = strtolower($ct['level']);
                $levelName = strtolower($this->component_levels[$obj['component_level_id']]  ?? '');
                //Hardcode, provided sample does not match the component type xml provided, this follow the Structure.xml
                if ($ct_levelName === "package") {
                    $type = $ct['type'];
                } else if ($ct_levelName === "rail") {
                    $type = "InfiBusRail";
                } else if ($ct_levelName === "domain") {
                    $type = "AvpDomain";
                } else if ($levelName  === "domain") {
                    $type = "AvpDomain";
                } else {
                    $type = $obj['component_type']['name'];
                }
            }

            $componentElement = $dom->createElement('Component');
            $componentElement->setAttribute('name', $obj['name']);
            $componentElement->setAttribute('type', $type);
            $componentElement->setAttribute('startIndex', 0);
            $componentElement->setAttribute('count', 1);
            $levelElement->appendChild($componentElement);

            if (!empty($obj['children'])) {
                $this->tree($componentElement, $dom, $obj['children']);
            }
        }
    }

    /**
     * Save the USM XML to a file
     * 
     * @param string $path Optional path to save the file. If null, will use storage_path.
     * @return string The full path to the saved file
     */
    public function saveXMLToFile($path = null)
    {
        $xml = $this->generateStructureXML();

        if ($path === null) {
            $fileName = 'structure_' . date('Ymd_His') . '.xml';
            $path = storage_path('app/exports/usm/' . $this->usm->id . '/' . $fileName);

            // Create directory if it doesn't exist
            if (!file_exists(dirname($path))) {
                mkdir(dirname($path), 0755, true);
            }
        }

        file_put_contents($path, $xml);

        return $path;
    }
}
