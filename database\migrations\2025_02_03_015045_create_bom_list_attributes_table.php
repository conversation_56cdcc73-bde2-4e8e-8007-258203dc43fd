<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bom_list_attributes', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->integer('length');
            $table->smallInteger('sequence')->default(0);
            $table->string('attribute_name')->nullable(); //From another table column
            $table->unsignedBigInteger('lineitem_attribute_id')->nullable(); //Can be null, some attribute_name are from speed column
            $table->unsignedBigInteger('bin_matrix_id')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bom_list_attributes');
    }
};
