<script setup>
import { onMounted, ref, computed } from 'vue';

const props = defineProps({
    modelValue: {
        type: [String, Number, null],
        required: true,
        default: null,
    },
    invalid: {
        type: String,
        default: null,
    },
    options: {
        type: [Object, Array],
        required: true,
    },
    placeholder: {
        type: String,
        default: 'Please select',
    },
    label_key: {
        type: String,
        default: 'name',
    },
    value_key: {
        type: String,
        default: 'id',
    },
    placeholder_disabled: {
        type: Boolean,
        default: true,
    },
});

defineEmits(['update:modelValue']);

const select = ref(null);

onMounted(() => {
    if (select.value.hasAttribute('autofocus')) {
        select.value.focus();
    }
});

defineExpose({ focus: () => select.value.focus() });

// Computed property to normalize the options
const normalizedOptions = computed(() => {
    if (Array.isArray(props.options)) {
        // If options is an array, map it to the expected format
        return props.options.reduce((acc, item) => {
            acc[item[props.value_key]] = item[props.label_key];
            return acc;
        }, {});
    } else {
        // If options is already an object, return it as is
        return props.options;
    }
});
</script>

<template>
    <select class="form-select" :class="{ 'is-invalid': invalid }" :value="modelValue" @change="$emit('update:modelValue', $event.target.value)" ref="select">
        <option value="" :disabled="placeholder_disabled">{{ placeholder }}</option>
        <option v-for="(label, id) in normalizedOptions" :key="id" :value="id">
            {{ label }}
        </option>
    </select>
</template>
