<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fuse_config_items', function (Blueprint $table) {
            $table->id();
            $table->string('register');
            $table->integer('size');
            $table->string('datatype')->nullable(); //Binary or Hex
            $table->string('pin_to_read')->nullable();
            $table->string('pin_to_modify')->nullable();
            $table->smallInteger('direction')->default(1); //0: Low to High, 1: High to Low
            $table->smallInteger('sequence')->default(0);
            $table->unsignedBigInteger('fuse_config_id')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fuse_config_items');
    }
};
