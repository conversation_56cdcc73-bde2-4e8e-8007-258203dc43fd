<?php

namespace App\Services;

use App\Models\SpeedBatch;
use App\Models\SpeedDynamic;

class LineDatabase
{

    protected $speed_batch_id;
    protected $batch;
    protected $line;
    protected $generate_table_name;
    protected $baseModal; //Used to get table columns and data types
    protected $model;
    protected $is_speed_table; //No lineitem table use back default speed table

    public function __construct(int $speed_batch_id)
    {
        $this->speed_batch_id = $speed_batch_id;
        $this->batch = SpeedBatch::find($speed_batch_id);

        if ($this->batch != null) {
            $this->generate_table_name = $this->batch->generateViewName;
        }

        if ($this->viewExists($this->generate_table_name)) {
            $this->is_speed_table = false;
            $this->model = $this->batch->getViewModal();
            $this->baseModal = $this->batch->getViewModal();
        } else {
            $this->is_speed_table = true;
            $this->model = $this->batch->getBasedModal();
            $this->baseModal = $this->batch->getBasedModal();
        }
    }

    public function isSpeedTable()
    {
        return $this->is_speed_table;
    }

    public function getTableName(): string
    {
        if ($this->is_speed_table) {
            return $this->batch->generate_based_name;
        }
        return $this->generate_table_name;
    }

    public function getColumns(): array
    {
        //Due to $this->model have where condition it will cause error, therefore change to $this->baseModal
        return $this->baseModal->getTableColumns();
    }

    public function getColumnsWithDataType()
    {
        return $this->baseModal->getColumnsWithDataTypes();
    }

    public function getModel(): object
    {
        return $this->model;
    }

    public function getSspecList()
    {
        return $this->model->select('QDF/SSPEC')
            ->pluck('QDF/SSPEC')
            ->unique()
            ->filter(function ($value) {
                return !is_null($value);
            })
            ->flatten()
            ->toArray();
    }

    public function getSspecXmlColumns(): array
    {
        $columns = $this->getColumns();
        //Reorder the column QDF/SSPEC and MM# to first
        $columns = array_merge(['QDF/SSPEC', 'MM#'], array_diff($columns, ['QDF/SSPEC', 'MM#']));
        return $columns;
    }

    private function viewExists(string $viewName): bool
    {
        $result = \DB::selectOne("
                SELECT EXISTS (
                    SELECT 1 
                    FROM pg_matviews 
                    WHERE schemaname = 'public' 
                    AND matviewname = ?
                ) AS exists;
            ", [$viewName]);

        return $result->exists;
    }
}
