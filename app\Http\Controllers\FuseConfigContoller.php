<?php

namespace App\Http\Controllers;

use App\Helpers\FuseTextFile;
use App\Http\Requests\FuseConfigUpdateRequest;
use App\Imports\FuseConfigImport;
use App\Models\BinMatrix;
use App\Models\FuseConfig;
use App\Models\FuseConfigLineitem;
use App\Models\FuseManager;
use App\Models\Product;
use App\Models\ProductGroup;
use App\Models\ProductType;
use App\Services\GenerateFuseDefinitionTxtService;
use App\Services\GenerateSspecTxtService;
use App\Services\LineDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Maatwebsite\Excel\Facades\Excel;

class FuseConfigContoller extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        //Build Filter
        $filters = $this->filterSessions($request, 'fuse_config', [
            'keyword' => '',
            'product_id' => null,
            'product_group_id' => null,
        ]);

        $list = FuseConfig::query()->with(['productType', 'productGroup', 'product', 'createdUser'])
            ->accessibleBy(auth()->user())
            ->when(!empty($filters['keyword']), function ($q) use ($filters) {
                $q->orWhere('name', 'like', '%' . $filters['keyword'] . '%');
            })->when(!empty($filters['product_id']), function ($q) use ($filters) {
                $q->where('product_id', $filters['product_id']);
            })->when(!empty($filters['product_group_id']), function ($q) use ($filters) {
                $q->where('product_group_id', $filters['product_group_id']);
            })->filterSort($filters)
            ->orderBy('created_at', 'desc')->paginate(config('table.per_page'));

        $products = Product::select('name', 'id')->get();
        $productGroups = ProductGroup::select('name', 'id', 'product_id')->get();

        return Inertia::render('FuseConfig/Index', [
            'header' => FuseConfig::header(),
            'filters' => $filters,
            'list' => $list,
            'products' => $products,
            'productGroups' => $productGroups,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        if (!$request->has('fuse_manager_id')) {
            return Redirect::route('fuse_managers.index')->with('error', 'Please select a Fuse Manager first.');
        }
        return $this->edit(null, $request->fuse_manager_id);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(FuseConfigUpdateRequest $request)
    {
        return $this->update($request, null);
    }

    /**
     * Display the specified resource.
     */
    public function show(FuseConfig $fuseConfig)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(?FuseConfig $fuseConfig = null, $fuse_manager_id = null, bool $createRevision = false,)
    {
        $productTypes = ProductType::select('name', 'id')->get();
        $products = Product::select('name', 'id', 'product_type_id')->get();
        $productGroups = ProductGroup::select('name', 'id', 'product_id')->get();
        $binMatrices = BinMatrix::select(['id', 'name', 'state', 'revision', 'product_group_id'])->get()->append('label');
        $dataTypes = config('settings.fuse_dataTypes');
        $types = config('settings.fuse_types');
        $mapTypes = config('settings.fuse_data_map_types');
        $columns = [];

        if (null === $fuseConfig) {
            $fuseManager = FuseManager::find($fuse_manager_id);

            $data = new FuseConfig;
            $data->fuse_manager_id = $fuse_manager_id;
            $data->fuse_manager = $fuseManager; //Preload
        } else {
            //Clone for new revision 
            if ($createRevision) {
                $data = $fuseConfig->load(['fuseConfigItems', 'binMatrix']);
                //Increase revision
                $data['revision'] = FuseConfig::getLatestRevision($fuseConfig->original_id)?->revision + 1;

                //Reset value as new
                $data['approved'] = false;
                $data['created_user_id'] = null;
                $data['generated_at'] = null;
                foreach ($data->fuseConfigItems as $index => $fuseConfigItem) {
                    $data->fuseConfigItems[$index]['fuse_config_id'] = null;
                }
            } else {
                $data = $fuseConfig->load(['fuseManager', 'fuseConfigItems.fuseConfigItemAddresses', 'createdUser']);
            }

            //Initialize LineDatabase
            if ($data->bin_matrix_id != null && $data->binMatrix != null) {
                $lineDatabase = new LineDatabase($data->binMatrix->speed_batch_id);
                $columns = $lineDatabase->getColumns();
            }
        }

        return Inertia::render('FuseConfig/Edit', [
            'data' => Inertia::always($data),
            'productTypes' => $productTypes,
            'products' => $products,
            'productGroups' => $productGroups,
            'binMatrices' => $binMatrices,
            'dataTypes' => $dataTypes,
            'types' => $types,
            'mapTypes' => $mapTypes,
            'columns' => $columns,
            'createRevision' => $createRevision,
            'expressionHtml' => config('settings.expressionHtml')
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(FuseConfigUpdateRequest $request, ?FuseConfig $fuseConfig = null)
    {
        $data = $request->validated();

        if (null === $fuseConfig) {
            $data['created_user_id'] = auth()->id();

            $fuseConfig = FuseConfig::create($data);
            //Save fuse_config_items and nested fuse_config_item_addresses with their newly created id
            foreach ($data['fuse_config_items'] as $items) {
                $item = $fuseConfig->fuseConfigItems()->create($items);
                $item->fuseConfigItemAddresses()->createMany($items['fuse_config_item_addresses']);
            }

            return Redirect::route('fuse_configs.edit', $fuseConfig->id)->with('message', 'Fuse Config created successfully');
        } else {
            $store_row_ids = [];
            $store_address_row_ids = [];
            foreach ($data['fuse_config_items'] as $item) {
                $item['fuse_config_id'] = $fuseConfig->id;
                $result = $fuseConfig->fuseConfigItems()->updateOrCreate(['id' => $item['id']], $item);

                foreach ($item['fuse_config_item_addresses'] as $address) {
                    $address['fuse_config_item_id'] = $result->id;
                    $address_result = $result->fuseConfigItemAddresses()->updateOrCreate(['id' => $address['id']], $address);

                    //Keep the Address Row ID to use for delete later
                    $store_address_row_ids[] = $address_result->id;
                }
                //Removed Address Rows
                foreach ($fuseConfig->fuseConfigItems()->get() as $fcItem) {
                    $fcItem->fuseConfigItemAddresses()->whereNotIn('id', $store_address_row_ids)->delete();
                }

                //Keep the Row ID to use for delete later
                $store_row_ids[] = $result->id;
            }

            //Delete Removed Rows
            $fuseConfig->fuseConfigItems()->whereNotIn('id', $store_row_ids)->delete();
            $fuseConfig->update($data);


            return Redirect::route('fuse_configs.edit', $fuseConfig->id)->with('message', 'Fuse Config updated successfully');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(FuseConfig $fuseConfig)
    {
        $fuseConfig->fuseConfigItems()->delete();
        $fuseConfig->delete();
        return Redirect::route('fuse_configs.index')->with('message', 'Fuse Config deleted successfully');
    }

    public function postState(Request $request, FuseConfig $fuseConfig)
    {
        $fuseConfig->update(['state' => $request->state, 'state_update_user_id' => auth()->id(), 'state_updated_at' => now()]);
        return response()->json(['message' => 'State updated successfully']);
    }

    public function getRevisionCreate(FuseConfig $fuseConfig)
    {
        return $this->edit($fuseConfig, $fuseConfig->fuse_manager_id, true);
    }

    /**
     * Import
     */
    public function getImport(Request $request)
    {
        if (!$request->has('fuse_manager_id')) {
            return Redirect::route('fuse_managers.index')->with('error', 'Please select a Fuse Manager first to import.');
        }

        $fuseManager = FuseManager::find($request->fuse_manager_id);
        return Inertia::render('FuseConfig/Import', [
            'fuseManager' => $fuseManager
        ]);
    }

    public function postImport(Request $request)
    {
        $request->validate([
            'file' => [
                'required',
                'file'
            ],
            'fuse_manager_id' => [
                'required',
                'numeric'
            ]
        ]);

        try {
            //Store the file to keep a copy
            $file = $request->file('file')->store('fuse_configs', 'public');
            //Get from public disk
            $filePath = Storage::disk('public')->path($file);
            Excel::import(new FuseConfigImport($file, $request->fuse_manager_id), $filePath);
            return Redirect::route('fuse_managers.edit', $request->fuse_manager_id)->with('message', 'Excel imported successfully.');
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            return Redirect::back()->withErrors(excelFailureMessage($e));
        } catch (\Exception $e) {
            return Redirect::route('fuse_configs.import', ['fuse_manager_id' => $request->fuse_manager_id])->withErrors(['errors' => $e->getMessage()]);
        }
    }

    /**
     * Fuse Definition
     */
    public function getFuseDefinition(int $fuse_config_id)
    {
        $service = new GenerateFuseDefinitionTxtService($fuse_config_id);
        return $service->generateTxt();
    }

    public function getSSPEC(int $fuse_config_id)
    {
        $service = new GenerateSspecTxtService($fuse_config_id);
        return $service->generateTxt();
    }

    public function getFuseConfigLineitems(int $fuse_config_id)
    {
        $data = FuseConfigLineitem::where('fuse_config_id', $fuse_config_id)->orderBy('sequence')->get();
        return response()->json(['data' => $data, 'dataTypes' => config('settings.dataTypes')]);
    }

    public function postFuseConfigLineitems(Request $request, int $fuse_config_id)
    {
        $store_row_ids = [];
        foreach ($request->items as $item) {
            $item['fuse_config_id'] = $fuse_config_id;

            $success_id = $item['id'];
            if ($item['id'] == null) {
                $result = FuseConfigLineitem::create($item);
                $success_id = $result->id;
            } else {
                $result = FuseConfigLineitem::where('id', $item['id'])->update($item);
            }
            //Keep the Row ID to use for delete later
            $store_row_ids[] = $success_id;
        }
        //Delete Removed Rows
        FuseConfigLineitem::whereNotIn('id', $store_row_ids)->where('fuse_config_id', $fuse_config_id)->delete();

        return Redirect::route('fuse_configs.edit', $fuse_config_id)->with('message', 'Fuse Config Lineitem updated successfully');
    }
}
