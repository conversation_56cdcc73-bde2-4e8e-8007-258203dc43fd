<script setup>
import { computed, onMounted, onUnmounted, watch } from 'vue';

const props = defineProps({
    id: {
        type: String,
        required: true,
    },
    title: {
        type: String,
        required: true,
    },
    form: {
        type: Object
    },
    buttonYes: { //Button Label
        type: String,
        default: 'Save'
    },
    buttonNo: {
        type: String,
        default: 'Close'
    },
    buttonType: { //Button Style follow Bootstrap alert, warning, etc
        type: String,
        default: 'primary'
    },
    modalClass: {
        type: String,
        default: ''
    },
    style: {
        type: String,
        default: ''
    },
    processing: {
        type: Boolean,
        default: false
    }
});

onMounted(() => document.addEventListener('keydown', closeOnEscape));

onUnmounted(() => {
    document.removeEventListener('keydown', closeOnEscape);
});

const buttonClass = computed(() => 'btn-' + props.buttonType)
const showYesButton = computed(() => props.buttonYes != null && props.buttonYes != '')

const close = () => {
    var myModal = document.getElementById(props.id);
    var modal = bootstrap.Modal.getInstance(myModal)
    modal.hide();
};

const closeOnEscape = (e) => {
    if (e.key === 'Escape' && props.show) {
        close();
    }
};

//Parent can access close() function
defineExpose({ close });
//Callback event to parent
defineEmits(['yesEvent', 'noEvent']);
</script>

<template>
    <teleport to="body">
        <div :id="id" class="modal fade" tabindex="-1" aria-labelledby="vueModalLabel" aria-hidden="true">
            <div class="modal-dialog" :class="modalClass" :style="style">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5" id="vueModalLabel">{{ title }}</h1>
                        <button type="button" class="btn-close" @click="$emit('noEvent')" aria-label="Close"></button>
                    </div>
                    <div class="modal-body overflow-visible">
                        <slot />
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" @click="$emit('noEvent')"
                            data-bs-dismiss="modal" :disabled="processing">{{ buttonNo }}</button>
                        <button type="button" v-if="showYesButton" class="btn" :class="buttonClass"
                            @click="$emit('yesEvent')" :disabled="processing || (form && form.processing)">
                            <span v-if="processing" class="spinner-border spinner-border-sm me-1" role="status"
                                aria-hidden="true"></span>
                            {{ buttonYes }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </teleport>
</template>

<style>
/* Allow dropdowns to overflow the modal */
.modal-body.overflow-visible {
    overflow: visible !important;
}

/* Ensure the modal doesn't clip overflow content */
.modal-content {
    overflow: visible !important;
}

/* Make sure the modal dialog doesn't clip content either */
.modal-dialog {
    overflow: visible !important;
}
</style>
