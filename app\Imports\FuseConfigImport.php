<?php

namespace App\Imports;

use App\Models\FuseConfig;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;

class FuseConfigImport implements ToCollection
{
    protected $filepath;
    protected $fuse_manager_id;

    public function __construct($filepath, $fuse_manager_id)
    {
        $this->filepath = $filepath;
        $this->fuse_manager_id = $fuse_manager_id;
    }

    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        $name = "";

        $temp_register = "";
        $is_address_row = false;
        $items = [];
        foreach ($rows as $index => $row) {
            //First Row is name
            if ($index == 0) $name = $row[1];

            //Header Start from 5th Row, Data Start from 6th Row
            if ($index >= 5) {
                if (!empty($row[0])) {
                    $is_address_row = false;
                    $temp_register = $row[0];
                    $direction = $row[5] == "High to Low" ? 1 : 0;
                    //Create Fuse Config Item 
                    $items[$row[0]] = [
                        'register' => $row[0],
                        'size' => $row[1],
                        'datatype' => $row[2],
                        'pin_to_read' => $row[3],
                        'pin_to_modify' => $row[4],
                        'direction' => $direction
                    ];
                } else if ($row[1] == "Visibility") {
                    //Just a header, next row will be address
                    $is_address_row = true;
                } else if ($is_address_row) {
                    if (!empty($row[1])) { //Visibility is not empty - (Internal)
                        $size = $row[4];

                        $items[$temp_register]['fuse_config_item_addresses'][] = [
                            'setting' => $row[2],
                            'stop_address' => 0,
                            'start_address' => 0,
                            'size' => $size,
                            'fuse_type' => $row[5],
                            'data_map_type' => $row[6],
                            'encoding_type' => $row[7] === 'None' ? null : $row[7],
                            'de_rv_xd' => $row[6] == "Direct" ? null : $row[8] //Only save Use Recommended Value for now
                        ];
                    } else {
                        //Another Inner Header ignore for now
                    }
                }
            }
        }

        // Calculate addresses for all items
        foreach ($items as $key => $item) {
            $item = $this->calculateItemAddresses($item);
            $items[$key] = $item;
        }

        //Start Save Into Database
        $fuseConfig = FuseConfig::create([
            'name' => $name,
            'import' => true,
            'import_file' => $this->filepath,
            'created_user_id' => auth()->id(),
            'fuse_manager_id' => $this->fuse_manager_id
        ]);
        if ($fuseConfig != null) {

            //Save fuse_config_items and nested fuse_config_item_addresses with their newly created id
            foreach ($items as $key => $item) {
                $fuseConfigItem = $fuseConfig->fuseConfigItems()->create($item);
                $fuseConfigItem->fuseConfigItemAddresses()->createMany($item['fuse_config_item_addresses']);
            }
        }

        return true;
    }

    /**
     * Calculate start and stop addresses for a fuse config item
     * 
     * @param array $item Fuse config item with addresses
     * @throws \Exception When validation fails
     * @return array Updated item with calculated addresses
     */
    private function calculateItemAddresses(array $item): array
    {
        if (!isset($item['fuse_config_item_addresses'])) {
            return $item;
        }

        if (!isset($item['size']) || !is_numeric($item['size'])) {
            throw new \Exception("Invalid or missing size for register {$item['register']}");
        }

        $addresses = &$item['fuse_config_item_addresses'];
        $startAddress = 0;
        $lastStopAddress = -1;

        foreach ($addresses as &$address) {
            if (!isset($address['size']) || !is_numeric($address['size'])) {
                throw new \Exception("Invalid or missing size for address in register {$item['register']}");
            }

            $size = intval($address['size']);
            if ($size <= 0) {
                throw new \Exception("Size must be greater than 0 for address in register {$item['register']}");
            }

            // Set start address for current item
            $address['start_address'] = $startAddress;

            // Calculate stop address (start + size - 1)
            $address['stop_address'] = ($startAddress + $size - 1);

            // Validate address continuity
            if ($lastStopAddress >= 0 && $startAddress != $lastStopAddress + 1) {
                throw new \Exception("Address gap detected in register {$item['register']} between addresses {$lastStopAddress} and {$startAddress}");
            }

            $lastStopAddress = $startAddress + $size - 1;
            $startAddress += $size;
        }

        // Validate total size matches the item size
        $totalAddressSize = array_sum(array_column($addresses, 'size'));
        if ($totalAddressSize != $item['size']) {
            throw new \Exception(
                "Total address sizes ({$totalAddressSize}) does not match item size ({$item['size']}) " .
                    "for register {$item['register']}. All addresses must sum up to the register size."
            );
        }

        return $item;
    }
}
