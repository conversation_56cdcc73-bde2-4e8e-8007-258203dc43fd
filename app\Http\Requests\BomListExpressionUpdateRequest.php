<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class BomListExpressionUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'list.*.bin_matrix_item_id' => ['required'],
            'list.*.entries.*.value' => ['required']
        ];
    }
}
