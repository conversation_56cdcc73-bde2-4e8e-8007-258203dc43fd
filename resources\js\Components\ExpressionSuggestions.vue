<script setup>
import { onMounted, ref, computed, watch } from 'vue';

const props = defineProps({
    modelValue: {
        type: String,
        default: '',
        required: false,
    },
    invalid: {
        type: String,
        default: null,
    },
    suggestions: {
        type: Array,
        default: () => [],
        required: true,
    },
    required: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: String,
        default: 'Expression',
    },
});

const emit = defineEmits(['update:modelValue']);

const input = ref(null);
const showSuggestions = ref(false);
const cursorPosition = ref(0);
const selectedIndex = ref(-1);

watch(showSuggestions, newVal => {
    if (!newVal) {
        selectedIndex.value = -1;
    }
});

const searchTerm = computed(() => {
    const value = props.modelValue || '';
    const lastOpenBracket = value.lastIndexOf('[', cursorPosition.value);
    if (lastOpenBracket === -1) return '';

    const term = value.slice(lastOpenBracket + 1, cursorPosition.value);
    return term.toLowerCase();
});

const filteredSuggestions = computed(() => {
    if (!searchTerm.value) return props.suggestions;
    return props.suggestions.filter(suggestion => suggestion.toLowerCase().startsWith(searchTerm.value));
});

const unmatchedBrackets = computed(() => {
    const value = props.modelValue || '';
    const openCount = (value.match(/\(/g) || []).length;
    const closeCount = (value.match(/\)/g) || []).length;
    return openCount !== closeCount;
});

const handleInput = event => {
    const value = event.target.value;
    cursorPosition.value = event.target.selectionStart;
    emit('update:modelValue', value);

    const hasOpenBracket = value.includes('[');

    if (hasOpenBracket) {
        showSuggestions.value = true;
        selectedIndex.value = -1;
    } else {
        showSuggestions.value = false;
    }
};

const handleKeydown = event => {
    if (!showSuggestions.value) return;

    switch (event.key) {
        case 'ArrowDown':
            event.preventDefault();
            if (selectedIndex.value < filteredSuggestions.value.length - 1) {
                selectedIndex.value++;
            } else {
                selectedIndex.value = 0;
            }
            break;

        case 'ArrowUp':
            event.preventDefault();
            if (selectedIndex.value > 0) {
                selectedIndex.value--;
            } else {
                selectedIndex.value = filteredSuggestions.value.length - 1;
            }
            break;

        case 'Enter':
            event.preventDefault();
            if (selectedIndex.value >= 0 && selectedIndex.value < filteredSuggestions.value.length) {
                selectSuggestion(filteredSuggestions.value[selectedIndex.value]);
            }
            break;

        case 'Escape':
            showSuggestions.value = false;
            selectedIndex.value = -1;
            event.preventDefault();
            break;
    }
};

const selectSuggestion = suggestion => {
    const value = props.modelValue || '';
    const lastOpenBracket = value.lastIndexOf('[', cursorPosition.value);
    if (lastOpenBracket === -1) return;

    const newValue = value.slice(0, lastOpenBracket) + '[' + suggestion + ']' + value.slice(cursorPosition.value);

    emit('update:modelValue', newValue);
    showSuggestions.value = false;
    selectedIndex.value = -1;
};

const handleBlur = () => {
    setTimeout(() => {
        showSuggestions.value = false;
        selectedIndex.value = -1;
    }, 200);
};

onMounted(() => {
    if (input.value?.hasAttribute('autofocus')) {
        input.value.focus();
    }
});

defineExpose({ focus: () => input.value?.focus() });
</script>

<template>
    <div class="relative">
        <textarea class="form-control" :class="{ 'is-invalid': invalid || unmatchedBrackets }" :value="modelValue || ''" @input="handleInput" @keydown="handleKeydown" @blur="handleBlur" ref="input" :placeholder="placeholder" :required="required"></textarea>

        <div v-if="unmatchedBrackets" class="invalid-feedback" style="display:block;">
            Brackets do not match.
        </div>

        <div v-show="showSuggestions" class="suggestions-dropdown">
            <!-- <div v-if="filteredSuggestions.length === 0" class="suggestion-item">
                No matches found
            </div> -->
            <div v-for="(suggestion, index) in filteredSuggestions" :key="suggestion" class="suggestion-item" :class="{ selected: index === selectedIndex }" @mousedown="selectSuggestion(suggestion)" @mouseover="selectedIndex = index">
                {{ suggestion }}
            </div>
        </div>
    </div>
</template>

<style scoped>
.relative {
    position: relative;
}

.suggestions-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    background: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    margin-top: 4px;
}

.suggestion-item {
    padding: 8px 12px;
    cursor: pointer;
}

.suggestion-item:hover {
    background-color: #f0f0f0;
}

.suggestion-item.selected {
    background-color: #e0e0e0;
}
</style>
