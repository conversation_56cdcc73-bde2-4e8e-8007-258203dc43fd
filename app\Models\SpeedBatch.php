<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SpeedBatch extends BaseModel
{
    protected $table = 'speed_batch';

    protected $fillable = [
        'name',
        'product_type_id',
        'product_id',
        'product_group_id',
        'state',
        'state_update_user_id',
        'state_updated_at',
        'created_user_id',
        'file_path',
        'revision',
        'is_current',
        'previous_revision_id',
        'original_id',
    ];

    protected $casts = [
        'state_updated_at' => 'datetime',
    ];

    protected $attributes = [
        'is_current' => true,
        'revision' => 0, //default
        'state' => STATE_WIP
    ];

    protected $appends = [
        'generate_view_name',
    ];

    public function createdUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_user_id');
    }

    public function speed(): HasMany
    {
        return $this->hasMany(Speed::class, 'batch_id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function productType(): BelongsTo
    {
        return $this->belongsTo(ProductType::class, 'product_type_id');
    }

    public function productGroup(): BelongsTo
    {
        return $this->belongsTo(ProductGroup::class, 'product_group_id');
    }

    public function stateUpdateUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'state_update_user_id');
    }

    public function lineitemManagers(): HasMany
    {
        return $this->hasMany(LineitemManager::class, 'speed_batch_id');
    }

    public function logs(): HasMany
    {
        return $this->hasMany(GenerateBatchLog::class, 'speed_batch_id');
    }


    //Mutator & Accessor
    public function generateBasedName(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => isset($attributes['id']) ? ("speed_" . $attributes['id']) : null
        );
    }

    public function generateViewName(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => isset($attributes['product_type_id']) && $attributes['product_type_id'] != null ?
                "speed_"
                . $attributes['id'] . "_"
                . $attributes['product_type_id'] . "_"
                . $attributes['product_id'] . "_"
                . $attributes['product_group_id'] . "_"
                . $attributes['revision']
                : null
        );
    }

    public function label(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => $attributes['name'] . " (" . $attributes['revision'] . " - " . $attributes['state'] . ")",
        );
    }

    public function isSnapshot(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => $attributes['state'] == STATE_SNAPSHOT
        );
    }

    public function isGenerating(): Attribute
    {
        $log = $this->logs()->isGenerating($this->id)->first();
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => $log != null
        );
    }

    /*
    * Functions
    */
    public function setState($state): self
    {
        //Set other is_current to false
        if ($state == STATE_SNAPSHOT) {
            self::where('original_id', $this->original_id)
                ->where('is_current', true)->update(['is_current' => false]);
        }

        $this->update([
            'is_current' => true,
            'state' => $state,
            'state_update_user_id' => auth()->id(),
            'state_updated_at' => now()
        ]);

        return $this;
    }

    public function dropGenerateViewTable(): self
    {
        $viewName = $this->generate_view_name;
        \DB::statement("DROP MATERIALIZED VIEW IF EXISTS \"{$viewName}\"");
        return $this;
    }

    public function getBasedModal(): SpeedDynamic
    {
        $baseModal = new SpeedDynamic;
        $baseModal->setTable($this->generate_based_name);
        return $baseModal;
    }

    public function getViewModal(): SpeedDynamic
    {
        $baseModal = new SpeedDynamic;
        $baseModal->setTable($this->generate_view_name);
        return $baseModal;
    }

    /*
    * Build Table Header
    */
    public static function header()
    {
        $headers = [];
        return array_merge($headers, [
            ['field' => 'name', 'title' => 'Name', 'sortable' => true],
            ['field' => 'state', 'title' => 'State', 'sortable' => true],
            ['field' => 'product_type_id', 'title' => 'Product Type', 'sortable' => false],
            ['field' => 'product_id', 'title' => 'Product Name', 'sortable' => false],
            ['field' => 'product_group_id', 'title' => 'Product Group', 'sortable' => false],
            ['field' => 'revision', 'title' => 'Revision', 'sortable' => true],
            ['field' => 'created_user_id', 'title' => 'Created By'],
            ['field' => 'created_at', 'title' => 'Created At', 'sortable' => true],
        ]);
    }

    public static function states()
    {
        return [STATE_SNAPSHOT];
    }
}
