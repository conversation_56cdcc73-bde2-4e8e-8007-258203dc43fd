<?php

namespace App\Services;

class CrossShipAliasResolver
{
    private array $raw;
    private array $aliasMap = [];
    private array $outputResult = [];

    public function __construct(array $crossShipRows)
    {
        $this->raw = $crossShipRows;
        $this->aliasMap = [];
        $this->outputResult = [];
        $this->process();
    }

    private function process(): void
    {
        $len = count($this->raw);
        for ($i = 0; $i < $len; $i += 2) {
            if (!isset($this->raw[$i + 1])) {
                break;
            }

            // split and trim to avoid whitespace issues
            $group = array_map('trim', explode(',', $this->raw[$i]));
            $sub = array_map('trim', explode(',', $this->raw[$i + 1]));

            // map each item to its original index
            $pos = [];
            foreach ($group as $idx => $val) {
                $pos[$val] = $idx;
            }

            // leftover items
            $left = array_values(array_diff($group, $sub));

            // subset pivot: last by original order
            usort($sub, function ($a, $b) use ($pos) {
                $ipa = $pos[$a] ?? PHP_INT_MAX;
                $ipb = $pos[$b] ?? PHP_INT_MAX;
                return $ipa <=> $ipb;
            });
            $subKey = end($sub);

            // leftover pivot: second if exists, else first
            usort($left, function ($a, $b) use ($pos) {
                $ipa = $pos[$a] ?? PHP_INT_MAX;
                $ipb = $pos[$b] ?? PHP_INT_MAX;
                return $ipa <=> $ipb;
            });
            $leftKey = count($left) > 1 ? $left[1] : ($left[0] ?? null);

            // alias entries: only when both subset and leftover have multiple items
            if (count($sub) > 1 && count($left) > 1) {
                $this->aliasMap[$subKey] = array_values(array_diff($sub, [$subKey]));
                $this->aliasMap[$leftKey] = array_values(array_diff($left, [$leftKey]));
            }

            // output result pairs
            if ($leftKey !== null) {
                $this->outputResult[] = [$subKey, $leftKey];
                $this->outputResult[] = [$subKey];
            } else {
                // special when subset covers all: pivot primary is first sub element
                $pivotPrimary = $sub[0];
                $pivotSecondary = $sub[1] ?? null;
                $this->outputResult[] = [$pivotPrimary, $pivotSecondary];
                $this->outputResult[] = [$pivotPrimary];
            }
        }
    }

    public function getAliasMap(): array
    {
        ksort($this->aliasMap);
        return $this->aliasMap;
    }

    public function getCrossShipResult(): array
    {
        return $this->outputResult;
    }

    /**
     * Return sorted list of unique pivot SSPEC (alias and leftover pivot keys).
     *
     * @return string[]
     */
    public function getPivotSSPEC(): array
    {
        $sspec = [];
        foreach ($this->outputResult as $pair) {
            foreach ($pair as $code) {
                if ($code !== null && $code !== '') {
                    $sspec[] = $code;
                }
            }
        }
        $unique = array_unique($sspec);
        sort($unique, SORT_STRING);
        return array_values($unique);
    }
}
