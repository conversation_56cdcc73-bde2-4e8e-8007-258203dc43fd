<script setup>
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import HeadRow from '@/Components/Table/HeadRow.vue';
import Paginate from '@/Components/Table/Paginate.vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import ProductFilters from '@/Components/ProductFilters.vue';
import { Head, router, useForm, Link } from '@inertiajs/vue3';
import { ref } from 'vue';
import { formatDate } from '@/helper';

const props = defineProps({
    header: {
        type: Object,
    },
    filters: {
        type: Object,
    },
    list: {
        type: Object,
        default: () => ({}),
    },
    products: {
        type: Array,
    },
    productGroups: {
        type: Array,
    }
});

const routeGroupName = 'fuse_managers';
const headerTitle = ref('Fuse Managers');
const form = useForm(props.filters);

const sort = field => {
    form.sort.field = field;
    form.sort.direction = form.sort.direction == '' || form.sort.direction == 'desc' ? 'asc' : 'desc';
    submit();
};

const submit = () => {
    form.get(route(routeGroupName + '.index'), {
        preserveScroll: true,
    });
};

const clearFilters = () => {
    form.keyword = '';
    form.product_id = null;
    form.product_group_id = null;
    submit();
}

const destroy = (id, name) => {
    const c = confirm(`Delete this Fuse Manager ${name} ?`);
    if (c) {
        router.delete(route(routeGroupName + '.destroy', id));
    }
};
</script>

<template>

    <Head :title="headerTitle" />

    <AuthenticatedLayout>
        <template #header>
            {{ headerTitle }}
        </template>

        <template #h-buttons>
            <Link class="btn btn-primary" :href="route(routeGroupName + '.create')">
            <i class="bi bi-plus"></i>
            Create
            </Link>

        </template>

        <div class="my-3 p-3 bg-body rounded shadow-sm">
            <form @submit.prevent="submit">
                <div class="row mb-3 align-items-center g-3">
                    <div class="col-md-2">
                        <div class="form-floating">
                            <input v-model="form.keyword" type="text" class="form-control" id="keywordInput"
                                placeholder="Keyword" autocomplete="off" />
                            <label for="keywordInput">Keyword</label>
                        </div>
                    </div>
                    <ProductFilters v-model="form" :products="products" :productGroups="productGroups" />
                    <div class="col-md-4">
                        <PrimaryButton type="submit" class="mx-2" :disabled="form.processing">
                            <i class="bi bi-search"></i>
                            Search
                        </PrimaryButton>
                        <SecondaryButton type="button" @click="clearFilters" :disabled="form.processing">
                            <i class="bi bi-x-octagon"></i>
                            Clear
                        </SecondaryButton>
                    </div>
                </div>
            </form>

            <div class="table-responsive-md">
                <table class="table table-bordered table-striped table-hover">
                    <thead>
                        <tr>
                            <HeadRow>Actions</HeadRow>
                            <HeadRow v-for="head in header" :field="head.field"
                                :sort="head.sortable ? filters.sort : null" @sortEvent="sort"
                                :disabled="form.processing">{{ head.title }}</HeadRow>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(item, index) in list.data">
                            <td width="10%">
                                <Link :href="route(routeGroupName + '.edit', item.id)" class="btn btn-sm btn-link">
                                <i class="bi bi-pencil"></i>
                                </Link>
                                <button @click="destroy(item.id, item.name)" class="btn btn-sm btn-link">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </td>
                            <td>{{ item.name }}</td>
                            <td>{{ item.state }}</td>
                            <!-- <td>{{ item.product?.name ?? '' }}</td>
                            <td>{{ item.product_group?.name ?? '' }}</td> -->
                            <td>{{ item.revision }}</td>
                            <td>{{ item.created_user?.name }}</td>
                            <td>{{ formatDate(item.created_at) }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <Paginate :data="list" />
        </div>
    </AuthenticatedLayout>
</template>
