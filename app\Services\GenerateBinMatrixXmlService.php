<?php

namespace App\Services;

use App\Models\BinMatrix;
use App\Models\BinMatrixFlowAttribute;

class GenerateBinMatrixXmlService
{
    public $bin_matrix_id;
    protected $bm;
    protected $flowAttributes;
    protected $columnsWithDataType;

    public function __construct(int $bin_matrix_id)
    {
        $this->bin_matrix_id = $bin_matrix_id;

        // Load the BinMatrix with all necessary relationships
        $this->bm = BinMatrix::with([
            'binMatrixItems.binMatrixFlows.flowAttributeValues.binMatrixFlowAttribute',
            'product',
            'productGroup',
            'createdUser'
        ])->find($bin_matrix_id);

        // Get all flow attributes for this bin matrix
        $this->flowAttributes = BinMatrixFlowAttribute::where('bin_matrix_id', $bin_matrix_id)
            ->orderBy('sequence')
            ->get()
            ->keyBy('id');
    }

    /**
     * Generate the BinMatrix XML file
     *
     * @return string The XML document as a string
     */
    public function generateStructureXML()
    {
        if (!$this->bm) {
            throw new \Exception("BinMatrix with ID {$this->bin_matrix_id} not found");
        }

        // Create a new XML document
        $dom = new \DOMDocument('1.0', 'utf-8');
        $dom->formatOutput = true;

        // Create the root element
        $rootElement = $dom->createElement('BinMatrix');
        $rootElement->setAttribute('xmlns:xsi', 'http://www.w3.org/2001/XMLSchema-instance');
        $rootElement->setAttribute('xsi:noNamespaceSchemaLocation', 'GEN_BinMatrixXml_tt.xsd');
        $rootElement->setAttribute('name', $this->bm->name);
        $dom->appendChild($rootElement);

        // Add BinMatrixRevision and ProductFamily elements
        $revisionElement = $dom->createElement('BinMatrixRevision', $this->bm->revision);
        $rootElement->appendChild($revisionElement);

        $productFamilyElement = $dom->createElement('ProductFamily', $this->bm->product->name);
        $rootElement->appendChild($productFamilyElement);

        // Add SupportedProduct element
        $supportedProductElement = $dom->createElement('SupportedProduct');
        $productElement = $dom->createElement('Product');
        $productElement->setAttribute('Name', $this->bm->productGroup->name);
        $productElement->setAttribute('ProductID', $this->bm->productGroup->id);
        $supportedProductElement->appendChild($productElement);
        $rootElement->appendChild($supportedProductElement);

        // Add BOMGroupTable element
        $bomGroupTableElement = $dom->createElement('BOMGroupTable');
        $rootElement->appendChild($bomGroupTableElement);

        // Process each BinMatrixItem (BOMGroup)
        foreach ($this->bm->binMatrixItems as $binMatrixItem) {
            $this->addBomGroup($dom, $bomGroupTableElement, $binMatrixItem);
        }

        return $dom->saveXML();
    }

    /**
     * Add a BOMGroup element to the XML
     */
    private function addBomGroup($dom, $bomGroupTableElement, $binMatrixItem)
    {
        // Create BOMGroup element
        $bomGroupElement = $dom->createElement('BOMGroup');
        $bomGroupElement->setAttribute('name', $binMatrixItem->bom_group);
        $bomGroupTableElement->appendChild($bomGroupElement);

        // Add BOMList element
        $bomListElement = $dom->createElement('BOMList');
        $bomGroupElement->appendChild($bomListElement);

        // Add each BOM to the BOMList
        $bomList = explode(',', str_replace(' ', '', $binMatrixItem->bom_list));
        foreach ($bomList as $bom) {
            if (!empty($bom)) {
                $bomElement = $dom->createElement('BOM', $bom);
                $bomListElement->appendChild($bomElement);
            }
        }

        // Add ActiveFlowList element
        $activeFlowListElement = $dom->createElement('ActiveFlowList');
        $bomGroupElement->appendChild($activeFlowListElement);

        // Add each Flow to the ActiveFlowList
        foreach ($binMatrixItem->binMatrixFlows as $flow) {
            if (!$flow->in_bm) continue;
            $this->addFlow($dom, $activeFlowListElement, $flow, $bomList);
        }

        // Add InactiveFlowList element (empty in this example)
        $inactiveFlowListElement = $dom->createElement('InactiveFlowList');
        $bomGroupElement->appendChild($inactiveFlowListElement);

        // Add each Flow to the ActiveFlowList
        foreach ($binMatrixItem->binMatrixFlows as $flow) {
            if ($flow->in_bm) continue;
            $this->addFlow($dom, $inactiveFlowListElement, $flow, $bomList);
        }

        $line = new LineDatabase($this->bm->speed_batch_id);
        $this->columnsWithDataType = $line->getColumnsWithDataType();
    }

    /**
     * Add a Flow element to the XML
     */
    private function addFlow($dom, $activeFlowListElement, $flow, $bomList)
    {
        // Create Flow element
        $flowElement = $dom->createElement('Flow');
        $flowElement->setAttribute('index', $flow->flow);
        $flowElement->setAttribute('bin', $flow->pass_bin);
        $flowElement->setAttribute('binName', 'p' . $flow->pass_bin);
        $flowElement->setAttribute('LIID', !empty($flow->mm) ? 'F' . $flow->mm : '');
        $activeFlowListElement->appendChild($flowElement);

        // Add olb_bin attribute
        $olbBinElement = $dom->createElement('Attribute');
        $olbBinElement->setAttribute('name', 'olb_bin');
        $olbBinElement->setAttribute('dataType', 'Integer');
        $olbBinElement->setAttribute('unit', '');
        $olbBinElement->setAttribute('multiplier', '');
        $olbBinElement->appendChild($dom->createTextNode($flow->olb_bin));
        $flowElement->appendChild($olbBinElement);

        // Add all flow attribute values
        $attributeValues = [];
        foreach ($flow->flowAttributeValues as $attributeValue) {
            if (isset($this->flowAttributes[$attributeValue->bin_matrix_flow_attribute_id])) {
                $attribute = $this->flowAttributes[$attributeValue->bin_matrix_flow_attribute_id];

                // Create Attribute element
                $attributeElement = $dom->createElement('Attribute');
                $attributeElement->setAttribute('name', $attribute->name);

                // Determine data type based on the value
                $dataType = $this->convertColumnDataType($attributeValue->name);
                $attributeElement->setAttribute('dataType', $dataType);
                $attributeElement->setAttribute('unit', '');
                $attributeElement->setAttribute('multiplier', '');
                $attributeElement->appendChild($dom->createTextNode($attributeValue->value));
                $flowElement->appendChild($attributeElement);

                // Store for later use in AttributePerBOM
                $attributeValues[$attribute->name] = $attributeValue->value;
            }
        }

        // Add CrossShip attribute
        $crossShipElement = $dom->createElement('Attribute');
        $crossShipElement->setAttribute('name', 'CrossShip');
        $crossShipElement->setAttribute('dataType', 'String');
        $crossShipElement->setAttribute('unit', '');
        $crossShipElement->setAttribute('multiplier', '');
        $crossShipElement->appendChild($dom->createTextNode($flow->cross_ship));
        $flowElement->appendChild($crossShipElement);

        // Add AttributePerBOM elements for Package Type and Sample/Production Type
        $this->addAttributePerBOM($dom, $flowElement, 'Package Type', $flow->package_type, $bomList);
        $this->addAttributePerBOM($dom, $flowElement, 'Sample/Production Type', $flow->production_type, $bomList);
    }

    /**
     * Add an AttributePerBOM element to the XML
     */
    private function addAttributePerBOM($dom, $flowElement, $attributeName, $value, $bomList)
    {
        $attributePerBOMElement = $dom->createElement('AttributePerBOM');
        $attributePerBOMElement->setAttribute('name', $attributeName);
        $attributePerBOMElement->setAttribute('dataType', 'String');
        $attributePerBOMElement->setAttribute('unit', '');
        $attributePerBOMElement->setAttribute('multiplier', '');
        $flowElement->appendChild($attributePerBOMElement);

        // Add a BOM element for each BOM in the list
        foreach ($bomList as $bom) {
            if (!empty($bom)) {
                $bomElement = $dom->createElement('BOM', $value);
                $bomElement->setAttribute('name', $bom);
                $attributePerBOMElement->appendChild($bomElement);
            }
        }
    }

    /**
     * Determine the data type of a value
     */
    private function determineDataType($value)
    {
        if (is_numeric($value)) {
            if (strpos($value, '.') !== false) {
                return 'Float/Exponential';
            }
            return 'Integer';
        }
        return 'String';
    }

    /**
     * Save the BinMatrix XML to a file
     *
     * @param string $path Optional path to save the file. If null, will use storage_path.
     * @return string The full path to the saved file
     */
    public function saveXMLToFile($path = null)
    {
        $xml = $this->generateStructureXML();

        if ($path === null) {
            $fileName = $this->bm->name . ' (' . $this->bm->revision . ' - ' . $this->bm->state . ')_' . date('Ymd_His') . '.xml';
            $path = storage_path('app/exports/binmatrix/' . $this->bm->id . '/' . $fileName);

            // Create directory if it doesn't exist
            if (!file_exists(dirname($path))) {
                mkdir(dirname($path), 0755, true);
            }
        }

        file_put_contents($path, $xml);

        return $path;
    }

    private function convertColumnDataType($column)
    {
        if (isset($this->columnsWithDataType[$column])) {
            return convertDataType($this->columnsWithDataType[$column]);
        }
        return "String";
    }
}
