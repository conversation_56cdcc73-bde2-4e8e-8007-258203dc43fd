<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->loadHelpers();
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        if (env('DB_QUERY', false)) {
            \DB::enableQueryLog();
            $this->app->terminating(function () {
                $total_time = 0;
                $total_query = 0;
                foreach (\DB::getQueryLog() as $log) {
                    $total_query++;
                    $total_time += $log['time'];
                    \Log::channel('query')->info($log);
                }
                \Log::channel('query')->info("Total Query:" . $total_query . " Seconds: " . $total_time / 1000);
            });
        }
    }

    protected function loadHelpers()
    {
        foreach (glob(__DIR__ . '/../Helpers/*.php') as $filename) {
            require_once $filename;
        }
    }
}
