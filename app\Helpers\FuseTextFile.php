<?php

namespace App\Helpers;

use App\Models\FuseConfig;

class FuseTextFile
{
    public static function header(FuseConfig $fuse)
    {
        // Generate file content
        $content = "#LIPORT Fuse Files\n";
        $content .= "#Fuse File Release: {$fuse->name}\n";
        $content .= "#Fuse Configuration(s): {$fuse->name}\n";
        $content .= "#Bin Matrix: \n";
        $content .= "#Supported Test Program(s): \n";
        $content .= "#File ID: \n";
        $content .= "#Program: \n";
        $content .= "#Revision: {$fuse->revision}\n";
        $content .= "#Created By: {$fuse->createdUser->name}\n";
        $content .= "#Created Date/Time (UTC): {$fuse->created_at}\n\n";

        return $content;
    }
}
