<script setup>
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, useForm, Link } from '@inertiajs/vue3';
import { ref, computed, watch, onMounted } from 'vue';
import ExpressionModal from '@/Components/ExpressionModal.vue';
import FlashAlert from '@/Components/FlashAlert.vue';


const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    newColumns: {
        type: Array,
        default: () => ([]),
    },
    compareResult: {
        type: Object,
        default: () => null,
    }
});

const routeGroupName = 'fuse_configs';
const headerTitle = ref('Import Fuse Config');

const storeForm = useForm({
    file: null,
});

const activeImportTab = computed(() => {
    //check is props.newColumns is not empty
    return props.newColumns.length == 0 && props.compareResult == null
})

</script>

<template>

    <Head :title="headerTitle" />

    <AuthenticatedLayout>
        <FlashAlert v-if="Object.keys($page.props.errors).length != 0" @close="$page.props.errors = {}"
            :status="'danger'">
            <ul>
                <li v-for="(message, field) in $page.props.errors">
                    {{ message }}
                </li>
            </ul>
        </FlashAlert>

        <template #header> {{ headerTitle }} </template>
        <span v-if="props.createRevision">(New Revision {{ props.data.revision }})</span>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <ul class="nav nav-tabs card-header-tabs">
                    <li class="nav-item">
                        <a class="nav-link" :class="{ 'active': activeImportTab }" data-bs-toggle="tab"
                            href="#tab_1">Import</a>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content">
                    <div class="tab-pane fade pt-10" :class="{ 'show': activeImportTab, 'active': activeImportTab }"
                        id="tab_1" role="tabpanel" aria-labelledby="tab_1">
                        <form
                            @submit.prevent="storeForm.post(route(routeGroupName + '.import'), { preserveState: false })">
                            <div class="row g-3 mb-2">
                                <div class="col-md-4">
                                    <InputLabel for="file_input" value="Excel File" class="sr-only" />
                                    <input ref="inputFile" type="file" id="file_input"
                                        @input="storeForm.file = $event.target.files[0]"
                                        :class="{ 'is-invalid': storeForm.errors.file }" class="form-control"
                                        accept=".xls,.xlsx" />
                                    <InputError :message="storeForm.errors.file" class="mt-2" />
                                </div>

                            </div>
                            <PrimaryButton type="submit" :disabled="storeForm.processing">
                                Import
                            </PrimaryButton>
                        </form>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex">
                    <div class="me-auto">
                        <Link class="btn btn-secondary me-2" :href="route(routeGroupName + '.index')">Back
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>
