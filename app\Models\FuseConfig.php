<?php

namespace App\Models;

use App\Traits\RevisionTrait;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class FuseConfig extends BaseModel
{
    use SoftDeletes;
    use RevisionTrait;

    public $fillable = [
        'name',
        'state',
        'created_user_id',
        'active',
        'import',
        'import_file',
        'fuse_manager_id'
    ];

    protected $attributes = [
        'active' => true,
        //'revision' => 0, //default
        //'state' => STATE_WIP
    ];

    public function fuseManager(): BelongsTo
    {
        return $this->belongsTo(FuseManager::class, 'fuse_manager_id');
    }

    public function createdUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_user_id');
    }

    public function fuseConfigItems(): HasMany
    {
        return $this->hasMany(FuseConfigItem::class, 'fuse_config_id')->orderBy('sequence');
    }

    public function fuseConfigLineitems(): HasMany
    {
        return $this->hasMany(FuseConfigLineitem::class, 'fuse_config_id')->orderBy('sequence');
    }

    /**
     * Get the USMs that use this fuse config.
     */
    public function usms(): BelongsToMany
    {
        return $this->belongsToMany(USM::class, 'fuse_config_usm', 'fuse_config_id', 'usm_id')
            ->withTimestamps();
    }

    //Mutator & Accessor

    //Scope function Below

    //Static Function Belows
    public static function header()
    {
        $headers = [];
        return array_merge($headers, [
            ['field' => 'name', 'title' => 'Name', 'sortable' => false],
            ['field' => 'state', 'title' => 'State', 'sortable' => false],
            // ['field' => '', 'title' => 'Product Code Name', 'sortable' => false],
            // ['field' => '', 'title' => 'Product Group', 'sortable' => false],
            ['field' => 'revision', 'title' => 'Revision', 'sortable' => true],
            ['field' => '', 'title' => 'Created By'],
            ['field' => 'created_at', 'title' => 'Created At'],
        ]);
    }
}
