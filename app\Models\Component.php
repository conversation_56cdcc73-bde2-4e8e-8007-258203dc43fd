<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Component extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'component_type_id',
        'notes',
        'component_level_id',
        'level',
        'parent_id',
        'usm_id',
    ];

    /**
     * Get the component level that this component belongs to.
     */
    public function componentLevel(): BelongsTo
    {
        return $this->belongsTo(ComponentLevel::class);
    }

    /**
     * Get the component type that this component belongs to.
     */
    public function componentType(): BelongsTo
    {
        return $this->belongsTo(ComponentType::class);
    }

    /**
     * Get the parent component of this component.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Component::class, 'parent_id');
    }

    /**
     * Get the immediate children components of this component.
     */
    public function children(): Has<PERSON>any
    {
        return $this->hasMany(Component::class, 'parent_id');
    }

    public function usmScenarios()
    {
        return $this->belongsToMany(USMScenario::class, 'component_usm_module_usm_scenario', 'component_id', 'usm_scenario_id')
            ->withPivot('usm_module_id');
    }

    /**
     * Get all ancestors (parents, grandparents, etc.) of this component.
     */
    public function ancestors()
    {
        return $this->parent()->with('ancestors');
    }

    /**
     * Get all descendants (children, grandchildren, etc.) of this component.
     */
    public function descendants()
    {
        return $this->children()->with('descendants');
    }

    /**
     * Get all descendants as a flat collection.
     */
    public function getAllDescendants()
    {
        $descendants = collect();

        foreach ($this->children as $child) {
            $descendants->push($child);
            $descendants = $descendants->merge($child->getAllDescendants());
        }

        return $descendants;
    }

    /**
     * Get components at the same level.
     */
    public function scopeSameLevel($query, $level)
    {
        return $query->where('level', $level);
    }

    /**
     * Get siblings (components with the same parent).
     */
    public function siblings()
    {
        if ($this->parent_id) {
            return $this->parent->children()->where('id', '!=', $this->id);
        }

        return self::whereNull('parent_id')
            ->where('id', '!=', $this->id)
            ->where('level', $this->level);
    }
}
