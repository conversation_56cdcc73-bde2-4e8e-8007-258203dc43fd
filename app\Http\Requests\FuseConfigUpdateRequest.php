<?php

namespace App\Http\Requests;

use App\Models\FuseConfig;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class FuseConfigUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [];
        return  array_merge($rules, [
            'name' => ['required', 'string', 'max:255'],
            'fuse_manager_id' => ['required', 'numeric', 'max:255'],
            'fuse_config_items' => ['array'],
        ]);
    }
}
