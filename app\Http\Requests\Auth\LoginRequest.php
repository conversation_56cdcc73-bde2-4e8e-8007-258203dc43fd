<?php

namespace App\Http\Requests\Auth;

use App\Ldap\LdapUser;
use App\Models\User;
use Illuminate\Auth\Events\Lockout;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use LdapRecord\Container;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        $rules = [];
        if (env(LOGIN_USERNAME, false)) {
            $rules['username'] = ['required', 'string'];
        } else {
            $rules['email'] =  ['required', 'string', 'email'];
        }
        $rules['password'] = ['required', 'string'];
        return $rules;
    }

    /**
     * Attempt to authenticate the request's credentials.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function authenticate(): void
    {
        $this->ensureIsNotRateLimited();

        $data = $this->only(env(LOGIN_USERNAME, false) ? 'username' : 'email', 'password');
        $data['active'] = true; //Active True

        if (env('LDAP_ENABLE', false)) {
            $connection = Container::getConnection('default');
            try {
                $ldapUser = LdapUser::findByOrFail('uid', $data['username']);
            } catch (\Exception $e) {
                throw ValidationException::withMessages([
                    'email' => "LDAP User Error: " . $e->getMessage(),
                    'username' => "LDAP User Error: " . $e->getMessage(),
                ]);
            }
            if ($connection->auth()->attempt($ldapUser->getDn(), $data['password'])) {
                // Credentials are valid!
                $user = User::where('username', $data['username'])->first();

                //Create new User, if not exist
                if ($user == null) {
                    $user = User::create([
                        'name' => $ldapUser->getFirstAttribute('cn'),
                        'username' => $data['username'],
                        'is_ldap' => true
                    ]);
                }
                Auth::login($user, $this->boolean('remember'));
            } else {
                $message = $connection->getLdapConnection()->getDiagnosticMessage();
                if (empty($message)) {
                    $message = "Auth Failed.";
                }
                throw ValidationException::withMessages([
                    'email' => "LDAP Auth Error: " . $message,
                    'username' => "LDAP Auth Error: " . $message,
                ]);
            }
        } else {
            //Default Login
            if (!Auth::attempt($data, $this->boolean('remember'))) {
                RateLimiter::hit($this->throttleKey());

                throw ValidationException::withMessages([
                    'email' => trans('auth.failed'),
                    'username' => trans('auth.failed'),
                ]);
            }
        }

        RateLimiter::clear($this->throttleKey());
    }

    /**
     * Ensure the login request is not rate limited.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function ensureIsNotRateLimited(): void
    {
        if (!RateLimiter::tooManyAttempts($this->throttleKey(), 5)) {
            return;
        }

        event(new Lockout($this));

        $seconds = RateLimiter::availableIn($this->throttleKey());

        throw ValidationException::withMessages([
            'email' => trans('auth.throttle', [
                'seconds' => $seconds,
                'minutes' => ceil($seconds / 60),
            ]),
        ]);
    }

    /**
     * Get the rate limiting throttle key for the request.
     */
    public function throttleKey(): string
    {
        return Str::transliterate(Str::lower($this->input('email')) . '|' . $this->ip());
    }
}
