<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import FlashAlert from '@/Components/FlashAlert.vue';
import { Head, Link } from '@inertiajs/vue3';
import { ref } from 'vue';
import { formatDate } from '@/helper';

const props = defineProps({
    data: {
        type: Object,
        required: true,
    },
    detail: {
        type: Object,
        default: null,
    },
    ioParams: {
        type: Object,
        default: null,
    },
});

const headerTitle = ref('Component Type Details');
const routeGroupName = 'component_types';
</script>

<template>
    <Head :title="headerTitle" />

    <AuthenticatedLayout>
        <template #header>
            {{ headerTitle }}
        </template>

        <template #h-buttons> </template>

        <div class="my-3 p-3 bg-body rounded shadow-sm">
            <!-- Component Type Details -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Basic Information</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Name:</div>
                        <div class="col-md-9">{{ data.name }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Level:</div>
                        <div class="col-md-9">{{ data.level }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Type:</div>
                        <div class="col-md-9">{{ data.type }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Source:</div>
                        <div class="col-md-9">{{ data.source }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Scenario Enabled:</div>
                        <div class="col-md-9">
                            <span v-if="data.scenario_enabled" class="badge bg-success">Yes</span>
                            <span v-else class="badge bg-danger">No</span>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Created At:</div>
                        <div class="col-md-9">{{ formatDate(data.created_at) }}</div>
                    </div>
                </div>
            </div>

            <!-- XML Representation -->
            <div class="card" v-if="data.sub_elements">
                <div class="card-header">
                    <h5 class="mb-0">XML Structure</h5>
                </div>
                <div class="card-body">
                    <pre class="bg-light p-3 rounded"><code>{{ data.xml_sub_elements }}</code></pre>
                </div>
            </div>

            <div class="card mt-4" v-else>
                <div class="card-body">
                    <div class="alert alert-info mb-0">
                        <i class="bi bi-info-circle me-2"></i>
                        No XML sub-elements data available for this component type.
                    </div>
                </div>
            </div>

            <!-- Detail Component Type XML -->
            <div class="card mt-4" v-if="detail">
                <div class="card-header">
                    <h5 class="mb-0">Component Type XML Detail</h5>
                </div>
                <div class="card-body">
                    <pre class="bg-light p-3 rounded xml-content"><code>{{ detail.xml_content }}</code></pre>
                </div>
            </div>

            <!-- IOParams -->
            <div class="card mt-4" v-if="ioParams">
                <div class="card-header">
                    <h5 class="mb-0">IO Parameters</h5>
                </div>
                <div class="card-body">
                    <!-- Non-Scenario Attributes -->
                    <div v-if="ioParams.NonScenarioAttributes && ioParams.NonScenarioAttributes.length > 0" class="mb-4">
                        <h6 class="mb-3">Non-Scenario Attributes</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead class="table-light">
                                    <tr>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>Type</th>
                                        <th>Value</th>
                                        <th>Units</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(attr, index) in ioParams.NonScenarioAttributes" :key="index">
                                        <td>{{ attr.name }}</td>
                                        <td>{{ attr.description }}</td>
                                        <td>{{ attr.type }}</td>
                                        <td>{{ attr.value }}</td>
                                        <td>{{ attr.units }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Scenario Attributes -->
                    <div v-if="ioParams.ScenarioAttributes && ioParams.ScenarioAttributes.length > 0">
                        <h6 class="mb-3">Scenario Attributes</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead class="table-light">
                                    <tr>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>Type</th>
                                        <th>Value</th>
                                        <th>Units</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(attr, index) in ioParams.ScenarioAttributes" :key="index">
                                        <td>{{ attr.name }}</td>
                                        <td>{{ attr.description }}</td>
                                        <td>{{ attr.type }}</td>
                                        <td>{{ attr.value }}</td>
                                        <td>{{ attr.units }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- No IO Parameters Available -->
                    <div v-if="(!ioParams.NonScenarioAttributes || ioParams.NonScenarioAttributes.length === 0) && (!ioParams.ScenarioAttributes || ioParams.ScenarioAttributes.length === 0)" class="alert alert-info mb-0">
                        <i class="bi bi-info-circle me-2"></i>
                        No IO Parameters available for this component type.
                    </div>
                </div>
            </div>
        </div>
        <Link class="btn btn-secondary" :href="route(routeGroupName + '.index')"> Back </Link>
    </AuthenticatedLayout>
</template>

<style scoped>
.xml-content {
    max-height: 500px;
    overflow-y: auto;
}
</style>
