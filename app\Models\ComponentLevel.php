<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ComponentLevel extends Model
{
    use HasFactory;

    protected $fillable = [
        'level',
        'name',
        'usm_id',
    ];

    /**
     * Get the components associated with this level.
     */
    public function components(): HasMany
    {
        return $this->hasMany(Component::class);
    }
}
