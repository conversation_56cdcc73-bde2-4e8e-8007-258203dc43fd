<script setup>
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import Checkbox from '@/Components/Checkbox.vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, useForm, Link } from '@inertiajs/vue3';
import { ref, computed, watch, onMounted, onBeforeUnmount, reactive } from 'vue';
import Select from '@/Components/Select.vue';
import axios from 'axios';
import DynamicTable from '@/Components/DynamicTable.vue';
import LineitemCompare from '@/Components/LineitemCompare.vue';
import ExpressionModal from '@/Components/ExpressionModal.vue';
import ExpressionSuggestions from '@/Components/ExpressionSuggestions.vue';
import FlashAlertWithErrors from '@/Components/FlashAlertWithErrors.vue';
import LogCard from '@/Components/LogCard.vue';
import { States } from '@/enums';

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    modules: {
        type: Object,
    },
    datatypes: {
        type: Object,
    },
    columns: {
        type: Object,
    },
    existingColumns: {
        type: Array,
        default: () => [],
    },
    // createRevision: {
    //     type: Boolean,
    // },
    expressionHtml: {
        type: String,
    },
    sspec: {
        type: Array,
    },
    logs: {
        type: Array,
        default: () => [],
    },
});

const routeGroupName = 'lineitem_managers';
const headerTitle = ref('Line Item Manager');

const localData = ref({ ...props.data });
const attributeErrors = reactive({});

onMounted(() => {
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));

    // Check if generation is in progress when component mounts
    if (localData.value.is_generating) {
        // Start refreshing logs immediately
        refreshLogs();
        startLogRefreshing();
    }
});

// Clean up interval when component is unmounted
onBeforeUnmount(() => {
    if (logRefreshInterval) {
        clearInterval(logRefreshInterval);
        logRefreshInterval = null;
    }
});

const form = useForm({
    name: props.data.name ?? null,
    module: props.data.module ?? null,
    speed_batch_id: props.data.speed_batch_id ?? null,
    active: props.data.active,
    attributes: props.data.lineitem_attributes ?? [],
});

const addRow = () => {
    const newIndex = form.attributes.length;
    form.attributes.push({
        id: null,
        active: true,
        attribute: '',
        format: 'varchar',
        value: '',
        expression: '',
    });
    // Initialize with no error
    attributeErrors[newIndex] = '';
};

const removeRow = index => {
    form.attributes.splice(index, 1);

    // Clean up error state for removed row
    delete attributeErrors[index];

    // Reindex the errors for remaining rows
    const newErrors = {};
    Object.keys(attributeErrors).forEach(key => {
        const numKey = parseInt(key);
        if (numKey > index) {
            newErrors[numKey - 1] = attributeErrors[key];
        } else if (numKey < index) {
            newErrors[numKey] = attributeErrors[key];
        }
    });

    // Update the errors object
    Object.keys(attributeErrors).forEach(key => delete attributeErrors[key]);
    Object.assign(attributeErrors, newErrors);
};

const postApprove = () => {
    var x = confirm('Are you sure you want to approve this Line Item Manager, not longer able to generate table?');
    if (x) {
        axios
            .post(route(routeGroupName + '.approve', props.data.id))
            .then(response => {
                localData.value = response.data.data;
                alert(response.data.message);
            })
            .catch(error => {
                alert(error.response.data.message);
            });
    }
};

// Variable to store the interval ID for log refreshing
let logRefreshInterval = null;

const postGenerate = () => {
    var x = confirm('Are you sure you want to generate this Line Item Manager?');
    if (x) {
        localData.value.is_generating = true;
        axios
            .post(route(routeGroupName + '.generate', props.data.id))
            .then(response => {
                localData.value.is_generating = true;
                alert(response.data.message);

                // Refresh logs immediately
                refreshLogs();

                // Start refreshing logs every 3 seconds while generating
                startLogRefreshing();

                // Switch to the logs tab
                switchToLogsTab();
            })
            .catch(error => {
                localData.value.is_generating = false;
                alert(error.response.data.message);
            });
    }
};

// Function to start refreshing logs at regular intervals
const startLogRefreshing = () => {
    // Clear any existing interval first
    if (logRefreshInterval) {
        clearInterval(logRefreshInterval);
    }

    // Set up a new interval to refresh logs every 3 seconds
    logRefreshInterval = setInterval(() => {
        refreshLogs();

        // Check if generation is complete and stop refreshing if it is
        if (!localData.value.is_generating) {
            clearInterval(logRefreshInterval);
            logRefreshInterval = null;
        }
    }, 3000);
};

// Function to switch to the logs tab
const switchToLogsTab = () => {
    // Use setTimeout to ensure DOM is updated
    setTimeout(() => {
        // Find the logs tab link and click it
        const logsTab = document.querySelector('a[href="#tab_3"]');
        if (logsTab) {
            logsTab.click();
        }
    }, 100);
};

// Function to refresh logs
const refreshLogs = () => {
    if (!localData.value.id) return;

    axios.get(route(routeGroupName + '.logs', localData.value.id))
        .then(response => {
            // Update logs in props (this is a workaround since props are read-only)
            if (props.logs && Array.isArray(props.logs)) {
                props.logs.splice(0, props.logs.length, ...response.data);

                // Check if the most recent log has completed_at, which means generation is done
                if (response.data.length > 0) {
                    const latestLog = response.data[0]; // First log is the most recent
                    if (latestLog.completed_at || latestLog.failed) {
                        localData.value.is_generating = false;
                    }
                }
            }
        })
        .catch(error => {
            console.error('Error refreshing logs:', error);
        });
};

const isReadOnly = computed(() => {
    return localData.value.speed_batch?.state == States.SNAPSHOT;
});

const showGenerateButton = computed(() => {
    return localData.value.id != null && localData.value.speed_batch?.state != States.SNAPSHOT;
});

const disableGenerateButton = computed(() => {
    return form.processing || localData.value.is_generating;
});

// Check if there are any attribute validation errors
const hasAttributeErrors = computed(() => {
    return Object.values(attributeErrors).some(error => error);
});

// Submit form with validation
const submitForm = () => {
    // Validate all attributes first
    form.attributes.forEach((attr, index) => {
        validateAttribute(attr.attribute, index);
    });

    // If there are validation errors, prevent submission
    if (hasAttributeErrors.value) {
        return;
    }

    // Submit the form
    if (props.data.id == null) {
        form.post(route(routeGroupName + '.store'), { preserveState: true });
    } else {
        form.patch(route(routeGroupName + '.update', props.data.id), { preserveState: true });
    }
};


// Function to validate if attribute exists in existingColumns
const validateAttribute = (attribute, index) => {
    if (!attribute || !props.existingColumns) {
        delete attributeErrors[index];
        return;
    }

    // Convert attribute to lowercase for case-insensitive comparison
    const attributeLower = attribute.toLowerCase();

    // Check if the attribute exists in props.columns (case-insensitive)
    const existsInColumns = props.existingColumns.some(column => column.toLowerCase() === attributeLower);

    // Check if the attribute is duplicated within the form itself
    const duplicateIndex = form.attributes.findIndex((attr, idx) =>
        idx !== index && attr.attribute.toLowerCase() === attributeLower
    );

    if (existsInColumns) {
        attributeErrors[index] = `The attribute "${attribute}" already exists in the database columns`;
    } else if (duplicateIndex !== -1) {
        attributeErrors[index] = `The attribute "${attribute}" is already used in row ${duplicateIndex + 1}`;
    } else {
        // Clear error if it's valid
        delete attributeErrors[index];
    }
};

// Watch for changes in props.data to update localData
watch(
    () => props.data,
    newVal => {
        localData.value = { ...newVal };
    },
);

// Watch for changes in form.attributes to validate attributes
watch(
    () => form.attributes,
    (newAttributes) => {
        newAttributes.forEach((attr, index) => {
            validateAttribute(attr.attribute, index);
        });
    },
    { deep: true }
);

</script>

<template>

    <Head :title="headerTitle" />

    <AuthenticatedLayout>
        <FlashAlertWithErrors :errors="$page.props.errors" @close="$page.props.errors = {}" />
        <template #header> {{ headerTitle }} </template>
        <span v-if="props.createRevision">(New Revision {{ props.data.revision }})</span>

        <form @submit.prevent="submitForm">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <ul class="nav nav-tabs card-header-tabs">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#tab_1">Details</a>
                        </li>
                        <li v-show="localData.generated_at" class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#tab_2">Table</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#tab_3">Logs</a>
                        </li>
                        <!-- <li v-show="localData.generated_at" class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#tab_3">Compare</a>
                        </li> -->
                    </ul>
                    <!-- PhooiSan said no need 6May2025 -->
                    <!-- <div v-if="data.id != null && !isReadOnly" class="text-end">
                        <div class="dropdown">
                            <button class="btn btn-sm btn-info dropdown-toggle" type="button" data-bs-toggle="dropdown"
                                aria-expanded="false">
                                <i class="bi bi-three-dots-vertical"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu">
                                <li>
                                    <button type="button" class="dropdown-item" @click="postApprove"
                                        :disabled="localData.approved">
                                        <span v-if="localData.approved_user"> {{ localData.approved_user.name }}
                                            approved at {{
                                                localData.approved_at }} </span>
                                        <span v-else>Approve</span>
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div> -->
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <div class="tab-pane fade pt-10 show active" id="tab_1" role="tabpanel" aria-labelledby="tab_1">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <InputLabel for="name" value="Name" />
                                    <TextInput id="name" type="text" v-model="form.name" :invalid="form.errors.name"
                                        :readonly="isReadOnly" required />
                                    <InputError :message="form.errors.name" />
                                </div>
                                <div class="col-md-3">
                                    <InputLabel for="module" value="Module" />
                                    <Select id="module" v-model="form.module" :invalid="form.errors.module"
                                        :options="modules" :placeholder="'Select Module'" required />
                                    <InputError :message="form.errors.module" />
                                </div>
                                <div class="col-md-3">
                                    <InputLabel for="speed" value="Speed" />
                                    <TextInput id="speed" type="text" v-model="data.speed_batch.name" disabled />
                                </div>
                            </div>

                            <div class="row g-3 mt-2">
                                <div class="col-6">
                                    <Checkbox id="checkActive" v-model:checked="form.active"> Active </Checkbox>
                                </div>
                            </div>

                            <div class="row g-3 mt-3">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th width="3%">
                                                <button type="button" class="btn btn-sm btn-primary" @click="addRow"><i
                                                        class="bi bi-plus"></i></button>
                                            </th>
                                            <th width="3%">No</th>
                                            <th width="3%">Active</th>
                                            <th width="20%">Attribute</th>
                                            <th width="10%">Format</th>
                                            <!-- <th width="20%">Value</th> -->
                                            <th>
                                                <a href="#" data-bs-toggle="modal"
                                                    data-bs-target="#expressionModal">Expression</a>
                                                <button type="button" class="btn btn-link btn-sm"
                                                    data-bs-toggle="tooltip" data-bs-html="true"
                                                    :data-bs-title="expressionHtml">
                                                    <i class="bi bi-info-circle"></i>
                                                </button>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="(row, index) in form.attributes" :key="index">
                                            <td class="text-center">
                                                <button type="button" class="btn btn-sm btn-danger"
                                                    @click="removeRow(index)"><i class="bi bi-dash"></i></button>
                                            </td>
                                            <td class="text-center">{{ index + 1 }}</td>
                                            <td class="text-center">
                                                <TextInput type="hidden" v-model="row.id" />
                                                <Checkbox id="checkActive" v-model:checked="row.active"> </Checkbox>
                                            </td>
                                            <td>
                                                <TextInput type="text" v-model="row.attribute"
                                                    :invalid="attributeErrors[index]"
                                                    @input="validateAttribute(row.attribute, index)" />
                                                <InputError :message="attributeErrors[index]" />
                                            </td>
                                            <td>
                                                <Select id="format" v-model="row.format" :options="datatypes"
                                                    :placeholder="'Select'" />
                                            </td>
                                            <td>
                                                <ExpressionSuggestions v-model="row.expression"
                                                    :suggestions="columns" />
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div v-if="localData.generated_at" class="tab-pane fade pt-10" id="tab_2" role="tabpanel"
                            aria-labelledby="tab_2">
                            <DynamicTable :key="localData.id + localData.approved" :lineitemId="localData.id"
                                :tableType="'lineitem'" />
                        </div>
                        <div class="tab-pane fade pt-10" id="tab_3" role="tabpanel" aria-labelledby="tab_3">
                            <div class="mb-4">
                                <h4>Generation Logs</h4>
                            </div>

                            <div v-if="props.logs && props.logs.length > 0" class="logs-container">
                                <LogCard v-for="(log, index) in props.logs" :key="index" :log="log" />
                            </div>

                            <div v-else class="alert alert-info">
                                No generation logs found for this Line Item Manager.
                            </div>
                        </div>
                        <!-- <div v-if="localData.generated_at" class="tab-pane fade pt-10" id="tab_3" role="tabpanel" aria-labelledby="tab_3">
                            <LineitemCompare :original_id="localData.original_id" />
                        </div> -->
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex">
                        <div class="me-auto">
                            <Link class="btn btn-secondary me-2"
                                :href="route('speedbatch.edit', data.speed_batch_id) + '#tab_3'">Back</Link>
                            <PrimaryButton v-show="!isReadOnly" type="submit"
                                v-html="data.id == null ? 'Create' : 'Save'"
                                :disabled="form.processing || hasAttributeErrors">
                            </PrimaryButton>
                        </div>
                        <div v-show="showGenerateButton">
                            <button type="button" class="btn btn-success" :disabled="disableGenerateButton"
                                @click="postGenerate" v-html="localData.is_generating ? 'Generating...' : 'Generate'" />
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <ExpressionModal />
    </AuthenticatedLayout>
</template>

<style scoped>
.logs-container {
    max-width: 100%;
}

.exception-container pre,
.messages-container pre {
    white-space: pre-wrap;
    word-break: break-word;
}

.messages-container {
    border-bottom: 1px solid #eee;
    padding-bottom: 1rem;
}

.border-end {
    border-right: 1px solid #dee2e6;
}
</style>
