<?php

namespace App\Http\Controllers;

use App\Http\Requests\ComponentTypeUpdateRequest;
use App\Models\ComponentType;
use App\Services\ComplexTypeDetailXML;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class ComponentTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        //Build Filter
        $filters = $this->filterSessions($request, 'component_types', [
            'keyword' => ''
        ]);

        $list = ComponentType::query()->when(!empty($filters['keyword']), function ($q) use ($filters) {
            $q->orWhere('name', 'like', '%' . $filters['keyword'] . '%');
        })->filterSort($filters)->orderBy('id', 'asc')->paginate(config('table.per_page'));

        //Load the files
        $sourceTypes = ComponentType::sourceTypes();
        $fileTypes = ['complexTypes', 'upload'];
        $files = [];
        foreach ($sourceTypes as $sourceType) {
            foreach ($fileTypes as $fileType) {
                $path = Storage::disk('public')->path("component_types/{$fileType}-{$sourceType}.xml");
                if (file_exists($path)) {
                    $url = asset("storage/component_types/{$fileType}-{$sourceType}.xml");
                    $files["{$fileType}"][] = $url;
                }
            }
        }

        return Inertia::render('ComponentType/Index', [
            'header' => ComponentType::header(),
            'filters' => $filters,
            'list' => $list,
            'sourceTypes' => ComponentType::sourceTypeOptions(),
            'files' => $files
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return $this->edit(null);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(ComponentTypeUpdateRequest $request)
    {
        return $this->update($request, null);
    }

    /**
     * Display the specified resource.
     */
    public function show(ComponentType $componentType)
    {
        // Load the component type with the formatted XML data
        $componentType->makeVisible(['xml_sub_elements']);
        $componentType->append('xml_sub_elements');

        // Initialize the ComplexTypeDetailXML service
        $xmlService = new ComplexTypeDetailXML();

        return Inertia::render('ComponentType/Show', [
            'data' => $componentType,
            'detail' => $xmlService->getDetailByComponentTypeName($componentType->name),
            'ioParams' => $xmlService->getIOParamsByComponentTypeName($componentType->name)
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(?ComponentType $componentType = null)
    {
        if (null === $componentType) {
            $data = new ComponentType;
        } else {
            $data = $componentType;
        }

        return Inertia::render('ComponentType/Edit', [
            'data' => $data,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(ComponentTypeUpdateRequest $request, ?ComponentType $componentType = null)
    {
        $data = $request->validated();
        $data['user_id'] = auth()->id();

        if (null === $componentType) {
            $data = ComponentType::create($data);
            return Redirect::route('component_types.edit', $data->id)->with('message', 'Component Type created successfully');
        } else {
            $componentType->update($data);
            return Redirect::route('component_types.edit', $componentType->id)->with('message', 'Component Type updated successfully');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ComponentType $componentType)
    {
        $componentType->delete();
        return Redirect::route('component_types.index')->with('message', 'Component Type deleted successfully');
    }

    public function postImportComplexTypes(Request $request)
    {
        $request->validate([
            'file' => [
                'required',
                'file',
                'mimes:xml'
            ],
            // 'source' => [
            //     'required',
            //     'string',
            //     'in:' . implode(',', array_keys(ComponentType::sourceTypeOptions()))
            // ]
        ]);

        try {
            //Using only one source for now
            $source = $request->source ?? array_keys(ComponentType::sourceTypeOptions())[0];

            // First check the XML structure
            $xmlFile = $request->file('file');
            $xmlContent = file_get_contents($xmlFile->path());
            $xml = simplexml_load_string($xmlContent);

            if ($xml === false) {
                throw new \Exception("Failed to parse XML file");
            }

            // Check if the root element is UpsTypes
            if ($xml->getName() !== 'UpsTypes') {
                throw new \Exception("Invalid XML format: root element must be 'UpsTypes'");
            }

            //Store the file to keep a copy
            $file = $request->file('file')->storeAs('component_types', "complexTypes-" . $source . ".xml", 'public');
            $imported = $this->importComplexTypes($source);
            return Redirect::route('component_types.index')->with('message', 'Complex Types imported successfully and create new saved: ' . $imported);
        } catch (\Exception $e) {
            return Redirect::route('component_types.index')->withErrors(['errors' => $e->getMessage()]);
        }
    }

    public function postImportComplexTypeDetails(Request $request)
    {
        $request->validate([
            'file' => [
                'required',
                'file',
                'mimes:xml'
            ],
            // 'source' => [
            //     'required',
            //     'string',
            //     'in:' . implode(',', array_keys(ComponentType::sourceTypeOptions()))
            // ]
        ]);

        try {
            //Using only one source for now
            $source = $request->source ?? array_keys(ComponentType::sourceTypeOptions())[0];

            // First check the XML structure
            $xmlFile = $request->file('file');
            $xmlContent = file_get_contents($xmlFile->path());
            $xml = simplexml_load_string($xmlContent);

            if ($xml === false) {
                throw new \Exception("Failed to parse XML file");
            }

            // Check if the root element is UploadTypes
            if ($xml->getName() !== 'UploadTypes') {
                throw new \Exception("Invalid XML format: root element must be 'UploadTypes'");
            }

            //Store the file to keep a copy
            $file = $request->file('file')->storeAs('component_types', "upload-" . $source . ".xml", 'public');
            return Redirect::route('component_types.index')->with('message', 'Complex Types Detail imported successfully.');
        } catch (\Exception $e) {
            return Redirect::route('component_types.index')->withErrors(['errors' => $e->getMessage()]);
        }
    }

    private function importComplexTypes($source)
    {
        $file = Storage::disk('public')->path('component_types/complexTypes-' . $source . '.xml');
        $xml_string = file_get_contents($file);
        $xml = simplexml_load_string($xml_string);

        if ($xml === false) {
            throw new \Exception("Failed to parse XML file");
        } else if ($xml->getName() !== 'UpsTypes') {
            throw new \Exception("Invalid XML format: root element must be 'UpsTypes'");
        }

        // Count for reporting
        $imported = 0;
        $skipped = 0;

        // Each UpsType in the XML represents a component type
        foreach ($xml->UpsType as $upsType) {
            // Extract attributes from the UpsType element
            $type = (string)$upsType->attributes()->type;
            $configName = (string)$upsType->attributes()->config_name;
            $level = (string)$upsType->attributes()->level;
            $scenarioEnabled = null;
            if (isset($upsType->attributes()->scenarioEnabled)) {
                $scenarioEnabled = $upsType->attributes()->scenarioEnabled == "false" ? false : true;
            }

            // Use the config_name as our component type name if available, otherwise use type
            $name = !empty($configName) ? $configName : $type;

            // Skip if name is empty
            if (empty($name)) {
                $skipped++;
                continue;
            }

            // Process sub elements (Speed, Dicc, Sicc, etc.)
            $subElements = [];
            foreach ($upsType->children() as $childElement) {
                $elementName = $childElement->getName();
                $elementAttrs = [];

                // Get all attributes of the child element
                foreach ($childElement->attributes() as $attrName => $attrValue) {
                    $elementAttrs[$attrName] = (string)$attrValue;
                }

                // Check for nested sub-elements (like ITD inside Speed)
                $nestedElements = [];
                foreach ($childElement->children() as $nestedElement) {
                    $nestedName = $nestedElement->getName();
                    $nestedAttrs = [];

                    // Get all attributes of the nested element
                    foreach ($nestedElement->attributes() as $attrName => $attrValue) {
                        $nestedAttrs[$attrName] = (string)$attrValue;
                    }

                    $nestedElements[] = [
                        'name' => $nestedName,
                        'attributes' => $nestedAttrs
                    ];
                }

                // Add nested elements if any exist
                if (!empty($nestedElements)) {
                    $elementAttrs['elements'] = $nestedElements;
                }

                $subElements[] = [
                    'name' => $elementName,
                    'attributes' => $elementAttrs
                ];
            }

            // Use updateOrCreate to handle both creation and updates
            $result = ComponentType::updateOrCreate(
                ['name' => $name], // The unique key to check against
                [
                    'active' => true,
                    'level' => $level,
                    'type' => $type,
                    'source' => $source,
                    'scenario_enabled' => $scenarioEnabled,
                    'sub_elements' => json_encode($subElements),
                    'create_user_id' => auth()->id(),
                ]
            );

            // Check if it was created or updated
            if ($result->wasRecentlyCreated) {
                $imported++;
            } else {
                $skipped++;
            }
        }

        // Log the import results
        \Log::info("ComponentType import completed: {$imported} imported, {$skipped} skipped");

        return $imported;
    }

    /*
    * Not using at the moment, keep for future
    */
    private function exportComplexTypes()
    {
        // Get all component types
        $componentTypes = ComponentType::all();

        // Create a new XML document
        $dom = new \DOMDocument('1.0', 'utf-8');
        $dom->formatOutput = true;

        // Create the root element <UpsTypes>
        $rootElement = $dom->createElement('UpsTypes');
        $rootElement->setAttribute('xmlns', 'urn:xmlns:ups');
        $dom->appendChild($rootElement);

        // Loop through component types and add them to the XML
        foreach ($componentTypes as $componentType) {
            // Skip if missing required data
            if (empty($componentType->level) || empty($componentType->type)) {
                continue;
            }

            // Create UpsType element
            $upsTypeElement = $dom->createElement('UpsType');
            $upsTypeElement->setAttribute('level', $componentType->level);
            $upsTypeElement->setAttribute('config_name', $componentType->name);
            $upsTypeElement->setAttribute('type', $componentType->type);

            // Add scenarioEnabled attribute if it exists in the original data
            // This is a placeholder - adjust as needed based on your data structure
            if ($componentType->scenario_enabled != null) {
                $upsTypeElement->setAttribute('scenarioEnabled', $componentType->scenario_enabled ? 'true' : 'false');
            }

            // Process sub-elements if available
            if (!empty($componentType->sub_elements)) {
                $subElements = json_decode($componentType->sub_elements, true);

                if (is_array($subElements)) {
                    foreach ($subElements as $subElement) {
                        // Create element for each sub-element (Speed, Dicc, Sicc, etc.)
                        $elementName = $subElement['name'];
                        $elementAttrs = $subElement['attributes'] ?? [];

                        $element = $dom->createElement($elementName);

                        // Add attributes to the element
                        foreach ($elementAttrs as $attrName => $attrValue) {
                            // Skip 'elements' as it's not an attribute but contains nested elements
                            if ($attrName !== 'elements') {
                                $element->setAttribute($attrName, $attrValue);
                            }
                        }

                        // Process nested elements (like ITD inside Speed)
                        if (isset($elementAttrs['elements']) && is_array($elementAttrs['elements'])) {
                            foreach ($elementAttrs['elements'] as $nestedElement) {
                                $nestedName = $nestedElement['name'];
                                $nestedAttrs = $nestedElement['attributes'] ?? [];

                                $nested = $dom->createElement($nestedName);

                                // Add attributes to the nested element
                                foreach ($nestedAttrs as $attrName => $attrValue) {
                                    $nested->setAttribute($attrName, $attrValue);
                                }

                                $element->appendChild($nested);
                            }
                        }

                        $upsTypeElement->appendChild($element);
                    }
                }
            }

            $rootElement->appendChild($upsTypeElement);
        }

        // Save the XML document to file
        $exportPath = Storage::disk('public')->path('component_types/exportedTypes.xml');
        $dom->save($exportPath);
        return $exportPath;
    }
}
