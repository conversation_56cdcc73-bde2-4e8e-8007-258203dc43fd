<script setup>
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import Select from '@/Components/Select.vue';
import TextInput from '@/Components/TextInput.vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, useForm, Link } from '@inertiajs/vue3';
import { ref, computed, watch } from 'vue';
import FlashAlertWithErrors from '@/Components/FlashAlertWithErrors.vue';
import Compare from './Compare.vue';
import ProductSelectors from '@/Components/ProductSelectors.vue';
import FlashAlert from '@/Components/FlashAlert.vue';

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    newColumns: {
        type: Array,
        default: () => [],
    },
    compareResult: {
        type: Object,
        default: () => null,
    },
    productTypes: {
        type: Object,
    },
    products: {
        type: Object,
    },
    productGroups: {
        type: Object,
    },
});

const routeGroupName = 'speedbatch';
const headerTitle = ref('Import Speed Module');

const storeForm = useForm({
    name: null,
    product_type_id: null,
    product_id: null,
    product_group_id: null,
    file: null,
});

const columnForm = useForm({});

const activeImportTab = computed(() => {
    //check is props.newColumns is not empty
    return props.newColumns.length == 0 && props.compareResult == null;
});

const showColumnTab = computed(() => {
    //check is props.newColumns is not empty
    return props.newColumns.length > 0;
});

const activeColumnTab = computed(() => {
    //check is props.newColumns is not empty
    return props.compareResult == null && props.newColumns.length > 0;
});

const activeDifferentTab = computed(() => {
    //check is props.compareResult is not empty
    return props.compareResult != null;
});
</script>

<template>
    <Head :title="headerTitle" />

    <AuthenticatedLayout>
        <FlashAlertWithErrors :errors="$page.props.errors" @close="$page.props.errors = {}" />

        <template #header> {{ headerTitle }} </template>
        <span v-if="props.createRevision">(New Revision {{ props.data.revision }})</span>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <ul class="nav nav-tabs card-header-tabs">
                    <li class="nav-item">
                        <a class="nav-link" :class="{ active: activeImportTab }" data-bs-toggle="tab" href="#tab_1">Import</a>
                    </li>
                    <li v-show="showColumnTab" class="nav-item">
                        <a class="nav-link" :class="{ active: activeColumnTab }" data-bs-toggle="tab" href="#tab_2">New Columns</a>
                    </li>
                    <li v-show="activeDifferentTab" class="nav-item">
                        <a class="nav-link" :class="{ active: activeDifferentTab }" data-bs-toggle="tab" href="#tab_3">Different</a>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content">
                    <div class="tab-pane fade pt-10" :class="{ show: activeImportTab, active: activeImportTab }" id="tab_1" role="tabpanel" aria-labelledby="tab_1">
                        <form @submit.prevent="storeForm.post(route(routeGroupName + '.store'), { preserveScroll: true, preserveState: true })">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <InputLabel for="name" value="Name" />
                                    <TextInput id="name" type="text" v-model="storeForm.name" :invalid="storeForm.errors.name" required />
                                    <InputError :message="storeForm.errors.name" />
                                </div>

                                <ProductSelectors v-model="storeForm" :product-types="productTypes" :products="products" :product-groups="productGroups" :errors="storeForm.errors" />
                            </div>

                            <div class="row g-3 my-2">
                                <div class="col-md-4">
                                    <InputLabel for="file_input" value="Excel File" class="sr-only" />
                                    <input ref="inputFile" type="file" id="file_input" @input="storeForm.file = $event.target.files[0]" :class="{ 'is-invalid': storeForm.errors.file }" class="form-control" accept=".xls,.xlsx" />
                                    <InputError :message="storeForm.errors.file" class="mt-2" />
                                </div>
                            </div>
                            <PrimaryButton type="submit" :disabled="storeForm.processing"> Import </PrimaryButton>
                        </form>
                    </div>

                    <div v-show="showColumnTab" class="tab-pane fade pt-10" :class="{ show: activeColumnTab, active: activeColumnTab }" id="tab_2" role="tabpanel" aria-labelledby="tab_2">
                        <form @submit.prevent="columnForm.post(route(routeGroupName + '.different'), { preserveState: false })">
                            <div class="row g-3 mb-2">
                                <div class="col-md-4">
                                    New Columns found
                                    <ol>
                                        <li v-for="(column, index) in newColumns" :key="index">
                                            <b>{{ column }}</b>
                                        </li>
                                    </ol>

                                    Do you want to proceed?
                                </div>
                            </div>
                            <PrimaryButton type="submit" :disabled="columnForm.processing"> Continue </PrimaryButton>
                        </form>
                    </div>
                    <div v-if="activeDifferentTab" class="tab-pane fade pt-10" :class="{ show: activeDifferentTab, active: activeDifferentTab }" id="tab_3" role="tabpanel" aria-labelledby="tab_3">
                        <Compare :headers="compareResult.columnsWithDifferences" :differences="compareResult.differences" />

                        <form @submit.prevent="columnForm.post(route(routeGroupName + '.import'), { preserveState: false })">
                            <PrimaryButton type="submit" :disabled="columnForm.processing"> Import </PrimaryButton>
                        </form>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex">
                    <div class="me-auto">
                        <Link class="btn btn-secondary me-2" :href="route(routeGroupName + '.index')">Back </Link>
                    </div>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>
