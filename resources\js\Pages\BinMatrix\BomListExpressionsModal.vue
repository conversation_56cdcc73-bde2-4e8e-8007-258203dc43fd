<script setup>
import Modal from '@/Components/Modal.vue';
import TextInput from '@/Components/TextInput.vue';
import { useForm } from '@inertiajs/vue3';
import axios from 'axios';
import { onMounted, ref, computed } from 'vue';
import Multiselect from 'vue-multiselect';
import FlashAlertWithErrors from '@/Components/FlashAlertWithErrors.vue';

const bomListExpressionsModal = ref(null);
const headers = ref([]);
const groups = ref([]);

// Define emits
const emit = defineEmits(['saveSuccess']);

const props = defineProps({
    id: {
        type: Number,
        required: true,
    },
    editDisabled: {
        type: Boolean,
        default: false,
    }
});

const form = useForm({
    list: [],
});

// Add processing state to track when axios request is in progress
const processing = ref(false);

onMounted(() => {
    const modalElement = document.getElementById('bomListExpressionsModal');
    modalElement.addEventListener('show.bs.modal', () => {
        axios.get(route('bin_matrix.bom_list_expression', props.id)).then(response => {
            headers.value = response.data.headers;
            groups.value = response.data.groups;
            form.list = response.data.data;
        });
    });
});

const save = () => {
    // Validate all entries before submission
    const hasErrors = form.list.some(row =>
        row.entries.some(entry => !isValidEntry(entry))
    );

    if (hasErrors) {
        alert('Please correct invalid entries before saving');
        return;
    }

    // Set processing state to true to disable buttons
    processing.value = true;
    form.clearErrors();

    // Use axios instead of form.post
    axios.post(route('bin_matrix.bom_list_expression', props.id), {
        list: form.list
    })
        .then(response => {
            // Handle success
            closeModal();
            resetInput();

            // Emit success event with message if provided in the response
            const message = response.data.message ?? null;
            emit('saveSuccess', message);
        })
        .catch(error => {
            // Handle errors
            if (error.response && error.response.data) {
                if (error.response && error.response.data) {
                    form.setError(error.response.data);
                }
            } else {
                alert('An error occurred while saving. Please try again.');
            }
        })
        .finally(() => {
            // Reset processing state
            processing.value = false;
        });
};

const closeModal = () => {
    form.clearErrors();
    resetInput();
    bomListExpressionsModal.value.close();
};

const resetInput = () => {
    form.reset();
};

//function on this page
const addRow = () => {
    const uuid = crypto.randomUUID(); // Browser built-in

    form.list.push({
        bin_matrix_item_id: null,
        entries: headers.value.map(attr => ({
            uuid: uuid,
            id: null,
            value: '',
            bom_list_attribute_id: attr.id,
            length: attr.length, //max character length per comma
        })),
        sequence: form.list.length + 1,
    });
};

const removeRow = index => {
    form.list.splice(index, 1);
    //Reupdate the sequence
    form.list.forEach((row, index) => {
        row.sequence = index + 1;
    });
};

//Hnadle the entries value format and validation
const formatEntry = entry => {
    entry.value = entry.value.toUpperCase().replace(/\s/g, '');
};

const isValidEntry = entry => {
    if (!entry.value) return true;
    return entry.value.split(',').every(segment => segment.length === entry.length);
};

//Computed
const buttonYes = computed(() => {
    return props.editDisabled ? null : 'Save';
})
</script>

<template>
    <Modal ref="bomListExpressionsModal" @yesEvent="save" @noEvent="closeModal" :id="'bomListExpressionsModal'"
        :title="'Bom List Expressions Editor'" :buttonYes="buttonYes" :buttonType="'primary'" :form="form"
        :modalClass="'modal-xl'" :processing="processing">
        <FlashAlertWithErrors :errors="form.errors" @close="form.clearErrors()" />
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th width="3%">
                            <button type="button" class="btn btn-sm btn-primary" @click="addRow">
                                <i class="bi bi-plus"></i>
                            </button>
                        </th>
                        <th width="3%">No</th>
                        <th width="15%">Bom Group</th>
                        <th v-for="attribute in headers" :key="attribute.id">
                            {{ attribute.name }}
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(row, index) in form.list" :key="index">
                        <td class="text-center">
                            <button type="button" class="btn btn-sm btn-danger" @click="removeRow(index)"><i
                                    class="bi bi-dash"></i></button>
                        </td>
                        <td class="text-center">
                            {{ index + 1 }}
                        </td>
                        <td>
                            <Multiselect v-model="row.bin_matrix_item_id" :options="groups.map(g => g.id)"
                                :custom-label="id => groups.find(g => g.id === id)?.label ?? 'N/A'" :searchable="true"
                                :close-on-select="true" :show-labels="false" required>
                            </Multiselect>
                        </td>
                        <td v-for="(entry, idx) in row.entries" :key="idx">
                            <!-- Limit the character length per comma -->
                            <input v-model="entry.value" type="text" class="form-control"
                                :class="{ 'is-invalid': entry.value && !isValidEntry(entry) }"
                                @input="formatEntry(entry)" required />
                            <div v-if="entry.value && !isValidEntry(entry)" class="invalid-feedback">Need exactly {{
                                entry.length }} characters</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </Modal>
</template>
