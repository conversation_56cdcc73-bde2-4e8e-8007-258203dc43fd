<?php

namespace App\Imports;

use App\Models\Speed;
use App\Models\SpeedBatch;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

class SpeedImportOld implements ToCollection, WithHeadingRow, WithValidation
{

    protected $errors = [];

    /**
     * @param Collection $collection
     */
    public function collection(Collection $collection)
    {
        //Skip column
        $exclude_columns = ['Internal Stepping (Do not use)'];
        $this->addNewColumnsToTable($collection, $exclude_columns);

        $liraBatch = SpeedBatch::create(['user_id' => auth()->id()]);

        foreach ($collection as $row) {
            $data = [];
            foreach ($row as $column => $value) {
                if (in_array($column, $exclude_columns)) continue;

                $data[$column] = empty($value) ? null : $value;
            }
            $data['batch_id'] = $liraBatch->id;
            Speed::forceCreate($data);
        }
    }

    public function headingRow(): int
    {
        return 2;
    }

    /**
     * Define validation rules.
     */
    public function rules(): array
    {
        return [
            'external_revision' => 'nullable|integer|min:0|max:255',
            'spec_code' => 'nullable|string|max:10',
            'spec_sequential_number' => 'nullable|string|max:10',
            'external_step' => 'nullable|string|max:10',
            'processor_base_code' => 'nullable|string|max:255',
            'finished_good_type' => 'nullable|string|max:20',
            'internal_rev_step' => 'nullable|string|max:10',
            'ic_category_type' => 'nullable|string|max:30',
            'item_market_name' => 'nullable|string|max:20',
            'pincount' => 'nullable|integer|min:-32768|max:32767',
            'market_code_name' => 'nullable|string|max:30',
            'previous_reference_id' => 'nullable|string|max:20',
            'product_grade' => 'nullable|string|max:10',
            'power_grade' => 'nullable|string|max:10',
            'transceiver_tile_config' => 'nullable|string|max:10',
            'transceiver_count' => 'nullable|integer|min:0|max:255',
            'transceiver_speed_ratio' => 'nullable|integer|min:0|max:255',
            'core_speed_ratio' => 'nullable|integer|min:0|max:255',
            'product_variant' => 'nullable|string|max:10',
            'density' => 'nullable|integer|min:0|max:65535',
            'density_uom' => 'nullable|string|max:10',
            'voltage_io' => 'nullable|numeric',
            'programmed_ind' => 'nullable|string|max:5',
            'mm_technology' => 'nullable|string|max:20',
            'pp_steering_committee' => 'nullable|string|max:10',
            'pb_free' => 'nullable|string|max:5',
            'custom_indicator' => 'nullable|string|max:5',
            'customer_custom_product' => 'nullable|boolean',
            'package_platform' => 'nullable|string|max:20',
            'package_text' => 'nullable|string|max:20',
            'shipment_media' => 'nullable|string|max:20',
            'trademark_family_name' => 'nullable|string|max:255',
            'fab_process' => 'nullable|string|max:10',
            'internal_stepping' => 'nullable|string|max:10',
            'speed_type' => 'nullable|string|max:30',
            'external_product_id' => 'nullable|string|max:20',
            'speed' => 'nullable|numeric',
            'die_code_name' => 'nullable|string|max:30',
            'cust_part_no' => 'nullable|string|max:20',
            'royalty_technology' => 'nullable|string|max:255',
            'comments' => 'nullable|string',
            'engineering_efforts' => 'nullable|string',
            'sub_component_category_name' => 'nullable|string|max:10',
            'rldram_iii_mhz' => 'nullable|integer|min:0|max:65535',
            'lvds_gbps' => 'nullable|numeric|min:0|max:999999.99',
            'mlab_simple_dual_port_mhz' => 'nullable|integer|min:0|max:65535',
            'mlab_true_dual_port_mhz' => 'nullable|integer|min:0|max:65535',
            'lutram_mhz' => 'nullable|integer|min:0|max:65535',
            'dsp_fixed_point_mode_mhz' => 'nullable|integer|min:0|max:65535',
            'dsp_floating_point_mode_mhz' => 'nullable|integer|min:0|max:65535',
            'maib_mhz' => 'nullable|integer|min:0|max:65535',
            'esram_mhz' => 'nullable|integer|min:0|max:65535',
            'hps_mhz_rate' => 'nullable|integer|min:0|max:65535',
            'ddr4_freq' => 'nullable|integer|min:0|max:65535',
            'external_stepping' => 'nullable|string|max:10',
            'l4_forecast_name' => 'nullable|string|max:255',
            'mm' => 'nullable|string|max:20',
            'package_type' => 'nullable|string|max:255',
            'qdf_sspec' => 'nullable|string|max:10',
            'sample_production_type' => 'nullable|string|max:20',
        ];
    }

    private function addNewColumnsToTable($collection, $exclude_columns)
    {
        // Retrieve column names from the first row
        $columns = array_values(array_diff(array_keys($collection->first()->toArray() ?? []), $exclude_columns));
        $speedColumns = (new Speed)->getTableColumns();
        // Check if all columns are present in the table
        $newColumns = array_values(array_diff($columns, $speedColumns));
        if (count($newColumns) == 0) return;


        //Add new columns to the table
        Schema::table('speed', function (Blueprint $table) use ($newColumns) {
            foreach ($newColumns as $column) {
                //Note: PostgreSQL cannot save column at specific location
                $table->string($column)->nullable();
            }
        });
    }
}
