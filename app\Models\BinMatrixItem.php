<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\SoftDeletes;

class BinMatrixItem extends BaseModel
{
    use SoftDeletes;

    protected $fillable = [
        'bom_group',
        'bin_type',
        'sequence',
        'bom_list',
        'bin_matrix_id',
    ];

    public function binMatrix()
    {
        return $this->belongsTo(BinMatrix::class);
    }

    public function binMatrixFlows()
    {
        return $this->hasMany(BinMatrixFlow::class)->orderBy('flow');
    }

    public function bomListExplode(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => isset($attributes['bom_list']) && !empty($attributes['bom_list']) ? explode(',', str_replace(' ', '', $attributes['bom_list'])) : []
        );
    }
}
