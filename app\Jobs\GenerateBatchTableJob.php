<?php

namespace App\Jobs;

use App\Models\GenerateBatchLog;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Throwable;

class GenerateBatchTableJob implements ShouldQueue
{
    use Queueable;

    public $speed_batch_id;
    /**
     * Create a new job instance.
     */
    public function __construct($speed_batch_id)
    {
        $this->speed_batch_id = $speed_batch_id;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        //run app:generate-batch-table
        \Artisan::call('app:generate-batch-table ' . $this->speed_batch_id);
    }

    /**
     * Handle a job failure.
     */
    public function failed(?Throwable $exception): void
    {
        $log = GenerateBatchLog::where('id', $this->speed_batch_id)->first();
        $log->failed = true;
        $log->exception .= "[" . now()->format('Y-m-d H:i:s') . "] " . $exception->getMessage() . "\n";
        $log->save();
    }
}
