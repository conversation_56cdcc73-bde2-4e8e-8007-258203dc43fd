<?php

namespace App\Http\Controllers;

use App\Http\Requests\UserUpdateRequest;
use App\Models\ProductPermission;
use App\Models\ProductType;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        //Build Filter
        $filters = $this->filterSessions($request, 'user', [
            'keyword' => ''
        ]);

        $list = User::query()->when(!empty($filters['keyword']), function ($q) use ($filters) {
            $q->orWhere('name', 'like', '%' . $filters['keyword'] . '%');
            $q->orWhere('email', 'like', '%' . $filters['keyword'] . '%');
        })->filterSort($filters)->paginate(config('table.per_page'));

        return Inertia::render('User/Index', [
            'header' => User::header(),
            'filters' => $filters,
            'list' => $list,
            'useUsername' => env(LOGIN_USERNAME, false)
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return $this->edit(null);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(UserUpdateRequest $request)
    {
        return $this->update($request, null);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    public function edit(string $id = null)
    {
        if (null == $id) {
            $data = new User;
        } else {
            $data = User::find($id);
        }
        $menu_list = config('menus.items');

        $productTypes = ProductType::with(['products' => function ($query) {
            $query->with('productGroups');
        }])->get();

        //Query all product permission
        $product_permissions = $this->intializeProductPermission($data, $productTypes);


        return Inertia::render('User/Edit', [
            'data' => $data,
            'useUsername' => env(LOGIN_USERNAME, false),
            'menu_list' => $menu_list,
            'product_types' => $productTypes,
            'product_permissions' => $product_permissions,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UserUpdateRequest $request, string $id = null)
    {
        $data = $request->validated();
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        } else {
            unset($data['password']);
        }

        if (null == $id) {
            $data = User::create($data);
            return Redirect::route('users.edit', $data->id)->with('message', 'User created successfully');
        } else {
            User::find($id)->update($data);
            return Redirect::route('users.edit', $id)->with('message', 'User updated successfully');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        $user->delete();
        return Redirect::route('users.index')->with('message', 'User deleted successfully');
    }

    /**
     * Save Menu
     */
    public function patchMenu(Request $request, $id)
    {
        $data = User::find($id);
        $data->menu_permission = $request->menus;
        $data->save();
        return Redirect::route('users.edit', $data->id)->with('message', 'Menu Permission Updated');
    }

    public function patchProductPermission(Request $request, $id)
    {
        $data = User::find($id);

        ProductPermission::where('user_id', $data->id)->delete();

        foreach ($request->productTypes as $id => $check) {
            if ($check)
                $this->grantAccess($data, 'ProductType', $id);
        }
        foreach ($request->products as $id => $check) {
            if ($check)
                $this->grantAccess($data, 'Product', $id);
        }
        foreach ($request->productGroups as $id => $check) {
            if ($check)
                $this->grantAccess($data, 'ProductGroup', $id);
        }

        return Redirect::route('users.edit', $data->id)->with('message', 'Product Permission Updated');
    }

    private function grantAccess(User $user, $type, $id)
    {
        ProductPermission::create([
            'user_id' => $user->id,
            'accessible_type' => "App\\Models\\$type",
            'accessible_id' => $id
        ]);
    }

    private function intializeProductPermission(User $user, $productTypes)
    {
        $product_permissions = $user->getAllProductPermissions();
        //Vue need to initialise the checkbox, build
        foreach ($productTypes as $type) {
            if (!isset($product_permissions['productTypes'][$type->id])) {
                $product_permissions['productTypes'][$type->id] = false;
            }
            foreach ($type->products as $product) {
                if (!isset($product_permissions['products'][$product->id])) {
                    $product_permissions['products'][$product->id] = false;
                }
                foreach ($product->productGroups as $group) {
                    if (!isset($product_permissions['productGroups'][$group->id])) {
                        $product_permissions['productGroups'][$group->id] = false;
                    }
                }
            }
        }
        return $product_permissions;
    }
}
