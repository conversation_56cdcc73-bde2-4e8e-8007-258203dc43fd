<?php

if (!function_exists('encodeColumn')) {
    /**
     * If name have special character vue excel cannot work,
     * because when a box is selected, it use html attribute "id" and "id" cannot have special character
     * By default column have _ will add __, because rawurlencode did not convert _ to % therefore add double __
     * to identify it orignal is single _
     */
    function encodeColumn($column)
    {
        if (str_contains($column, '_')) return str_replace('_', '__', $column);
        return str_replace('%', '_', rawurlencode($column));
    }
}

if (!function_exists('decodeColumn')) {
    /**
     * Decode back to orignal column name before save to database
     */
    function decodeColumn($column)
    {
        if (str_contains($column, '__')) return str_replace('__', '_', $column);
        return rawurldecode(str_replace('_', '%', $column));
    }
}

if (!function_exists('excelFailureMessage')) {
    /**
     * Reuse excel failure message
     */
    function excelFailureMessage(\Maatwebsite\Excel\Validators\ValidationException $e)
    {
        $failures = $e->failures();
        $error_messages = [];
        foreach ($failures as $failure) {
            $failure->row();
            $failure->attribute();
            $failure->errors();
            $failure->values();
            foreach ($failure->errors() as $error) {
                $error_messages[] = "Row " . $failure->row() . " - [" . $failure->values()[$failure->attribute()] . "] " . $error;
            }
        }
        return $error_messages;
    }
}

if (!function_exists('multiselect_options')) {
    function multiselect_options($model, $id = 'id', $label = 'name')
    {
        $options = [];
        foreach ($model as $m) {
            $options[] = ['id' => $m->{$id}, 'label' => $m->{$label}];
        }

        return $options;
    }
}

if (!function_exists('select_options')) {
    function select_options($model, $id = 'id', $labels = [])
    {
        $options = [];
        foreach ($model as $m) {
            if (count($labels) > 1) {
                // Join multiple labels with dashes
                $labelValues = [];
                foreach ($labels as $label) {
                    $labelValues[] = $m->{$label};
                }
                $options[$m->{$id}] = implode(' - ', $labelValues);
            } else {
                $options[$m->{$id}] = $m->{$labels[0]};
            }
        }

        return $options;
    }
}

if (!function_exists('convertDataType')) {
    function convertDataType($dataType)
    {
        if (stripos($dataType, 'float') !== false) {
            return "Float/Exponential";
        } else if (stripos($dataType, 'int') !== false) {
            return "Integer";
        }
        return "String";
    }
}

if (!function_exists('notEmptyOrZero')) {
    function notEmptyOrZero(mixed $value)
    {
        // empty() is true for "", NULL, false, 0, "0", [], etc.
        // We only want to exclude the "truly" empty values, but keep 0/"0"
        return !empty($value) || $value === 0 || $value === '0';
    }
}

if (!function_exists('binToHexWithPack')) {
    function binToHexWithPack(mixed $bin)
    {
        $bits = strlen($bin);
        $bytes  = $bits / 8;
        $hexLen = $bytes * 2;
        $dec    = bindec($bin);

        // Use X (uppercase hex). 0-padding to $hexLen digits.
        return sprintf("%0{$hexLen}X", $dec);
    }
}

if (!function_exists('formatLog')) {
    function formatLog(string $message)
    {
        return "[" . now()->format('Y-m-d H:i:s') . "] " . $message . "\n";
    }
}

if (!function_exists('databaseDataType')) {
    function databaseDataType(string $dataType)
    {
        switch ($dataType) {
            case 'varchar':
                $definition = "character varying(255)";
                break;
            case 'integer':
                $definition = "integer";
                break;
            case 'float':
                $definition = "double precision";
                break;
            default:
                $definition = "character varying(255)";
                break;
        }
        return $definition;
    }
}
