<?php

namespace App\Console\Commands;

use App\Models\LineitemManager;
use App\Models\SpeedBatch;
use App\Models\SpeedDynamic;
use App\Services\RowParser;

class TestParser extends BaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-parser {lineitem_manager_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test all different condition parser';


    protected $lineitem_manager_id = null;
    /**
     * Execute the console command.
     */
    public function handle()
    {
        //$this->testHex();
        $this->lineitem_manager_id = $this->argument('lineitem_manager_id');

        if ($this->lineitem_manager_id) {
            $this->parseLineitem();
        } else {
            $this->sandbox();
        }
    }

    private function sandbox()
    {
        $parser = new RowParser(true); // Enable debugging
        // Math
        // $parser->addExpression('tripled', "MUL([value],3)");
        // $parser->addExpression('Sum', "IF(SUM(value,3) > 102, 'MORE THAN 102', 'LESS THAN 102')");
        // // Text
        // $parser->addExpression('concat', "CONCAT(food,' = ',status, ' = ', items)");
        // $parser->addExpression('replaced', "REPLACE(food, 'AA', 'WAGYU A5')");
        // // //Smaller
        // $parser->addExpression('smaller', "IF(LENGTH([value])<2, 'SMALL', 'LARGE')");
        // // Greater than or equal to
        // $parser->addExpression('Greater Equal', "IF([value] >= 100, 'High', 'Low')");

        // // Less than or equal to
        // $parser->addExpression('Less Equal', "IF([value]<=50, 'Low', 'HIGH')");

        // // // OR conditions (both syntaxes work)
        // $parser->addExpression('Mix IF OR', "IF([value] >= 100 || [status] != 'basic', 'VIP', 'PEASANT')");
        // $parser->addExpression('OR Conditions 2', "IF(value <= 50 OR status == 'basic', 'Basic', 'Premium')");
        // // IF with two conditions
        // $parser->addExpression('Mix IF AND', "IF([value] >= 100 && [status] != 'basic', 'Special', 'Normal')");
        // // Return one column value
        // $parser->addExpression('specific_column', "[items]");
        // //$parser->addExpression('MIX MATH', "SUB(SUM(value,10),100)");
        // $parser->addExpression('SUBSTR', "IF(SUBSTR([food],1,2) == 'A3', IF([value] == 6, '0.78', IF([value] == 7, '0.75', '')), IF(SUBSTR([food],1,2) == 'A5', IF([value] == 4, '0.8', IF([value] == 5, '0.99', IF([value] == 6, '0.75', '0.8')))))");
        // $parser->addExpression('CONCAT WITH SINGLEQUOTE', 'CONCAT([food]," ","int(\'", [status], "\')"," " , [items])'); //Expected output: AA3IB('premium')99
        //$parser->addExpression('LiveSUB', "if(SUBSTR([PREVIOUS REFERENCE ID],4,1)== B ,'111','000')");
        $parser->addExpression('LiveSUBS', "SUBSTR([PREVIOUS REFERENCE ID],4,1)");
        // $parser->addExpression('LONG', "IF([POWER GRADE] == 'FV','00001',IF([CORE SPEED RATIO] == 1, '10000',IF([CORE SPEED RATIO] == 2, '01000',IF([CORE SPEED RATIO] == 3, '00100',IF([CORE SPEED RATIO] == 4, '00010',999)))))");

        // Test cases
        $tests = [
            ['value' => 6, 'food' => 'AA3IB', 'status' => 'premium', 'items' => 99], // Should return 0.78
            ['value' => 7, 'food' => 'AA3IB', 'status' => 'premium', 'items' => 99], // Should return 0.75
            ['value' => 4, 'food' => 'AA5IB', 'status' => 'premium', 'items' => 99], // Should return 0.8
            ['value' => 5, 'food' => 'AA5IB', 'status' => 'premium', 'items' => 99], // Should return 0.99
            ['value' => 6, 'food' => 'AA5IB', 'status' => 'premium', 'items' => 99], // Should return 0.75
            ['value' => 100, 'food' => 'AA5IB', 'status' => 'premium', 'items' => 99, 'PREVIOUS REFERENCE ID' => 'AGIX041R29D1E2V', 'CORE SPEED RATIO' => "2"],
            ['value' => 100, 'food' => 'AA5IB', 'status' => 'premium', 'items' => 99, 'PREVIOUS REFERENCE ID' => 'AGIB041R29D1E2V', 'CORE SPEED RATIO' => "3"]

        ];

        foreach ($tests as $index => $row) {
            $result = $parser->parseRow($row);
            foreach ($result as $field => $r) {
                $this->comment("Test " . ($index + 1) . ": " . $field . " -> " . ($r ?? 'null'));
            }
        }

        $check = 'XX([food])';
        $this->comment("Check: " . $parser->isExpression($check));
    }


    private function parseLineitem()
    {
        $line = LineitemManager::find($this->lineitem_manager_id);

        try {
            //Start populate data
            $batch = SpeedBatch::find($line->speed_batch_id);
            $data = $batch->getBasedModal()->orderBy('id')->get();

            foreach ($data as $d) {
                $parser = new RowParser;
                foreach ($line->lineitemAttributes as $attribute) {
                    if (!$attribute->active || empty($attribute->expression)) continue;
                    $parser->addExpression($attribute->column_name, $attribute->expression, true);
                }
                $result = $parser->parseRow($d->toArray());
                $insert_row = [];
                foreach ($result as $column => $value) {
                    $insert_row[$column] = $value;
                }

                $this->comment(print_r($d->toArray(), true));
                $this->comment(print_r($insert_row, true));
            }


            return true;
        } catch (\Exception $e) {
            $this->comment($e->getMessage());
            return false;
        }
    }

    private function testHex()
    {
        $hex1 = binToHexWithPack('01000011010011001100000011011101'); //434CC0DD
        $hex2 = binToHexWithPack('01000011010010110100000011011101'); //434B40DD
        $hex3 = binToHexWithPack('01010011010011000100000011011101'); //534C40DD
        $hex4 = binToHexWithPack('00000011010011001100000011011101'); //034CC0DD
        dd($hex1, $hex2, $hex3, $hex4);
    }
}
