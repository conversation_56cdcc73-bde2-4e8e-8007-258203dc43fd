<?php

namespace App\Models;

use App\Traits\ModuleModelTrait;
use Illuminate\Support\Facades\Schema;

/**
 * Load using View Table
 */
class SpeedDynamic extends BaseModel
{
    use ModuleModelTrait;

    protected $table = 'speed'; // Fallback table name

    public function getTable()
    {
        return $this->table;
    }

    // Optional: Allow setting a custom table name dynamically
    public function setTable($table)
    {
        $this->table = $table;
        return $this;
    }

    public function getTableColumns()
    {
        $excludedColumns = ['id', 'created_at', 'updated_at', 'deleted_at', 'batch_id'];
        $columns = Schema::getColumnListing($this->getTable());
        return array_values(array_diff($columns, $excludedColumns));
    }

    function getColumnsWithDataTypes()
    {
        $table = $this->getTable();
        $columns = Schema::getColumnListing($table);
        $columnTypes = [];

        foreach ($columns as $column) {
            $columnTypes[$column] = Schema::getColumnType($table, $column);
        }

        return $columnTypes;
    }

    public function speedBatch()
    {
        return $this->belongsTo(SpeedBatch::class);
    }
}
