<script setup>
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, router, useForm, Link } from '@inertiajs/vue3';
import { ref, computed, watch, onMounted } from 'vue';
import Select from '@/Components/Select.vue';
import axios from 'axios';
import ProductSelectors from '@/Components/ProductSelectors.vue';
import { formatDate } from '@/helper';

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    productTypes: {
        type: Object,
    },
    products: {
        type: Object,
    },
    productGroups: {
        type: Object,
    },
    binMatrices: {
        type: Object,
    },
    createRevision: {
        type: Boolean,
    },
    states: {
        type: Array,
    }
});

const routeGroupName = 'fuse_managers';
const headerTitle = ref('Fuse Config Manager');
const routeFuseConfig = 'fuse_configs';

const localData = ref({ ...props.data });

onMounted(() => {
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
});

watch(
    () => props.data,
    newVal => {
        localData.value = { ...newVal };
    },
);

const form = useForm({
    create_revision: props.createRevision,
    revision: props.data.revision,
    name: props.data.name ?? null,
    product_type_id: props.data.product_type_id ?? null,
    product_id: props.data.product_id ?? null,
    product_group_id: props.data.product_group_id ?? null,
    bin_matrix_id: props.data.bin_matrix_id ?? null,
});

const isReadOnly = computed(() => {
    return props.createRevision ? true : false;
});

const editDisabled = computed(() => {
    return localData.value.state == 'SNAPSHOT';
});


const setState = state => {
    const c = confirm('Change the state to ' + state);
    if (c) {
        axios
            .post(route(routeGroupName + '.state.update', props.data.id), { state: state })
            .then(response => {
                localData.value.state = state;
                alert(response.data.message);
            })
            .catch(error => {
                alert(error.response.data.message);
            });
    }
};

const submit = () => {
    if (props.data.id == null) {
        form.post(route(routeGroupName + '.store'), { preserveState: true });
    } else {
        form.patch(route(routeGroupName + '.update', props.data.id), { preserveState: true });
    }
};

const destroy = (id, name) => {
    const c = confirm(`Delete this Fuse Config ${name} ?`);
    if (c) {
        router.delete(route(routeFuseConfig + '.destroy', id));
    }
};

const filteredBinMatrices = computed(() => {
    if (!form.product_group_id) {
        return []; // Return empty array if no product group selected
    }
    // Filter bin matrices to only show those matching the selected product group
    return props.binMatrices.filter(matrix => matrix.product_group_id === parseInt(form.product_group_id));
});
</script>

<template>

    <Head :title="headerTitle" />

    <AuthenticatedLayout>
        <template #header> {{ headerTitle }} </template>
        <span v-if="props.createRevision">(New Revision {{ props.data.revision }})</span>
        <form @submit.prevent="submit">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <ul class="nav nav-tabs card-header-tabs">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#tab_1">Details</a>
                        </li>
                    </ul>
                    <div v-if="data.id != null && props.createRevision == false" class="d-flex gap-2">
                        <div class="dropdown">
                            <button class="btn btn-sm btn-info dropdown-toggle" type="button" data-bs-toggle="dropdown"
                                aria-expanded="false"><i class="bi bi-check-circle"></i> {{ localData.state }}</button>
                            <ul class="dropdown-menu dropdown-menu">
                                <li v-for="state in states" :key="state">
                                    <button type="button" class="dropdown-item" @click="setState(state)"
                                        :disabled="state == localData.state">{{ state }}</button>
                                </li>
                            </ul>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-info dropdown-toggle" type="button" data-bs-toggle="dropdown"
                                aria-expanded="false">
                                <i class="bi bi-file-earmark-arrow-down"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu">
                                <li>
                                    <a class="dropdown-item" :href="route(routeGroupName + '.fuse_def', data.id)">Fuse
                                        Definition</a>
                                </li>
                                <li>
                                    <a class="dropdown-item"
                                        :href="route(routeGroupName + '.fuse_sspec', data.id)">SSPEC</a>
                                </li>
                                <!-- <li>
                                    <hr class="dropdown-divider" />
                                </li>
                                <li>
                                    <Link class="dropdown-item" :href="route(routeGroupName + '.revision.create', data.id)"> Create New Revision</Link>
                                </li> -->
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <div class="tab-pane fade pt-10 show active" id="tab_1" role="tabpanel" aria-labelledby="tab_1">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <InputLabel for="name" value="Name" />
                                    <TextInput id="name" type="text" v-model="form.name" :invalid="form.errors.name"
                                        required />
                                    <InputError :message="form.errors.name" />
                                </div>

                                <ProductSelectors v-model="form" :product-types="productTypes" :products="products"
                                    :product-groups="productGroups" :errors="form.errors" />

                                <div class="col-md-3">
                                    <InputLabel for="bin_matrix_id" value="Bin Matrix" />
                                    <Select id="bin_matrix_id" v-model="form.bin_matrix_id"
                                        :invalid="form.errors.bin_matrix_id" :options="filteredBinMatrices"
                                        :placeholder="'Select Bin Matrix'" :label_key="'label'" required />
                                    <InputError :message="form.errors.bin_matrix_id" />
                                </div>
                            </div>

                            <div class="row g-3 mt-2">
                                <div class="col-12">
                                    <div class="text-end">
                                        Revision: {{ data.revision }} <br />
                                        <template v-if="data.created_user">Created By: {{ data.created_user.name }}
                                        </template>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-end align-items-center my-2">
                                <div class="d-flex align-items-center gap-2">
                                    <!-- Create illusion cannot press, since Link not able to do disabled -->
                                    <template v-if="data.id == null">
                                        <button class="btn btn-primary" disabled>
                                            <i class="bi bi-plus"></i>
                                            Add
                                        </button>

                                        <button class="btn btn-primary" disabled>
                                            <i class=" bi bi-filetype-xls"></i>
                                            Import
                                        </button>
                                    </template>
                                    <template v-else>
                                        <Link class="btn btn-primary"
                                            :href="route(routeFuseConfig + '.create') + '?fuse_manager_id=' + data.id">
                                        <i class="bi bi-plus"></i>
                                        Add
                                        </Link>

                                        <Link class="btn btn-primary"
                                            :href="route(routeFuseConfig + '.import') + '?fuse_manager_id=' + data.id">
                                        <i class=" bi bi-filetype-xls" :disabled="data.id == null"></i>
                                        Import
                                        </Link>
                                    </template>
                                </div>
                            </div>

                            <div class="row g-3 mt-3">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th width="8%">
                                                Actions
                                            </th>
                                            <th>Name</th>
                                            <th>Created At</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="item in data.fuse_configs">
                                            <td>
                                                <Link :href="route(routeFuseConfig + '.edit', item.id)"
                                                    class="btn btn-sm btn-link">
                                                <i class="bi bi-pencil"></i>
                                                </Link>
                                                <button @click="destroy(item.id, item.name)"
                                                    class="btn btn-sm btn-link">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </td>
                                            <td>{{ item.name }}</td>
                                            <td>{{ formatDate(item.created_at) }}</td>
                                        </tr>

                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex">
                        <div class="me-auto">
                            <Link class="btn btn-secondary me-2" :href="route(routeGroupName + '.index')">Back</Link>
                            <PrimaryButton v-if="!editDisabled" type="submit"
                                v-html="data.id == null ? 'Create' : 'Save'" :disabled="form.processing">
                            </PrimaryButton>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </AuthenticatedLayout>
</template>
