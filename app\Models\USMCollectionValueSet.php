<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class USMCollectionValueSet extends BaseModel
{
    use SoftDeletes;

    protected $table = 'usm_collection_value_sets';

    protected $fillable = [
        'name',
        'usm_collection_id',
        'usm_id',
        'sequence',
    ];

    /**
     * The collection this value set belongs to
     */
    public function collection()
    {
        return $this->belongsTo(USMCollection::class, 'usm_collection_id');
    }

    /**
     * The USM this value set belongs to
     */
    public function usm()
    {
        return $this->belongsTo(USM::class, 'usm_id');
    }

    /**
     * The values in this value set
     */
    public function values()
    {
        return $this->hasMany(USMCollectionValue::class, 'usm_collection_value_set_id');
    }
}
