<?php

namespace App\Http\Requests;

use App\Models\LineitemManager;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class LineItemManagerUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [];
        return  array_merge($rules, [
            //'create_revision' => ['boolean'], //<PERSON> is trying to create a new revision now
            //Able to save same name as long as revision is different
            'name' => ['string', 'max:255', Rule::unique(LineitemManager::class)->ignore($this->lineitem_manager)->where('speed_batch_id', $this->input('speed_batch_id'))],
            'module' => ['string', 'max:255'],
            // 'product_type_id' => ['numeric', 'max:255'],
            // 'product_id' => ['numeric', 'max:255'],
            // 'product_group_id' => ['numeric', 'max:255'],
            'speed_batch_id' => ['required'],
            'active' => ['boolean'],
            'attributes' => ['array'],
        ]);
    }
}
