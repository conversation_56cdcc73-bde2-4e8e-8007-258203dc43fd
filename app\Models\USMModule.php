<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class USMModule extends BaseModel
{
    use SoftDeletes;

    protected $table = 'usm_modules';

    public $fillable = [
        'name',
        'keyword',
        'sequence',
        'active',
        'usm_id',
        'scenario_mode'
    ];

    protected $attributes = [
        'active' => true,
    ];

    public function usm()
    {
        return $this->belongsTo(USM::class, 'usm_id');
    }

    public function componentTypes()
    {
        return $this->hasMany(USMModuleComponentType::class, 'usm_module_id');
    }
}
