<script setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';

const props = defineProps({
  batchId: {
    type: Number,
    required: true
  }
});

const routeGroupName = 'speedbatch';
const columns = ref({});

onMounted(async () => {
  try {
    const res = await axios.get(route(routeGroupName + '.format.get', props.batchId)) 
    const originalColumns = res.data

    columns.value = Object.entries(originalColumns).map(([name, type]) => ({
      name,
      type
    }))
  } 
  catch (error) {
    console.error('Failed to load columns:', error)
    alert('Failed to load columns.')
  }
})

// Simulate submission
function submit() {
  console.log('Simulated submit payload:', columns.value.value)
  alert('Submitted! Check console to see the updated columns.')
}
</script>

<template>
  <div class="px-5">
    <form @submit.prevent="submit">
      <table class="table table-bordered"> 
        <thead>
          <tr>
            <th>Column Name</th>
            <th>Data Type</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(col, index) in columns" :key="index">
            <td>
              <input
                type="text"
                class="form-control"
                v-model="col.name"
                required
              />
            </td>
            <td>
              <select class="form-select" v-model="col.type" required>
                <option value="String">String</option>
                <option value="Integer">Integer</option>
                <option value="Float/Exponential">Float</option>
              </select>
            </td>
          </tr>
        </tbody>
      </table>

      <button type="submit" class="btn btn-primary mt-3"> Save </button>
    </form>
  </div>
</template>


