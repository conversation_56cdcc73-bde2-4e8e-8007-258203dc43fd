<script setup>

const props = defineProps({
    headers: {
        type: Object,
    },
    differences: {
        type: [Number, Array],
    },
});
</script>

<template>
    <div class="table-responsive">
        <table v-show="headers != null" class="table table-bordered comparison-table">
            <thead>
                <tr>
                    <th rowspan="2" class="index-header">#</th>
                    <th v-for="header in headers" :key="header" colspan="2" class="header-cell text-center">{{ header }}
                    </th>
                </tr>
                <tr class="subheader-row">
                    <template v-for="header in headers" :key="header">
                        <th class="subheader-cell">Existing</th>
                        <th class="subheader-cell">New</th>
                    </template>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(diff, index) in differences" :key="index">
                    <td>{{ Number(index) + 1 }}</td>
                    <template v-if="diff.status">
                        <td :colspan="headers.length * 2" class="text-center bg-warning">
                            {{ diff.status }}
                        </td>
                    </template>
                    <template v-else>
                        <template v-for="header in headers" :key="header">
                            <template v-if="diff[header] == null && diff[header] == null">
                                <td width="10%">[SAME]</td>
                                <td width="10%">[SAME]</td>
                            </template>
                            <template v-else>
                                <td width="10%">{{ diff[header]?.row1 || '[EMPTY]' }}</td>
                                <td width="10%">{{ diff[header]?.row2 || '[EMPTY]' }}</td>
                            </template>
                        </template>
                    </template>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<style>
/* Enable horizontal and vertical scrolling while maintaining dropdown visibility */
.table-responsive {
    overflow-x: auto;
    overflow-y: auto;
    min-height: 0.01%;
    /* Fix for IE11 */
    max-height: 70vh;
    /* Set a maximum height to enable vertical scrolling (70% of viewport height) */
    position: relative;
    /* Required for sticky positioning to work */
}

/* Set minimum width for the comparison table to ensure it's scrollable */
.comparison-table {
    min-width: 100%;
    width: auto;
    border-collapse: separate;
    /* Important for sticky positioning */
    border-spacing: 0;
    /* Remove gaps between cells */
}

/* When there are many columns, ensure the table is wide enough */
@media (min-width: 768px) {
    .comparison-table {
        min-width: 150%;
    }
}

/* For very wide tables on larger screens */
@media (min-width: 1200px) {
    .comparison-table {
        min-width: 200%;
    }
}

/* Ensure the first column (index column) stays fixed */
.comparison-table thead tr th:first-child,
.comparison-table tbody tr td:first-child {
    position: sticky;
    left: 0;
    background-color: #fff;
    z-index: 1;
    border-right: 1px solid #dee2e6;
}

/* Add border to header rows */
.comparison-table thead tr th {
    border-bottom: 2px solid #dee2e6;
}

/* Make the header rows sticky */
.comparison-table thead th {
    position: sticky;
    background-color: #f8f9fa;
    z-index: 1;
}

/* First row of headers */
.comparison-table thead tr:first-child th {
    top: 0;
    text-align: center;
    height: 43px;
}

/* Second row of headers */
.comparison-table thead tr.subheader-row th {
    top: 43px;
    /* Height of the first row */
    background-color: #e9ecef;
    /* Slightly darker background */
    text-align: center;
    height: 38px;
}

/* Style for the index header */
.comparison-table th.index-header {
    background-color: #f8f9fa;
    z-index: 2;
    /* Higher z-index for the intersection */
}

/* Style for the subheader cells */
.comparison-table th.subheader-cell {
    font-weight: normal;
}

/* Set consistent widths for columns */
.comparison-table td,
.comparison-table th {
    width: 10%;
    min-width: 100px;
}

/* First column width */
.comparison-table th:first-child,
.comparison-table td:first-child {
    width: 5%;
    min-width: 50px;
}

/* Add a subtle shadow to indicate scrolling */
.comparison-table thead tr th {
    box-shadow: 0 2px 2px -1px rgba(0, 0, 0, 0.1);
}

/* Style for the fixed column shadow */
.comparison-table tbody tr td:first-child {
    box-shadow: 2px 0 2px -1px rgba(0, 0, 0, 0.1);
}

/* Style for the corner cell (intersection of fixed header and column) */
.comparison-table thead tr th:first-child {
    box-shadow: 2px 2px 3px -1px rgba(0, 0, 0, 0.15);
}

/* Add some padding to cells for better readability */
.comparison-table th,
.comparison-table td {
    padding: 8px 12px;
    white-space: nowrap;
}

/* Ensure consistent column widths */
.comparison-table th,
.comparison-table td {
    box-sizing: border-box;
}

/* Add borders to help visualize column boundaries */
.comparison-table th,
.comparison-table td {
    border: 1px solid #dee2e6;
}

/* Make the first header row cells taller to accommodate the text */
.comparison-table thead tr:first-child th {
    height: 43px;
}

/* Ensure the first column has a consistent width */
.comparison-table th:first-child,
.comparison-table td:first-child {
    width: 5%;
    min-width: 40px;
    text-align: center;
}

/* Highlight different values */
.comparison-table tbody tr td:nth-child(odd):not(:first-child) {
    background-color: rgba(0, 0, 0, 0.02);
}
</style>