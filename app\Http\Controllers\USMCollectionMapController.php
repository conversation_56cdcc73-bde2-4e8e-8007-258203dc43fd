<?php

namespace App\Http\Controllers;

use App\Models\Component;
use App\Models\USM;
use App\Models\USMCollection;
use App\Models\USMCollectionMap;
use App\Models\USMCollectionCondition;
use App\Models\USMCollectionAssignment;
use App\Models\USMCollectionAssignmentValueSet;
use App\Models\USMModuleComponentAttribute;
use App\Models\USMScenario;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;

class USMCollectionMapController extends Controller
{
    /**
     * Show the form for editing the specified collection map.
     */
    public function edit(Request $request)
    {
        $usm_id = $request->input('usm_id');
        $usm_module_id = $request->input('usm_module_id');
        $scenario_mode = $request->input('scenario_mode');
        // $scenario_id = $request->input('scenario_id');

        // if ($scenario_id != null) {
        //     $scenario = USMScenario::find($scenario_id);
        // }

        $scenarios = null;
        if ($scenario_mode == XML_SCENARIO) {
            $scenarios = USMScenario::where('usm_id', $usm_id)->get();
        }

        //Get Collections
        $collections = USMCollection::with('valueSets')->where('scenario_mode', $scenario_mode)
            ->where('usm_module_id', $usm_module_id)
            ->where('usm_id', $usm_id)->get();

        //Get available Component
        $attributes = USMModuleComponentAttribute::where('usm_id', $usm_id)->where('usm_module_id', $usm_module_id)->where('scenario_mode', $scenario_mode)->get();
        $components = Component::where('usm_id', $usm_id)
            ->whereIn('component_level_id', $attributes->pluck('component_level_id')->unique())
            ->whereIn('component_type_id', $attributes->pluck('component_type_id')->unique())
            ->get();

        // Find existing collection map or prepare a new one
        $collectionMap = USMCollectionMap::where('usm_id', $usm_id)
            ->where('usm_module_id', $usm_module_id)
            ->where('scenario_mode', $scenario_mode)
            // ->when($scenario_mode === XML_SCENARIO, function ($query) use ($scenario_id) {
            //     return $query->where('usm_scenario_id', $scenario_id);
            // })
            // ->when($scenario_mode === XML_NONSCENARIO, function ($query) {
            //     return $query->whereNull('usm_scenario_id');
            // })
            ->with(['conditions', 'conditions.assignments'])
            ->first();

        if (!$collectionMap) {
            return response()->json([
                'usm_collection_map_id' => null,
                'name' => '',
                'conditions' => [],
                'component_options' => multiselect_options($components, 'id', 'name'),
                'collections' => $collections,
                'scenario_options' => $scenarios != null ? multiselect_options($scenarios, 'id', 'name') : [],
            ]);
        }

        // Transform the data structure to match frontend expectations
        $conditions = $collectionMap->conditions->map(function ($condition) {
            // Load scenario assignments for this condition with their value sets
            $scenarioAssignments = USMCollectionAssignment::where('usm_collection_condition_id', $condition->id)
                ->with('valueSets') // Eager load value sets 
                ->orderBy('sequence')
                ->get()
                ->filter(function ($assignment) {
                    // Filter to include only assignments for scenarios (has collection_id)
                    return !empty($assignment->collection_id);
                })
                ->map(function ($assignment) {
                    return [
                        'id' => $assignment->id,
                        'collection_id' => $assignment->collection_id,
                        'component_ids' => $assignment->component_ids,
                        'sequence' => $assignment->sequence,
                        'value_sets' => $assignment->valueSets->map(function ($valueSet) {
                            return [
                                'value_set_id' => $valueSet->value_set_id,
                                'scenario_ids' => $valueSet->scenario_ids
                            ];
                        })->values()->toArray()
                    ];
                })->values()->toArray();

            // Get regular assignments (non-scenario)
            $regularAssignments = $condition->assignments->map(function ($assignment) {
                return [
                    'id' => $assignment->id,
                    'collection_id' => $assignment->collection_id,
                    'value_set_ids' => $assignment->valueSets->pluck('value_set_id')->toArray(),
                    'component_ids' => $assignment->component_ids,
                    'sequence' => $assignment->sequence,
                ];
            })->values()->toArray();

            return [
                'id' => $condition->id,
                'comment' => $condition->comment,
                'expression' => $condition->expression,
                'sequence' => $condition->sequence,
                'assignments' => $regularAssignments,
                'scenario_assignments' => $scenarioAssignments
            ];
        })->values()->toArray();

        return response()->json([
            'usm_collection_map_id' => $collectionMap->id,
            'name' => $collectionMap->name,
            'linked' => $collectionMap->linked,
            'conditions' => $conditions,
            'component_options' => multiselect_options($components, 'id', 'name'),
            'collections' => $collections,
            'scenario_options' => $scenarios != null ? multiselect_options($scenarios, 'id', 'name') : [],
        ]);
    }

    /**
     * Update the specified collection map in storage.
     */
    public function update(Request $request)
    {
        $usm_collection_map_id = $request->input('usm_collection_map_id');
        $usm_id = $request->input('usm_id');
        $usm_module_id = $request->input('usm_module_id');
        $scenario_mode = $request->input('scenario_mode');
        // $scenario_id = $request->input('usm_scenario_id');
        $name = $request->input('name');
        $conditions = $request->input('conditions', []);
        $linked = $request->input('linked', false);

        DB::beginTransaction();
        try {

            if ($usm_collection_map_id == null) {
                $collectionMap = USMCollectionMap::create([
                    'usm_id' => $usm_id,
                    'usm_module_id' => $usm_module_id,
                    'scenario_mode' => $scenario_mode,
                    'name' => $name,
                    'linked' => $linked,
                ]);
            } else {
                $collectionMap = USMCollectionMap::findOrFail($usm_collection_map_id);
                $collectionMap->name = $name;
                $collectionMap->linked = $linked;
                $collectionMap->save();
            }

            // Track all existing and new condition IDs
            $existingConditionIds = $collectionMap->conditions()->pluck('id')->toArray();
            $newConditionIds = [];

            // Create or update conditions and assignments
            foreach ($conditions as $index => $conditionData) {
                // Create or update the condition
                $conditionId = $conditionData['id'] ?? null;
                $keepAssignmentIds = []; // Track assignments to keep for this condition

                if ($conditionId) {
                    // Update existing condition
                    $condition = USMCollectionCondition::find($conditionId);
                    if ($condition) {
                        $condition->comment = $conditionData['comment'];
                        $condition->expression = $conditionData['expression'];
                        $condition->sequence = $index + 1;
                        $condition->save();
                        $newConditionIds[] = $condition->id;
                    } else {
                        // ID was provided but not found, create new
                        $condition = new USMCollectionCondition([
                            'usm_id' => $usm_id,
                            'comment' => $conditionData['comment'],
                            'expression' => $conditionData['expression'],
                            'sequence' => $index + 1,
                        ]);
                        $collectionMap->conditions()->save($condition);
                        $newConditionIds[] = $condition->id;
                    }
                } else {
                    // Create new condition
                    $condition = new USMCollectionCondition([
                        'usm_id' => $usm_id,
                        'comment' => $conditionData['comment'],
                        'expression' => $conditionData['expression'],
                        'sequence' => $index + 1,
                    ]);
                    $collectionMap->conditions()->save($condition);
                    $newConditionIds[] = $condition->id;
                }

                if ($scenario_mode === XML_NONSCENARIO) {
                    // Track assignment IDs to keep for this condition
                    $keepAssignmentIds = [];

                    if (!empty($conditionData['assignments'])) {
                        foreach ($conditionData['assignments'] as $assignmentIndex => $assignmentData) {
                            $assignmentId = $assignmentData['id'] ?? null;

                            if ($assignmentId) {
                                // Update existing assignment
                                $assignment = USMCollectionAssignment::find($assignmentId);
                                if ($assignment) {
                                    $assignment->collection_id = $assignmentData['collection_id'];
                                    // Don't store value_set_ids directly anymore
                                    $assignment->component_ids = $assignmentData['component_ids'];
                                    $assignment->sequence = $assignmentIndex + 1;
                                    $assignment->save();
                                    $keepAssignmentIds[] = $assignment->id;
                                } else {
                                    // ID was provided but not found, create new
                                    $assignment = new USMCollectionAssignment($assignmentData);
                                    $condition->assignments()->save($assignment);
                                    $keepAssignmentIds[] = $assignment->id;
                                }
                            } else {
                                // Create new assignment
                                $assignment = new USMCollectionAssignment([
                                    'collection_id' => $assignmentData['collection_id'],
                                    'component_ids' => $assignmentData['component_ids'],
                                    'sequence' => $assignmentIndex + 1,
                                ]);
                                $condition->assignments()->save($assignment);
                                $keepAssignmentIds[] = $assignment->id;
                            }

                            // Delete existing value sets for this assignment
                            $assignment->valueSets()->delete();

                            // Process value sets
                            if (!empty($assignmentData['value_set_ids'])) {
                                foreach ($assignmentData['value_set_ids'] as $valueSetId) {
                                    $assignmentValueSet = new USMCollectionAssignmentValueSet([
                                        'usm_collection_assignment_id' => $assignment->id,
                                        'value_set_id' => $valueSetId,
                                    ]);
                                    $assignment->valueSets()->save($assignmentValueSet);
                                }
                            }
                        }

                        // Delete assignments that were removed (not in the current request)
                        if ($condition->id) {
                            $existingAssignmentIds = USMCollectionAssignment::where('usm_collection_condition_id', $condition->id)
                                ->whereNull('collection_id') // Regular (non-scenario) assignments
                                ->pluck('id')
                                ->toArray();

                            $assignmentsToDelete = array_diff($existingAssignmentIds, $keepAssignmentIds);

                            if (!empty($assignmentsToDelete)) {
                                // First delete associated value sets
                                USMCollectionAssignmentValueSet::whereIn('usm_collection_assignment_id', $assignmentsToDelete)->delete();

                                // Then delete the assignments
                                USMCollectionAssignment::whereIn('id', $assignmentsToDelete)->delete();
                            }
                        }
                    }
                }

                if ($scenario_mode === XML_SCENARIO) {
                    // Track assignment IDs to keep for this condition
                    $keepScenarioAssignmentIds = [];

                    if (!empty($conditionData['scenario_assignments'])) {
                        foreach ($conditionData['scenario_assignments'] as $assignmentIndex => $assignmentData) {
                            $assignmentId = $assignmentData['id'] ?? null;

                            if ($assignmentId) {
                                // Update existing assignment
                                $assignment = USMCollectionAssignment::find($assignmentId);
                                if ($assignment) {
                                    $assignment->collection_id = $assignmentData['collection_id'];
                                    $assignment->component_ids = $assignmentData['component_ids'];
                                    $assignment->sequence = $assignmentIndex + 1;
                                    $assignment->save();
                                    $keepScenarioAssignmentIds[] = $assignment->id;
                                } else {
                                    // ID was provided but not found, create new
                                    $assignment = new USMCollectionAssignment([
                                        'collection_id' => $assignmentData['collection_id'],
                                        'component_ids' => $assignmentData['component_ids'],
                                        'sequence' => $assignmentIndex + 1,
                                    ]);
                                    $condition->assignments()->save($assignment);
                                    $keepScenarioAssignmentIds[] = $assignment->id;
                                }
                            } else {
                                // Create new assignment
                                $assignment = new USMCollectionAssignment([
                                    'collection_id' => $assignmentData['collection_id'],
                                    'component_ids' => $assignmentData['component_ids'],
                                    'sequence' => $assignmentIndex + 1,
                                ]);
                                $condition->assignments()->save($assignment);
                                $keepScenarioAssignmentIds[] = $assignment->id;
                            }

                            // Delete existing value sets for this assignment
                            $assignment->valueSets()->delete();

                            // Process value sets
                            if (!empty($assignmentData['value_sets'])) {
                                foreach ($assignmentData['value_sets'] as $valueSetData) {
                                    $assignmentValueSet = new USMCollectionAssignmentValueSet([
                                        'usm_collection_assignment_id' => $assignment->id,
                                        'value_set_id' => $valueSetData['value_set_id'],
                                        'scenario_ids' => $valueSetData['scenario_ids'] ?? [],
                                    ]);
                                    $assignment->valueSets()->save($assignmentValueSet);
                                }
                            }
                        }
                    }

                    // Delete scenario assignments that were removed (not in the current request)
                    if ($condition->id) {
                        $existingScenarioAssignmentIds = USMCollectionAssignment::where('usm_collection_condition_id', $condition->id)
                            ->whereNotNull('collection_id')
                            ->pluck('id')
                            ->toArray();

                        $scenarioAssignmentsToDelete = array_diff($existingScenarioAssignmentIds, $keepScenarioAssignmentIds);

                        if (!empty($scenarioAssignmentsToDelete)) {
                            // First delete associated value sets
                            USMCollectionAssignmentValueSet::whereIn('usm_collection_assignment_id', $scenarioAssignmentsToDelete)->delete();

                            // Then delete the assignments
                            USMCollectionAssignment::whereIn('id', $scenarioAssignmentsToDelete)->delete();
                        }
                    }
                }
            }

            // Delete conditions that are no longer in the request
            $conditionsToDelete = array_diff($existingConditionIds, $newConditionIds);
            if (!empty($conditionsToDelete)) {
                // Delete all assignments for these conditions
                USMCollectionAssignment::whereIn('usm_collection_condition_id', $conditionsToDelete)->delete();

                // Delete the conditions
                USMCollectionCondition::whereIn('id', $conditionsToDelete)->delete();
            }

            DB::commit();
            return Redirect::back()->with(['message' => 'Collection map ' . $name . ' saved successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withErrors(['errors' => $e->getMessage()]);
        }
    }
}
