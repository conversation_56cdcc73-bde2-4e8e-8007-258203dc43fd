<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('usm_module_component_attributes', function (Blueprint $table) {
            $table->id();
            $table->string('attribute')->nullable(); //From ComplexTypeDetailXML
            $table->string('level')->nullable(); //Level
            $table->unsignedBigInteger('component_level_id')->nullable();
            $table->string('scenario_mode')->nullable(); //NonScenarioAttributes or ScenarioAttributes
            $table->boolean('is_complex')->default(false);
            $table->string('name')->nullable(); //Attribute original name
            $table->string('parameter')->nullable(); //ComplexValue name
            $table->string('datatype')->nullable(); //type: in the xml (string,float)
            $table->string('units')->nullable(); //V,unitless,undefined
            $table->string('description')->nullable();
            $table->string('complex_description')->nullable();
            $table->unsignedBigInteger('component_type_id')->nullable();
            $table->unsignedBigInteger('usm_module_id')->nullable();
            $table->unsignedBigInteger('usm_id');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('usm_module_component_attributes');
    }
};
