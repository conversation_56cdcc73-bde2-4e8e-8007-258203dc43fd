<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('usm_collection_value_sets', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->smallInteger('sequence')->default(0);
            $table->unsignedBigInteger('usm_collection_id');
            $table->unsignedBigInteger('usm_id');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('usm_collection_value_sets');
    }
};
