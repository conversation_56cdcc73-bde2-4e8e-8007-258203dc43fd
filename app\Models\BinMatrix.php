<?php

namespace App\Models;

use App\Traits\RevisionTrait;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class BinMatrix extends BaseModel
{
    use SoftDeletes;
    use RevisionTrait;

    protected $table = 'bin_matrix';

    public $fillable = [
        'name',
        'state',
        'product_type_id',
        'product_id',
        'product_group_id',
        'speed_batch_id',
        'created_user_id',
        'active',
        'revision',
        'is_current',
        'previous_revision_id',
        'original_id',
        'state_update_user_id',
        'state_updated_at',
    ];

    protected $attributes = [
        'active' => true,
        'is_current' => true,
        'revision' => 0, //default
        'state' => STATE_WIP
    ];

    public function productType(): BelongsTo
    {
        return $this->belongsTo(ProductType::class, 'product_type_id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function productGroup(): BelongsTo
    {
        return $this->belongsTo(ProductGroup::class, 'product_group_id');
    }

    public function speedBatch(): BelongsTo
    {
        return $this->belongsTo(SpeedBatch::class, 'speed_batch_id');
    }

    public function createdUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_user_id');
    }

    public function binMatrixItems(): HasMany
    {
        return $this->hasMany(BinMatrixItem::class, 'bin_matrix_id')->orderBy('sequence');
    }

    public function fuseConfigs(): HasMany
    {
        return $this->hasMany(FuseConfig::class, 'bin_matrix_id');
    }

    //Mutator & Accessor
    public function label(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => $attributes['name'] . " (" . $attributes['revision'] . " - " . $attributes['state'] . ")",
        );
    }


    //Scope function Below
    // Scope to filter by user permissions
    public function scopeAccessibleBy($query, User $user)
    {
        $permissions = $user->getAllProductPermissions();

        return $query->where(function ($query) use ($permissions) {
            // Case 1: When there are specific product group permissions
            if (!empty($permissions['productGroups'])) {
                // Only allow specifically permitted product groups
                $query->whereIn('product_group_id', array_keys($permissions['productGroups']));
            } else {
                // Case 2: When no specific product group permissions
                if (!empty($permissions['products'])) {
                    $query->where(function ($q) use ($permissions) {
                        $q->whereIn('product_id', array_keys($permissions['products']));
                    });
                }

                if (!empty($permissions['productTypes'])) {
                    $query->where(function ($q) use ($permissions) {
                        $q->whereIn('product_type_id', array_keys($permissions['productTypes']));
                    });
                }
            }
        });
    }

    //Static Function Belows
    public static function header()
    {
        $headers = [];
        return array_merge($headers, [
            ['field' => 'name', 'title' => 'Name', 'sortable' => true],
            ['field' => 'state', 'title' => 'State', 'sortable' => true],
            ['field' => '', 'title' => 'Product Code Name', 'sortable' => false],
            ['field' => '', 'title' => 'Product Group', 'sortable' => false],
            ['field' => 'revision', 'title' => 'Revision', 'sortable' => true],
            ['field' => '', 'title' => 'Created By'],
            ['field' => 'created_at', 'title' => 'Created At'],
        ]);
    }

    public static function states()
    {
        return [STATE_SNAPSHOT];
    }
}
