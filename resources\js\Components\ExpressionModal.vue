<script setup>
const props = defineProps({
    buttonYes: { //Button Label
        type: String,
        default: 'Save'
    },
    buttonNo: {
        type: String,
        default: 'Close'
    }
});
</script>

<template>
    <teleport to="body">
        <div id="expressionModal" class="modal fade" tabindex="-1" aria-labelledby="expressionLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-scrollable modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5" id="expressionLabel">Expression Functions</h1>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <ul>
                            <li>
                                IF statement expression <br />
                                <code>==, !=, &gt, %lt, %gt=, %lt=, &&, ||</code><br />
                                <u>Example:</u> <br />
                                <code>IF([Pincount] >= 10, "Big", "Small")</code> <br />
                                <small><i>Returns "Big" if Pincount is greater than or equal to 10, otherwise returns
                                        "Small".</i></small> <br />
                                <code>IF([Pincount] > 100 && ([QDF/SSPEC] != 'xyxy'), [Pincount], 0)</code> <br />
                                <small><i>Return Pincount if Pincount is greater than 100 and QDF/SSPEC is not equal to
                                        'xyxy', otherwise return 0.</i></small> <br />
                            </li>
                            <li>
                                Math expression <br />
                                <code>SUM, SUB, MUL, DIV</code> <br />
                                <u>Example:</u> <br />
                                <code>SUM([Pincount],[TRANSCEIVER COUNT])</code> <br />
                                <small><i>Sum of two columns</i></small> <br />
                                <code>SUM([Pincount], 100)</code> <br />
                                <small><i>Pincount column + self define 100</i></small> <br />
                                <code>SUB(MUL([Pincount],10) ,100)</code> <br />
                                <small><i>Pincount column multiply by 10 and subtract 100</i></small><br />
                            </li>
                            <li>
                                String expression <br />
                                <code>CONCAT, SUBSTR, REPLACE</code> <br />
                                <u>Example:</u> <br />
                                <code>CONCAT([Pincount], " and ", [TRANSCEIVER COUNT])</code> <br />
                                <small><i>Concatenate multiple columns: "100 and 200"</i></small> <br />
                                <code>SUBSTR([column],start,length )</code> <br />
                                <small><i>Substring of a column, return string of length "length" starting from index
                                        "start". </i></small> <br />
                                <code>REPLACE([column], 'abc', 'def')</code> <br />
                                <small><i>Replace all 'abc' in column with 'def'</i></small>
                            </li>
                            <li>
                                LENGTH expression <br />
                                <code>LENGTH([column])</code> <br />
                                <small><i>Number of characters of a column</i></small><br />
                                <u>Example:</u> <br />
                                <code>IF(LENGTH([column])&lt=10, "SMALL", "LARGE")</code> <br />
                                <small><i>Return 'SMALL' if length of column is less than equal 10, otherwise return
                                        'LARGE'</i></small>
                            </li>
                            <li>
                                COLUMN expression <br />
                                <code>[Pincount]</code> <br />
                                <small><i>To get the value of a single column</i></small><br />
                            </li>
                        </ul>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ buttonNo }}</button>
                    </div>
                </div>
            </div>
        </div>
    </teleport>
</template>
