<script setup>
import InputError from '@/Components/InputError.vue';
import TextInput from '@/Components/TextInput.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TreeNode from '@/Components/TreeNode.vue';
import { useForm, Link } from '@inertiajs/vue3';
import { ref, onMounted, computed, watch } from 'vue';
import Select from '@/Components/Select.vue';
import FlashAlertWithErrors from '@/Components/FlashAlertWithErrors.vue';
import CollectionMapModal from './CollectionMapModal.vue';
import CollectionEditorModal from './CollectionEditorModal.vue';
import FlashAlert from '@/Components/FlashAlert.vue';
import ExpressionSuggestions from '@/Components/ExpressionSuggestions.vue';
import { ScenarioMode } from '@/enums';

const props = defineProps({
    usm_module_id: {
        type: Number,
    },
    usm_id: {
        type: Number,
    },
    scenarioOptions: {
        type: Object,
    },
    columns: {
        type: Array,
        default: () => [],
    },
    scenario_mode: {
        type: String,
        default: null,
    },
});

const tree = ref([]);
const maxLevel = ref(3);
const expandedNodes = ref(new Set());
const checkedNodes = ref(new Set());
const selectedComponent = ref(null);
const modeOptions = ref({
    NonScenarioAttributes: 'Non-Scenario',
    ScenarioAttributes: 'Scenario',
});
const valueTabGroupComponents = ref([]);
const collectionTabGroup = ref([]);
const moduleComponentAttributes = ref([]);
const inputValues = ref({});
const availableComponentTypeIds = ref([]);
const checkedComponentIds = ref([]); //For Treenode
const componentKey = ref(0); // Key to force re-render of tree components

// Initialize form with useForm
const form = useForm({
    values: [],
    usm_id: props.usm_id,
    usm_module_id: props.usm_module_id,
    scenario_id: Object.keys(props.scenarioOptions || {}).length > 0 ? Object.keys(props.scenarioOptions)[0] : null,
    mode: props.scenario_mode || ScenarioMode.NON_SCENARIO,
});

onMounted(() => {
    fetchInitialData();
});

const fetchInitialData = async () => {
    axios
        .get(route('components.get', props.usm_id))
        .then(response => {
            tree.value = response.data.tree;
        })
        .catch(error => {
            console.error('Error fetching data:', error);
        });

    fetchModuleComponentData();
};

const fetchModuleComponentData = () => {
    form.processing = true;
    axios
        .get(route('usm.module_component_data'), {
            params: {
                usm_id: props.usm_id,
                usm_module_id: props.usm_module_id,
                mode: form.mode,
                scenario_id: form.scenario_id,
            },
        })
        .then(response => {
            checkedComponentIds.value = response.data.checked_component_ids;
            availableComponentTypeIds.value = response.data.available_component_type_ids;
            valueTabGroupComponents.value = response.data.values_tab;
            collectionTabGroup.value = response.data.collections_tab.collections;
            moduleComponentAttributes.value = response.data.collections_tab.moduleComponentAttributes;

            // Initialize checked nodes with the IDs from the backend
            if (response.data.checked_component_ids && response.data.checked_component_ids.length > 0) {
                // Clear the existing set and add the new values
                checkedNodes.value.clear();
                response.data.checked_component_ids.forEach(id => {
                    checkedNodes.value.add(id);
                });
            } else {
                // Clear existing checks if no components are checked for this scenario
                checkedNodes.value.clear();
            }

            // Increment the component key to force tree re-render
            componentKey.value++;

            populateFormValues();
            populateCollectionForm();
        })
        .catch(error => {
            console.error('Error fetching module component data:', error);
        })
        .finally(() => {
            form.processing = false;
        });
};

const populateFormValues = () => {
    // Create temporary object to hold form values
    const formData = {};
    // Object to track is_collection values
    const collectionStatus = {};

    valueTabGroupComponents.value.forEach(group => {
        group.attrs.forEach(attr => {
            if (attr.values) {
                // Map values to the form structure using componentId as keys
                Object.entries(attr.values).forEach(([componentId, value]) => {
                    const key = `${attr.id}_${componentId}`;
                    formData[key] = value.input_value;
                    // Track the is_collection status for each value
                    collectionStatus[key] = value.is_collection || false;
                    // Initialize the inputValues object for UI binding
                    inputValues.value[key] = value.input_value;
                });
            }
        });
    });

    // Update the form values
    form.values = [];
    Object.entries(formData).forEach(([key, value]) => {
        const [typeId, componentId] = key.split('_');
        form.values.push({
            usm_module_component_attribute_id: parseInt(typeId),
            component_id: parseInt(componentId),
            input_value: value,
            usm_id: props.usm_id,
            is_collection: collectionStatus[key] || false,
        });
    });
};

const toggleExpand = nodeId => {
    if (expandedNodes.value.has(nodeId)) {
        expandedNodes.value.delete(nodeId);
    } else {
        expandedNodes.value.add(nodeId);
    }
};

const toggleCheck = node => {
    // Prevent adding null or undefined IDs to the set
    if (!node || node.id === null || node.id === undefined) {
        return;
    }

    if (checkedNodes.value.has(node.id)) {
        checkedNodes.value.delete(node.id);
    } else {
        checkedNodes.value.add(node.id);
    }

    if (form.mode === ScenarioMode.SCENARIO) {
        getCheckedComponents();
    }
};

const getCheckedComponents = () => {
    return Array.from(checkedNodes.value);
};

const handleExpressionChange = (paramId, componentId, newValue) => {
    // Update the local inputValues for UI binding
    const key = `${paramId}_${componentId}`;
    inputValues.value[key] = newValue;

    // Update the form.values array
    const valueIndex = form.values.findIndex(item => item.usm_module_component_attribute_id === parseInt(paramId) && item.component_id === parseInt(componentId));

    if (valueIndex !== -1) {
        // Update existing value
        form.values[valueIndex].input_value = newValue;
    } else {
        // Add new value
        form.values.push({
            usm_module_component_attribute_id: parseInt(paramId),
            component_id: parseInt(componentId),
            input_value: newValue,
            usm_id: props.usm_id,
            is_collection: false,
        });
    }
};

const handleInputChange = (paramType, paramId, componentId, e) => {
    const newValue = e.target.value;

    // Find the existing value in form.values array
    const index = form.values.findIndex(item => item.usm_module_component_attribute_id === parseInt(paramId) && item.component_id === parseInt(componentId));

    if (index !== -1) {
        // Update existing value
        form.values[index].input_value = newValue;
    } else {
        // Add new value
        form.values.push({
            usm_module_component_attribute_id: parseInt(paramId),
            component_id: parseInt(componentId),
            input_value: newValue,
            usm_id: props.usm_id,
        });
    }
};

// Helper function to get input value
const getInputValue = (paramId, componentId) => {
    const value = form.values.find(item => item.usm_module_component_attribute_id === parseInt(paramId) && item.component_id === parseInt(componentId));
    return value ? value.input_value : '';
};

// Helper function to check if an input is part of a collection
const isCollectionInput = (paramId, componentId) => {
    const value = form.values.find(item => item.usm_module_component_attribute_id === parseInt(paramId) && item.component_id === parseInt(componentId));
    return value ? value.is_collection : false;
};

const saveValues = () => {
    form.processing = true;
    form.clearErrors();

    // Send data to the server using axios
    axios
        .post(route('usm.module_component_type_values.update'), {
            values: form.values,
            usm_id: form.usm_id,
            usm_module_id: form.usm_module_id,
            mode: form.mode,
            scenario_id: form.scenario_id,
            checked_component_ids: getCheckedComponents(),
        })
        .then(response => {
            // Refresh data after saving
            fetchInitialData();
            form.recentlySuccessful = true;
            // Reset success state after delay (similar to Inertia's default behavior)
            setTimeout(() => {
                form.recentlySuccessful = false;
            }, 2000);
        })
        .catch(error => {
            if (error.response && error.response.data) {
                // Map axios errors to form errors
                form.setError(error.response.data);
            }
        })
        .finally(() => {
            form.processing = false;
        });
};

// Watch for changes in mode or scenario and re-fetch data
watch(
    () => form.mode,
    newValue => {
        fetchModuleComponentData();

        //Update collectionForm when the Values Tab form changes
        collectionForm.mode = newValue;
        if (newValue !== ScenarioMode.SCENARIO) {
            collectionForm.scenario_id = null;
        } else {
            collectionForm.scenario_id = form.scenario_id;
        }
    },
);

// Watch for changes in scenario_id and re-fetch data when in scenario mode
watch(
    () => form.scenario_id,
    newValue => {
        if (form.mode === ScenarioMode.SCENARIO) {
            fetchModuleComponentData();

            // Update collectionForm
            collectionForm.scenario_id = newValue;
        }
    },
);

//Computed
const enableCheckbox = computed(() => {
    return form.mode === ScenarioMode.SCENARIO;
});

// Compute which components should be visible based on mode and checked status
const visibleComponents = computed(() => {
    if (form.mode !== ScenarioMode.SCENARIO || checkedNodes.value.size === 0) {
        // Show all components when not in scenario mode or when no components are checked
        return null; // null means show all
    }

    const checkedIds = Array.from(checkedNodes.value);
    return checkedIds;
});

// Check if a component should be visible in the table
const isComponentVisible = componentId => {
    if (visibleComponents.value === null) {
        return true; // Show all components
    }
    return visibleComponents.value.includes(componentId);
};

/**
 * Collections related
 */

const collectionForm = useForm({
    usm_id: props.usm_id,
    usm_module_id: props.usm_module_id,
    scenario_id: form.mode === ScenarioMode.SCENARIO ? (Object.keys(props.scenarioOptions || {}).length > 0 ? Object.keys(props.scenarioOptions)[0] : null) : null,
    mode: form.mode,
    rows: {},
});

const populateCollectionForm = () => {
    collectionForm.rows = {}; //Reset
    collectionTabGroup.value.forEach(collection => {
        const options = moduleComponentAttributes.value.filter(attr => attr.component_type_id === collection.component_type_id);

        if (collection.rows[collection.id]) {
            //Push option into each collection.rows[collection.id]
            collection.rows[collection.id].forEach(row => {
                row.options = options;
            });
            collectionForm.rows[collection.id] = collection.rows[collection.id];
        }
    });
};

const addCollectionRow = collection => {
    const options = moduleComponentAttributes.value.filter(attr => attr.component_type_id === collection.component_type_id);

    // Initialize the collection's rows array if it doesn't exist
    if (!collectionForm.rows[collection.id]) {
        collectionForm.rows[collection.id] = [];
    }

    collectionForm.rows[collection.id].push({
        options: options,
        usm_module_component_attribute_id: null,
        usm_collection_id: collection.id,
        usm_id: props.usm_id,
        collection_values: {}, //collection.values,
    });

    // Get the row we just added
    const row = collectionForm.rows[collection.id][collectionForm.rows[collection.id].length - 1];

    // Initialize collection values
    collection.value_sets.forEach(valueSet => {
        // Initialize with empty values for new entries
        row.collection_values[valueSet.id] = {
            id: null,
            usm_id: props.usm_id,
            usm_collection_id: collection.id,
            usm_collection_value_set_id: valueSet.id,
            usm_module_component_attribute_id: row.usm_module_component_attribute_id || null,
            input_value: '',
        };
    });
};

const removeCollectionRow = (collectionId, index) => {
    collectionForm.rows[collectionId].splice(index, 1);
};

/**
 * Handle attribute selection change
 */
const handleCollectionAttributeChange = (row, value) => {
    // Update the attribute ID in the row
    row.usm_module_component_attribute_id = value;

    // Update all collection values to use the new attribute ID
    Object.keys(row.collection_values).forEach(valueSetId => {
        if (row.collection_values[valueSetId]) {
            row.collection_values[valueSetId].usm_module_component_attribute_id = value;
        }
    });
};

const saveCollections = () => {
    collectionForm.processing = true;
    collectionForm.clearErrors();

    // Send data to the server using axios
    axios
        .post(route('usm.collection_values.update'), collectionForm)
        .then(response => {
            // Refresh data after saving
            fetchInitialData();
            collectionForm.recentlySuccessful = true;
            // Reset success state after delay (similar to Inertia's default behavior)
            setTimeout(() => {
                collectionForm.recentlySuccessful = false;
            }, 2000);
        })
        .catch(error => {
            if (error.response && error.response.data) {
                // Map axios errors to form errors
                collectionForm.setError(error.response.data);
            }
        })
        .finally(() => {
            collectionForm.processing = false;
        });
};
</script>

<template>
    <div class="row">
        <div class="col-3">
            <nav id="bullet" class="h-100 flex-column align-items-stretch pe-4 border-end">
                <div class="component-tree">
                    <div v-if="tree && tree.length > 0">
                        <ul class="tree-root">
                            <tree-node v-for="node in tree" :key="`${node.id}-${componentKey}`" :node="node" :max-level="maxLevel" :selected-node-id="selectedComponent ? selectedComponent.id : null" :expanded-nodes="expandedNodes" :checked-nodes="checkedNodes" :enable-checkbox="enableCheckbox" :available-component-type-ids="availableComponentTypeIds" :checked-component-ids="checkedComponentIds" @toggle-expand="toggleExpand" @toggle-check="toggleCheck" />
                        </ul>
                    </div>
                    <div v-else class="alert alert-info">No components found. Add a component to get started.</div>
                </div>
            </nav>
        </div>

        <div class="col-9">
            <div class="d-flex align-items-center mb-3">
                <div class="me-3 d-flex align-items-center">
                    <span class="me-2">Mode:</span>
                    <Select v-model="form.mode" :options="modeOptions" class="w-auto" />
                </div>
                <div class="me-3 d-flex align-items-center" v-if="form.mode === ScenarioMode.SCENARIO">
                    <span class="me-2">Scenario:</span>
                    <Select v-model="form.scenario_id" :options="scenarioOptions" class="w-auto" />
                </div>
                <button class="btn btn-sm btn-info ms-auto" type="button" aria-expanded="false" data-bs-toggle="modal" :data-bs-target="`#collectionMapModal${usm_module_id}`"><i class="bi bi-collection"></i> Collection Maps</button>
            </div>
            <!-- Tab Navigation -->
            <ul class="nav nav-tabs my-3" id="componentTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" :id="`values-tab-${usm_module_id}`" data-bs-toggle="tab" :data-bs-target="`#values-content-${usm_module_id}`" type="button" role="tab" aria-controls="values-content" aria-selected="true">Values</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" :id="`collections-tab-${usm_module_id}`" data-bs-toggle="tab" :data-bs-target="`#collections-content-${usm_module_id}`" type="button" role="tab" aria-controls="collections-content" aria-selected="false">Collections</button>
                </li>
            </ul>
            <!-- Tab Content -->
            <div class="tab-content" id="componentTabsContent">
                <!-- Values Tab -->

                <div class="tab-pane fade show active" :id="`values-content-${usm_module_id}`" role="tabpanel" aria-labelledby="values-tab">
                    <!-- Alert messages -->
                    <div class="mb-4">
                        <FlashAlertWithErrors :errors="form.errors" />
                    </div>
                    <form @submit.prevent="saveValues">
                        <div v-if="valueTabGroupComponents.length === 0" class="alert alert-info mb-4">No module values found. Please add module component types first.</div>

                        <div v-else class="accordion mb-4" id="accordionPanelValue">
                            <div class="accordion-item" v-for="(group, index) in valueTabGroupComponents" :key="group.id">
                                <h2 class="accordion-header">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" :data-bs-target="'#panel-' + group.id" aria-expanded="true" :aria-controls="'panel-' + group.id">{{ index + 1 }}. {{ group.name }} - {{ group.level }}</button>
                                </h2>
                                <div :id="'panel-' + group.id" class="accordion-collapse collapse show">
                                    <div class="accordion-body">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th width="5%" class="text-center">#</th>
                                                    <th>Attribute</th>
                                                    <th v-for="(component) in group.components" :key="component.id" v-show="isComponentVisible(component.id)">
                                                        {{ component.name }}
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr v-for="(attr, paramIndex) in group.attrs" :key="paramIndex">
                                                    <td class="text-center">{{ paramIndex + 1 }}</td>
                                                    <td>{{ attr.attribute }}</td>
                                                    <td v-for="(component, compIndex) in group.components" :key="component.id" v-show="isComponentVisible(component.id)">
                                                        <TextInput v-if="isCollectionInput(attr.id, component.id)" :value="'collection'" class="form-control" :disabled="true" />
                                                        <ExpressionSuggestions v-else :suggestions="columns" v-model="inputValues[`${attr.id}_${component.id}`]" @update:modelValue="newValue => handleExpressionChange(attr.id, component.id, newValue)" :required="isComponentVisible(component.id)" :placeholder="''" />
                                                        <InputError :message="form.errors[`values.${attr.id}_${component.id}`]" />
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <PrimaryButton type="submit" :class="{ 'opacity-25': form.processing }" :disabled="form.processing"> Save </PrimaryButton>
                            <Transition enter-active-class="fade transition ease-in-out duration-500" enter-from-class="opacity-0" enter-to-class="opacity-100" leave-active-class="fade transition ease-in-out duration-500" leave-to-class="opacity-0">
                                <span v-if="form.recentlySuccessful" class="ms-2 text-success">Saved.</span>
                            </Transition>
                        </div>
                    </form>
                </div>

                <!-- Collections Tab -->
                <div class="tab-pane fade" :id="`collections-content-${usm_module_id}`" role="tabpanel" aria-labelledby="collections-tab">
                    <!-- Alert messages -->
                    <div class="mb-4">
                        <FlashAlertWithErrors :errors="collectionForm.errors" />
                    </div>
                    <div class="mb-3">
                        <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" :data-bs-target="`#collectionEditorModal-${usm_module_id}`"><i class="bi bi-plus"></i> Collections & Value Sets</button>
                    </div>
                    <form @submit.prevent="saveCollections">
                        <div class="accordion mb-4" id="accordionPanelTab">
                            <div class="accordion-item" v-for="(group, index) in collectionTabGroup" :key="group.id">
                                <h2 class="accordion-header">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" :data-bs-target="'#panel-' + group.id" aria-expanded="true" :aria-controls="'panel-' + group.id">{{ index + 1 }}. {{ group.name }} - {{ group.level }}</button>
                                </h2>
                                <div :id="'panel-' + group.id" class="accordion-collapse collapse show">
                                    <div class="accordion-body">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th width="5%" class="text-center">
                                                        <button type="button" class="btn btn-sm btn-primary" @click="addCollectionRow(group)">
                                                            <i class="bi bi-plus"></i>
                                                        </button>
                                                    </th>
                                                    <th width="30%">Attribute</th>
                                                    <th v-for="(value_set, index) in group.value_sets" :key="value_set.id">{{ value_set.name }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr v-for="(row, index) in collectionForm.rows[group.id]">
                                                    <td class="text-center">
                                                        <button type="button" class="btn btn-sm btn-danger" @click="removeCollectionRow(group.id, index)" :disabled="props.editDisabled">
                                                            <i class="bi bi-dash"></i>
                                                        </button>
                                                    </td>
                                                    <td>
                                                        <Select
                                                            :options="row.options"
                                                            :value="row.usm_module_component_attribute_id"
                                                            @update:modelValue="
                                                                value => {
                                                                    handleCollectionAttributeChange(row, value);
                                                                }
                                                            "
                                                            :disabled="props.editDisabled"
                                                            :label_key="'attribute'"
                                                            required
                                                        />
                                                    </td>
                                                    <td v-for="(value_set, index) in group.value_sets" :key="value_set.id">
                                                        <ExpressionSuggestions v-if="row?.collection_values?.[value_set.id]" :suggestions="columns" v-model="row.collection_values[value_set.id].input_value" :required="true" />
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <PrimaryButton type="submit" :class="{ 'opacity-25': collectionForm.processing }" :disabled="collectionForm.processing"> Save </PrimaryButton>
                            <Transition enter-active-class="fade transition ease-in-out duration-500" enter-from-class="opacity-0" enter-to-class="opacity-100" leave-active-class="fade transition ease-in-out duration-500" leave-to-class="opacity-0">
                                <span v-if="collectionForm.recentlySuccessful" class="ms-2 text-success">Saved.</span>
                            </Transition>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <CollectionMapModal :usm_id="usm_id" :usm_module_id="usm_module_id" :scenario_mode="form.mode" :key="usm_module_id" @saveSuccess="fetchInitialData"></CollectionMapModal>
    <CollectionEditorModal :usm_id="usm_id" :usm_module_id="usm_module_id" :scenario_mode="form.mode" :scenario_id="parseInt(form.scenario_id, 10)" :key="`${usm_module_id}-${form.mode}-${form.scenario_id}`" @saveSuccess="fetchInitialData"></CollectionEditorModal>
</template>

<style scoped>
.component-tree {
    padding: 10px 0;
    position: relative;
}

.tree-root,
.nested-tree {
    list-style-type: none;
    padding-left: 0;
}

.nested-tree {
    padding-left: 20px;
    position: relative;
}

/* Add vertical lines to nested items */
.nested-tree::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 8px;
    width: 1px;
    background-color: #dee2e6;
}

.tree-node {
    margin: 5px 0;
    position: relative;
}

/* Add horizontal lines to connect to vertical lines */
.nested-tree .tree-node::before {
    content: '';
    position: absolute;
    top: 12px;
    left: -12px;
    width: 12px;
    height: 1px;
    background-color: #dee2e6;
}

.expand-btn {
    background: none;
    border: none;
    padding: 0 5px;
    cursor: pointer;
}

.expand-placeholder {
    width: 24px;
    display: inline-block;
}

.node-label {
    padding: 3px 8px;
    cursor: pointer;
    border-radius: 3px;
    margin-right: 10px;
}

.node-label:hover {
    background-color: #f0f0f0;
}

.node-label.selected {
    background-color: #e0e0ff;
    font-weight: bold;
}

.node-actions {
    display: none;
    margin-left: auto;
}

.tree-node:hover .node-actions {
    display: flex;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}
</style>
