<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import axios from 'axios';
import InputLabel from './InputLabel.vue';
import Select from './Select.vue';
import PrimaryButton from './PrimaryButton.vue';
import HeadRow from './Table/HeadRow.vue';

const props = defineProps({
    original_id: {
        type: Number,
        required: true,
    },
});

const lineitemFamily = ref([]);
const table1 = ref();
const table2 = ref();
const headers = ref();
const differences = ref();
const postLoading = ref(false);

onMounted(() => {
    fetchInitialData();
});

const fetchInitialData = async () => {
    if (props.original_id != null) {
        await axios.get(route('search.lineitem_family_options', { original_id: props.original_id })).then(response => {
            lineitemFamily.value = response.data;
        });
    }
};

const compare = () => {
    postLoading.value = true;
    axios
        .post(route('lineitem_managers.compare', { table1: table1.value, table2: table2.value }))
        .then(response => {
            headers.value = response.data.columnsWithDifferences;
            differences.value = response.data.differences;
        })
        .catch(error => {
            alert(error.response.data.message);
        })
        .finally(() => {
            postLoading.value = false;
        });
};

// Compute filtered options for Table 2
const table2Options = computed(() => {
    const entries = Object.entries(lineitemFamily.value || {});
    const filteredEntries = entries.filter(([key]) => key !== table1.value);
    return Object.fromEntries(filteredEntries);
});

const disabledCompare = computed(() => !table1.value || !table2.value || postLoading.value);

// Watch table1 changes to reset table2 if selected value is the same
watch(table1, newValue => {
    if (table2.value === newValue) {
        table2.value = null;
    }
});
</script>

<template>
    <div class="row g-3">
        <div class="col-md-4">
            <InputLabel for="table1" value="Table 1" />
            <Select id="table1" v-model="table1" :options="lineitemFamily" :placeholder="'Select main'" />
        </div>
        <div class="col-md-4">
            <InputLabel for="table1" value="Table 2" />
            <Select id="table1" v-model="table2" :options="table2Options" :placeholder="'Select to compare'" />
        </div>
        <div class="col-md-4 d-flex align-items-end">
            <PrimaryButton @click="compare" :disabled="disabledCompare">Compare</PrimaryButton>
        </div>
    </div>

    <div class="row mt-3">
        <table v-show="headers != null" class="table table-bordered">
            <thead>
                <tr>
                    <HeadRow width="5%">#</HeadRow>
                    <HeadRow v-for="header in headers" colspan="2" class="text-center">{{ header }}</HeadRow>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(diff, index) in differences" :key="index">
                    <td>{{ Number(index) + 1 }}</td>
                    <template v-if="diff.status">
                        <td :colspan="headers.length * 2" class="text-center bg-warning">
                            {{ diff.status }}
                        </td>
                    </template>
                    <template v-else>
                        <template v-for="header in headers" :key="header">
                            <td width="10%">{{ diff[header]?.row1 || '[EMPTY]' }}</td>
                            <td width="10%">{{ diff[header]?.row2 || '[EMPTY]' }}</td>
                        </template>
                    </template>
                </tr>
            </tbody>
        </table>
    </div>
</template>
