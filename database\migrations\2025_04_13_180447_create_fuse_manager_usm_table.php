<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fuse_manager_usm', function (Blueprint $table) {
            $table->unsignedBigInteger('fuse_manager_id');
            $table->unsignedBigInteger('usm_id');

            $table->primary(['fuse_manager_id', 'usm_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fuse_manager_usm');
    }
};
