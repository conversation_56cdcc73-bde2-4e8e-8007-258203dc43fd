<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Casts\Attribute;

trait RevisionTrait
{
    //Accessor
    public function originalId(): Attribute
    {
        //If row is the original, direct use own ID
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => isset($attributes['original_id']) ? $attributes['original_id'] : (isset($attributes['id']) ? $attributes['id'] : null),
        );
    }

    public function previousRevisionId(): Attribute
    {
        //If row is the first version, direct use own ID
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => isset($attributes['previous_revision_id']) ? $attributes['previous_revision_id'] : (isset($attributes['id']) ? $attributes['id'] : null),
        );
    }

    // Version history methods
    public function createNewVersion(array $data): self
    {
        $latest_revision = self::getLatestRevision($this->original_id ?? $this->id);

        $data = array_merge($data, [
            'revision' => $latest_revision == null ? 1 : $latest_revision->revision + 1,
            'is_current' => false,
            'previous_revision_id' => $this->id,
            'created_user_id' => auth()->id(),
            'original_id' => $latest_revision == null ? $this->id : $latest_revision->original_id,
        ]);

        // Create new version
        return self::create($data);
    }

    public static function getRevisionHistory(int $id)
    {
        return self::where('id', $id)
            ->orWhere(function ($query) use ($id) {
                $query->where(function ($q) use ($id) {
                    $manager = self::find($id);
                    $previousIds = [];
                    while ($manager && $manager->previous_revision_id) {
                        $previousIds[] = $manager->previous_revision_id;
                        $manager = $manager->previousVersion;
                    }
                    if (!empty($previousIds)) {
                        $q->whereIn('id', $previousIds);
                    }
                });
            })
            ->orderBy('revision', 'desc')
            ->get();
    }

    public static function getLatestRevision(int $id)
    {
        return self::where('original_id', $id)
            ->orderBy('revision', 'desc')
            ->select('revision', 'original_id')
            ->first();
    }
}
