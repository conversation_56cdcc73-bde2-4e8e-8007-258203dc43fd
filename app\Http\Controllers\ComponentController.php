<?php

namespace App\Http\Controllers;

use App\Helpers\ComponentHelper;
use App\Models\Component;
use App\Models\ComponentLevel;
use App\Http\Requests\ComponentLevelUpdateRequest;
use App\Http\Requests\ComponentUpdateRequest;
use App\Models\ComponentType;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;

class ComponentController extends Controller
{
    /**
     * Display a listing of the components.
     */
    public function getComponents($usm_id)
    {
        $component_types = ComponentType::where('active', true)->get();
        $levels = ComponentLevel::where('usm_id', $usm_id)->get();
        $tree = (new ComponentHelper)->getComponentTree($usm_id);

        return response()->json(['component_types' => $component_types, 'levels' => $levels, 'tree' => $tree]);
    }

    /**
     * Create the specified component in storage.
     */
    public function store(ComponentUpdateRequest $request)
    {
        $data = $request->validated();
        $component = Component::create($data);

        return response()->json($component, 201);
    }

    /**
     * Update the specified component in storage.
     */
    public function update(ComponentUpdateRequest $request, int $id)
    {
        $data = $request->validated();
        $component = Component::find($id);
        $component->update($data);

        return response()->json($component);
    }

    /**
     * Remove the specified component from storage.
     */
    public function destroy($id)
    {
        $component = Component::findOrFail($id);
        $component->delete();
        return response()->json(null, 204);
    }

    public function getComponentLevels($usm_id)
    {
        $componentLevels = ComponentLevel::where('usm_id', $usm_id)->get();
        return response()->json(['data' => $componentLevels]);
    }

    public function postComponentLevels(ComponentLevelUpdateRequest $request, $usm_id)
    {
        $data = $request->validated();

        $store_row_ids = [];
        foreach ($data['rows'] as $row) {
            $result = ComponentLevel::updateOrCreate(['id' => $row['id'], 'usm_id' => $usm_id], $row);
            $store_row_ids[] = $result->id;
        }
        ComponentLevel::whereNotIn('id', $store_row_ids)->where('usm_id', $usm_id)->delete();

        return Redirect::back()->with(['message' => 'Component levels updated successfully']);
    }
}
