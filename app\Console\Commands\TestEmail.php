<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Mail\Message;
use Illuminate\Support\Facades\Mail;

class TestEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-email';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test send email';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->comment('Start Test Send Email');
        Mail::raw('Hello world', function (Message $message) {
            $message->to('<EMAIL>')->from(env('MAIL_FROM_ADDRESS'));
        });
        $this->comment('End Test Send Email');
    }
}
