<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bin_matrix_flows', function (Blueprint $table) {
            $table->id();
            $table->boolean('in_bm')->default(true);
            $table->boolean('in_avid_terra')->default(true);
            $table->boolean('in_ffr')->default(true);
            $table->integer('ord');
            $table->integer('flow');
            $table->integer('olb_bin');
            $table->integer('pass_bin');
            $table->string('reference_qdf')->nullable();
            $table->string('package_type')->nullable(); //Part of BOM Naming Convention
            $table->string('production_type')->nullable(); //Part of BOM Naming Convention
            $table->string('mm')->nullable(); //Part of BOM Naming Convention, hidden from UI
            $table->text('cross_ship'); //Part of Finished Good Attribute
            $table->unsignedBigInteger('bin_matrix_id');
            $table->unsignedBigInteger('bin_matrix_item_id');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bin_matrix_flows');
    }
};
