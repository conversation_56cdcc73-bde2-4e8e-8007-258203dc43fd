<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('speed_batch', function (Blueprint $table) {
            $table->string('state')->nullable();
            $table->unsignedBigInteger('state_update_user_id')->nullable();
            $table->timestamp('state_updated_at')->nullable();

            //Remove Approve columns
            $table->dropColumn('approved');
            $table->dropColumn('approved_user_id');
            $table->dropColumn('approved_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('speed_batch', function (Blueprint $table) {
            $table->dropColumn('state');
            $table->dropColumn('state_update_user_id');
            $table->dropColumn('state_updated_at');
        });
    }
};
