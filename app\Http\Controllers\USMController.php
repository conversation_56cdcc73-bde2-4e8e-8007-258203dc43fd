<?php

namespace App\Http\Controllers;

use App\Http\Requests\USMUpdateRequest;
use App\Models\BinMatrix;
use App\Models\Component;
use App\Models\ComponentLevel;
use App\Models\ComponentType;
use App\Models\Product;
use App\Models\ProductGroup;
use App\Models\ProductType;
use App\Models\USM;
use App\Models\USMCollection;
use App\Models\USMCollectionValue;
use App\Models\USMCollectionValueSet;
use App\Models\USMModule;
use App\Models\USMModuleComponentAttribute;
use App\Models\USMModuleComponentAttributeValue;
use App\Models\USMScenario;
use App\Services\CollectionMappingService;
use App\Services\ComplexTypeDetailXML;
use App\Services\GenerateLineItemDataXmlService;
use App\Services\GenerateStructureXmlService;
use App\Services\GenerateUPSXmlService;
use App\Services\GenerateUSMXmlService;
use App\Traits\WithProductData;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;

class USMController extends Controller
{
    use WithProductData;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        //Build Filter
        $filters = $this->filterSessions($request, 'usm', [
            'keyword' => '',
            'product_type_id' => null,
            'product_id' => null,
            'product_group_id' => null,
        ]);

        $list = USM::query()->with(['productType', 'productGroup', 'product', 'createdUser'])
            ->accessibleBy(auth()->user())
            ->when(!empty($filters['keyword']), function ($q) use ($filters) {
                $q->orWhere('name', 'like', '%' . $filters['keyword'] . '%');
            })->when(!empty($filters['product_type_id']), function ($q) use ($filters) {
                $q->where('product_type_id', $filters['product_type_id']);
            })->when(!empty($filters['product_id']), function ($q) use ($filters) {
                $q->where('product_id', $filters['product_id']);
            })->when(!empty($filters['product_group_id']), function ($q) use ($filters) {
                $q->where('product_group_id', $filters['product_group_id']);
            })->filterSort($filters)
            ->orderBy('created_at', 'desc')->paginate(config('table.per_page'));

        $productTypes = ProductType::pluck('name', 'id');
        $products = Product::select('name', 'id', 'product_type_id')->get();
        $productGroups = ProductGroup::select('name', 'id', 'product_id')->get();

        return Inertia::render('USM/Index', [
            'header' => USM::header(),
            'filters' => $filters,
            'list' => $list,
            'productTypes' => $productTypes,
            'products' => $products,
            'productGroups' => $productGroups
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return $this->edit(null);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(USMUpdateRequest $request)
    {
        return $this->update($request, null);
    }

    /**
     * Display the specified resource.
     */
    public function show(USM $usm)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(?USM $usm = null, bool $createRevision = false)
    {
        $binMatrixOptions = [];
        $scenarioOptions = [];
        $columns = [];

        if (null === $usm) {
            $data = new USM;
        } else {
            //Clone for new revision 
            if ($createRevision) {
                $data = $usm;
                //Increase revision
                $data['revision'] = USM::getLatestRevision($usm->original_id)?->revision + 1;

                //Reset value as new
                $data['state'] = STATE_WIP;
                $data['created_user_id'] = null;
                $data['state_update_user_id'] = null;
                $data['state_updated_at'] = null;
                $data['is_current'] = false;
            } else {
                $data = $usm->load(['createdUser', 'modules', 'scenarios', 'binMatrix.speedBatch', 'binMatrix.fuseConfigs', 'fuseConfigs']);

                $matrices = BinMatrix::whereProducts($data->product_type_id, $data->product_id, $data->product_group_id)->get();
                foreach ($matrices as $item) {
                    $binMatrixOptions[$item->id] = $item->name . ' - ' . $item->revision;
                }

                $selectedFuseConfigIds = $data->fuseConfigs->pluck('id')->toArray();
                $data->fuse_config_ids = $selectedFuseConfigIds;

                foreach ($data->scenarios as $item) {
                    $scenarioOptions[$item->id] = $item->name;
                }

                $columns = $data->binMatrix->speedBatch->getViewModal()->getTableColumns();
            }
        }
        return Inertia::render('USM/Edit', $this->withProductData([
            'data' => Inertia::always($data),
            'createRevision' => $createRevision,
            'states' => USM::states(),
            'binMatrixOptions' => $binMatrixOptions,
            'scenarioOptions' => $scenarioOptions,
            'columns' => $columns
        ]));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(USMUpdateRequest $request, ?USM $usm = null)
    {
        $data = $request->validated();

        // Extract modules data from request
        $modulesData = $data['modules'] ?? [];
        unset($data['modules']); // Remove from main data array

        // Extract scenarios data from request
        $scenariosData = $data['scenarios'] ?? [];
        unset($data['scenarios']); // Remove from main data array

        // Extract fuse config IDs data from request
        $fuseConfigIds = $data['fuse_config_ids'] ?? [];
        unset($data['fuse_config_ids']); // Remove from main data array

        if (null === $usm) {
            $data['created_user_id'] = auth()->id();

            $usm = USM::create($data);

            // Create modules for new USM
            $this->saveUSMModules($usm, $modulesData);

            // Create scenarios for new USM
            $this->saveUSMScenarios($usm, $scenariosData);

            // Sync fuse configs
            $usm->fuseConfigs()->sync($fuseConfigIds);

            // Create default component levels for new USM
            $this->presetComponentLevel($usm->id);

            return Redirect::route('usm.edit', $usm->id)->with('message', 'USM created successfully');
        } else {

            //Create new revision 
            if ($data['create_revision']) {
                $usm = $this->createRevision($usm, $data);
                //return Redirect::route('usm.edit', $usm->id)->with('message', "USM Revision {$usm->revision} created successfully");
            } else {
                $usm->update($data);

                // Update modules for existing USM
                $this->saveUSMModules($usm, $modulesData);

                // Update scenarios for existing USM
                $this->saveUSMScenarios($usm, $scenariosData);

                // Sync fuse configs
                $usm->fuseConfigs()->sync($fuseConfigIds);
            }

            return Redirect::route('usm.edit', $usm->id)->with('message', 'USM updated successfully');
        }
    }

    /**
     * Save USM modules
     */
    private function saveUSMModules(USM $usm, array $modules = [])
    {
        // Delete existing modules that are not in the new list
        $existingModuleIds = [];
        foreach ($modules as $module) {
            if (!empty($module['id'])) {
                $existingModuleIds[] = $module['id'];
            }
        }

        // Delete modules not in the updated list
        if (!empty($existingModuleIds)) {
            USMModule::where('usm_id', $usm->id)
                ->whereNotIn('id', $existingModuleIds)
                ->delete();
        } else {
            // If no existing IDs, delete all modules for this USM
            USMModule::where('usm_id', $usm->id)->delete();
        }

        // Create or update modules
        foreach ($modules as $index => $moduleData) {
            $moduleId = $moduleData['id'] ?? null;

            // Prepare module data
            $moduleAttributes = [
                'name' => $moduleData['name'],
                'keyword' => $moduleData['keyword'],
                'sequence' => $moduleData['sequence'] ?? ($index + 1),
                'usm_id' => $usm->id,
                'active' => $moduleData['active'] ?? true,
            ];

            if ($moduleId) {
                // Update existing module
                USMModule::where('id', $moduleId)->update($moduleAttributes);
            } else {
                // Create new module
                USMModule::create($moduleAttributes);
            }
        }
    }

    /**
     * Save USM scenarios data
     */
    private function saveUSMScenarios(USM $usm, array $scenariosData)
    {
        // Delete existing scenarios that are not in the new data
        $scenarioIds = collect($scenariosData)->pluck('id')->filter()->all();
        $usm->scenarios()->whereNotIn('id', $scenarioIds)->delete();

        // Create or update scenarios
        foreach ($scenariosData as $scenarioData) {
            if (!empty($scenarioData['id'])) {
                // Update existing scenario
                $usm->scenarios()->where('id', $scenarioData['id'])->update($scenarioData);
            } else {
                // Create new scenario
                $usm->scenarios()->create($scenarioData);
            }
        }
    }

    //To clone other related data
    private function createRevision($oldUSM, $data)
    {
        DB::beginTransaction();
        try {
            //TODO
            DB::commit();

            return null;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(USM $usm)
    {
        $usm->delete();
        return Redirect::route('usm.index')->with('message', 'USM deleted successfully');
    }

    public function postState(Request $request, USM $usm)
    {
        $usm->update(['state' => $request->state, 'state_update_user_id' => auth()->id(), 'state_updated_at' => now()]);
        return response()->json(['message' => 'State updated successfully']);
    }

    public function getRevisionCreate(USM $usm)
    {
        return $this->edit($usm, true);
    }

    public function getAttributeMap(USM $usm)
    {
        $componentTypeIds = Component::where('usm_id', $usm->id)
            ->distinct()
            ->pluck('component_type_id')
            ->filter(); // Remove any null values

        // Get component types with their attributes
        $componentTypes = ComponentType::whereIn('id', $componentTypeIds)->orderBy('id')->get();

        $xml = new ComplexTypeDetailXML;

        $attributeMap = [];
        foreach ($componentTypes as $componentType) {
            $ioParams = $xml->getIOParamsByComponentTypeName($componentType->name);

            foreach ($ioParams as $k => $params) {
                foreach ($params as $param) {
                    if (isset($param['complex_values'])) {
                        foreach ($param['complex_values'] as $complexValue) {
                            $complexValue['name'] = $param['name'];
                            $complexValue['description'] = $param['description'];
                            $complexValue['is_complex'] = true;
                            $complexValue['component_type_id'] = $componentType->id;
                            $complexValue['level'] = $componentType->level;
                            $complexValue['component_type'] = $componentType->name;
                            $complexValue['attribute'] = $param['name'] . "[" . $complexValue['complex_name'] . "]";
                            $complexValue['scenario_mode'] = $k;
                            $complexValue['parameter'] = $complexValue['complex_name'];


                            // Get the associated module, if any
                            $moduleMapping = USMModuleComponentAttribute::where('usm_id', $usm->id)
                                ->where('component_type_id', $componentType->id)
                                ->where('attribute', $complexValue['attribute'])
                                ->first();

                            $complexValue['module_id'] = $moduleMapping ? $moduleMapping->usm_module_id : null;

                            $attributeMap[] = $complexValue;
                        }
                    } else {
                        $param['component_type_id'] = $componentType->id;
                        $param['level'] = $componentType->level;
                        $param['component_type'] = $componentType->name;
                        $param['attribute'] = $param['name'];
                        $param['scenario_mode'] = $k;

                        // Get the associated module, if any
                        $moduleMapping = USMModuleComponentAttribute::where('usm_id', $usm->id)
                            ->where('component_type_id', $componentType->id)
                            ->where('attribute', $param['attribute'])
                            ->first();

                        $param['module_id'] = $moduleMapping ? $moduleMapping->usm_module_id : null;

                        $attributeMap[] = $param;
                    }
                }
            }
        }

        return response()->json(['data' => $attributeMap]);
    }

    /**
     * Get modules for a USM
     */
    public function getModules(USM $usm)
    {
        $modules = USMModule::where('usm_id', $usm->id)
            ->orderBy('sequence')
            ->select('name', 'id')
            ->get();

        return response()->json([
            'modules' => $modules
        ]);
    }

    public function postModuleMappings(Request $request, USM $usm)
    {
        // Validate the request
        $request->validate([
            'rows' => 'required|array',
            'rows.*.module_id' => 'nullable|exists:usm_modules,id',
            'rows.*.component_type_id' => 'required|exists:component_types,id',
            'rows.*.attribute' => 'required|string',
        ]);

        $rows = $request->input('rows');

        $componentLevels = ComponentLevel::where('usm_id', 1)
            ->pluck('id', 'name')
            ->toArray();

        // Process each row
        foreach ($rows as $row) {
            if (!empty($row['module_id'])) {
                // Check if a mapping already exists
                $mapping = USMModuleComponentAttribute::where('usm_id', $usm->id)
                    ->where('component_type_id', $row['component_type_id'])
                    ->where('attribute', $row['attribute'])
                    ->first();

                if ($mapping) {
                    // Update existing mapping
                    $mapping->update([
                        'usm_module_id' => $row['module_id'],
                    ]);
                } else {
                    // Create new mapping
                    USMModuleComponentAttribute::create([
                        'usm_id' => $usm->id,
                        'component_type_id' => $row['component_type_id'],
                        'attribute' => $row['attribute'],
                        'usm_module_id' => $row['module_id'],
                        'scenario_mode' => $row['scenario_mode'],
                        'level' => $row['level'],
                        'component_level_id' => isset($componentLevels[$row['level']]) ? $componentLevels[$row['level']] : null,
                        'is_complex' => isset($row['is_complex']) ? $row['is_complex'] : false,
                        'name' => $row['name'],
                        'parameter' => isset($row['parameter']) ? $row['parameter'] : null,
                        'datatype' => $row['type'],
                        'units' => $row['units'],
                        'description' => $row['description'],
                        'complex_description' => isset($row['complex_description']) ? $row['complex_description'] : null,
                    ]);
                }
            }
        }
        return Redirect::back()->with(['message' => 'Component levels updated Module mappings saved successfully']);
    }

    public function getModuleComponentData(Request $request)
    {
        $usm_id = $request->input('usm_id');
        $usm_module_id = $request->input('usm_module_id');
        $mode = $request->input('mode');
        $scenario_id = $request->input('scenario_id');

        // Validate required fields
        if (!$usm_id || !$usm_module_id) {
            return response()->json(['error' => 'Missing required parameters'], 400);
        }

        //Get checked component ids
        $checked_component_ids = [];
        if ($mode == XML_SCENARIO && $scenario_id) {
            $checked_component_ids = USMScenario::find($scenario_id)
                ->components()
                ->wherePivot('usm_module_id', $usm_module_id)
                ->pluck('id')
                ->toArray();
        }

        // Get all module component types
        $componentAttributes = USMModuleComponentAttribute::where('usm_module_id', $usm_module_id)
            ->where('scenario_mode', $mode)
            ->with('componentType.components')
            ->get();

        // Get saved values for this component
        $valuesQuery = USMModuleComponentAttributeValue::whereHas('moduleComponentAttribute', function ($query) use ($usm_module_id) {
            $query->where('usm_module_id', $usm_module_id);
        })->where('usm_id', $usm_id);

        // Filter values based on scenario mode
        if ($mode == XML_NONSCENARIO) {
            $valuesQuery->whereNull('usm_scenario_id');
        } else if ($mode == XML_SCENARIO && $scenario_id) {
            $valuesQuery->where('usm_scenario_id', $scenario_id);
        }
        $values = $valuesQuery->get();

        // Group by component type
        $groupedData = [];
        $availableComponentTypeIds = []; //For the tree-node to strike out unavailable components
        foreach ($componentAttributes as $attr) {
            $componentType = $attr->componentType;

            if (!$componentType) continue;

            $typeId = $componentType->id;

            // Initialize component type group if it doesn't exist
            if (!isset($groupedData[$typeId])) {
                $groupedData[$typeId] = [
                    'id' => $componentType->id,
                    'name' => $componentType->name,
                    'active' => $componentType->active,
                    'level' => $componentType->level,
                    'type' => $componentType->type,
                    'scenario_enabled' => $componentType->scenario_enabled,
                    'sub_elements' => $componentType->sub_elements,
                    'components' => $componentType->components,
                    'attrs' => []
                ];
            }

            // Convert the model to an array and add it to the group
            $attrArray = $attr->toArray();
            $attrArray['values'] = [];
            $groupedData[$typeId]['attrs'][] = $attrArray;

            // Add component to available components
            $availableComponentTypeIds[] = $attr->component_type_id;
        }

        $mapping = new CollectionMappingService($usm_id, $usm_module_id, $mode, $scenario_id);
        $collection_mappping = $mapping->getMappedCollectionForValues();

        // Merge the values into the component structure
        if ($values->count() > 0) {
            foreach ($values as $value) {
                $attributeId = $value->usm_module_component_attribute_id;
                $componentId = $value->component_id;

                // Find the attribute and add the value
                foreach ($groupedData as &$group) {
                    foreach ($group['attrs'] as &$attrArray) {
                        if ($attrArray['id'] == $attributeId) {
                            // Store the value object as an array
                            $valueArray = $value->toArray();
                            $valueArray['is_collection'] = isset($collection_mappping[$componentId][$attributeId]) ? true : false;
                            $attrArray['values'][$componentId] = [
                                'id' => $valueArray['id'],
                                "usm_id" => $valueArray['usm_id'],
                                "component_id" => $valueArray['component_id'],
                                "usm_module_component_attribute_id" => $valueArray['usm_module_component_attribute_id'],
                                "input_value" => $valueArray['input_value'],
                                "is_collection" => $valueArray['is_collection'],
                            ];
                            break;
                        }
                    }
                }
            }
        }

        // Initialize empty values for any component that doesn't have values yet
        foreach ($groupedData as &$group) {
            if (!empty($group['components'])) {
                foreach ($group['attrs'] as &$attrArray) {
                    foreach ($group['components'] as $component) {
                        if (!isset($attrArray['values'][$component['id']])) {
                            // Create an empty default value structure
                            $attrArray['values'][$component['id']] = [
                                'id' => null,
                                'usm_id' => $usm_id,
                                // 'usm_module_id' => $usm_module_id,
                                'component_id' => $component['id'],
                                'usm_module_component_attribute_id' => $attrArray['id'],
                                'input_value' => '',
                                'is_collection' => isset($collection_mappping[$component['id']][$attrArray['id']]) ? true : false
                            ];
                        }
                    }
                }
            }
        }

        $collections = USMCollection::with(['moduleComponentAttributes', 'valueSets', 'values'])
            // ->when($scenario_id, function ($query) use ($scenario_id) {
            //     return $query->where('usm_scenario_id', $scenario_id);
            // })
            ->when(!$scenario_id, function ($query) {
                return $query->whereNull('usm_scenario_id');
            })
            ->where('usm_module_id', $usm_module_id)
            ->where('scenario_mode', $mode)->get();

        $groupedCollections = [];
        foreach ($collections as $collection) {
            $collectionArray['id'] = $collection->id;
            $collectionArray['name'] = $collection->name;
            $collectionArray['level'] = $collection->level;
            $collectionArray['component_type_id'] = $collection->component_type_id;
            $collectionArray['value_sets'] = $collection->valueSets->toArray();
            $collectionArray['rows'] = [];

            foreach ($collection->moduleComponentAttributes as $moduleComponentAttribute) {
                $row = [];
                $row['usm_module_component_attribute_id'] = $moduleComponentAttribute->id;
                $row['usm_collection_id'] = $collection->id;
                foreach ($collection->values as $value) {
                    if ($value->usm_module_component_attribute_id === $moduleComponentAttribute->id) {
                        $row['collection_values'][$value->usm_collection_value_set_id] = $value->toArray();
                    }
                }

                //Append collection_values that not yet created but value_set exists, initial empty one
                foreach ($collection->valueSets as $valueSet) {
                    if (!isset($row['collection_values'][$valueSet->id])) {
                        $row['collection_values'][$valueSet->id] = [
                            'id' => null,
                            'input_value' => null,
                            'value' => null,
                            'usm_module_component_attribute_id' => null,
                            'usm_collection_id' => $collection->id,
                            'usm_collection_value_set_id' => $valueSet->id,
                            'usm_id' => $usm_id,
                        ];
                    }
                }

                $collectionArray['rows'][$collection->id][] = $row;
            }
            $groupedCollections[] = $collectionArray;
        }
        $moduleComponentAttributes = USMModuleComponentAttribute::where('usm_module_id', $usm_module_id)->where('scenario_mode', $mode)->select('id', 'attribute', 'component_type_id')->get();

        return response()->json([
            "checked_component_ids" => $checked_component_ids,
            "available_component_type_ids" => array_values(array_unique($availableComponentTypeIds)),
            "values_tab" => array_values($groupedData),
            "collections_tab" => ['collections' => $groupedCollections, 'moduleComponentAttributes' => $moduleComponentAttributes],
        ]);
    }

    /**
     * Save module component type values
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function postModuleComponentTypeValues(Request $request)
    {
        $usm_id = $request->input('usm_id');
        $usm_module_id = $request->input('usm_module_id');
        $values = $request->input('values', []);
        $mode = $request->input('mode');
        $scenario_id = $request->input('scenario_id');
        $checked_component_ids = $request->input('checked_component_ids');

        // Validate required fields
        if (!$usm_id || !$usm_module_id) {
            return response()->json(['error' => 'Missing required parameters'], 400);
        }

        // Validate each value
        $validator = \Validator::make(['values' => $values], [
            'values' => 'required|array',
            'values.*.usm_module_component_attribute_id' => 'required|exists:usm_module_component_attributes,id',
            'values.*.component_id' => 'required|exists:components,id',
            'values.*.input_value' => 'nullable|string',
            'values.*.usm_id' => 'required|integer'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            \DB::beginTransaction();
            //Save the mode this module is using
            $module = USMModule::find($usm_module_id);
            $module->scenario_mode = $mode;
            $module->save();

            foreach ($values as $value) {
                // Check if a value already exists for this type and component
                $existing = USMModuleComponentAttributeValue::where('usm_module_component_attribute_id', $value['usm_module_component_attribute_id'])
                    ->where('component_id', $value['component_id'])
                    ->where('usm_id', $usm_id)
                    ->where('usm_scenario_id', $mode === XML_SCENARIO ? $scenario_id : null)
                    ->first();

                if ($existing) {
                    // Update existing value
                    $existing->input_value = $value['input_value'];
                    $existing->usm_scenario_id = $mode === XML_SCENARIO ? $scenario_id : null;
                    $existing->save();
                } else {
                    // Create new value
                    USMModuleComponentAttributeValue::create([
                        'usm_module_component_attribute_id' => $value['usm_module_component_attribute_id'],
                        'component_id' => $value['component_id'],
                        'input_value' => $value['input_value'],
                        'usm_id' => $usm_id,
                        'usm_scenario_id' => $mode === XML_SCENARIO ? $scenario_id : null
                    ]);
                }
            }

            if ($mode === XML_SCENARIO && $scenario_id !== null) {
                // Create an array of component_id => ['usm_module_id' => $usm_module_id] for sync
                $syncData = [];
                foreach ($checked_component_ids as $componentId) {
                    $syncData[$componentId] = ['usm_module_id' => $usm_module_id];
                }

                USMScenario::find($scenario_id)->components()->sync($syncData);
            }

            \DB::commit();
            return response()->json(['message' => 'Values saved successfully']);
        } catch (\Exception $e) {
            \DB::rollBack();
            return response()->json(['message' => 'Failed to save values: ' . $e->getMessage()], 500);
        }
    }

    public function postCollectionValues(Request $request)
    {
        try {
            \DB::beginTransaction();

            // Keep track of all collection value IDs that are in the current submission
            $usedValueIds = [];
            $usm_id = $request->usm_id;
            $usm_module_id = $request->usm_module_id;

            foreach ($request->rows as $rows) {
                foreach ($rows as $row) {
                    // Handle the many-to-many relationship using Eloquent
                    if (!empty($row['usm_module_component_attribute_id']) && !empty($row['usm_collection_id'])) {
                        // Get the collection and attach the attribute if not already attached
                        $collection = USMCollection::find($row['usm_collection_id']);
                        $collection->moduleComponentAttributes()->syncWithoutDetaching([$row['usm_module_component_attribute_id']]);
                    }

                    // Save or update collection values
                    foreach ($row['collection_values'] as $valueSetId => $collection_value) {
                        if (!empty($collection_value['id'])) {
                            // Update existing value
                            $value = USMCollectionValue::find($collection_value['id']);
                            if ($value) {
                                $value->update([
                                    'input_value' => $collection_value['input_value'],
                                    'usm_module_component_attribute_id' => $row['usm_module_component_attribute_id'],
                                ]);
                                // Add this ID to the list of used IDs
                                $usedValueIds[] = $collection_value['id'];
                            }
                        } else {
                            // Create new value
                            $newValue = USMCollectionValue::create([
                                'usm_id' => $collection_value['usm_id'],
                                'usm_collection_id' => $collection_value['usm_collection_id'],
                                'usm_collection_value_set_id' => $collection_value['usm_collection_value_set_id'],
                                'usm_module_component_attribute_id' => $row['usm_module_component_attribute_id'],
                                'input_value' => $collection_value['input_value'],
                            ]);
                            // Add this ID to the list of used IDs
                            $usedValueIds[] = $newValue->id;
                        }
                    }
                }
            }

            // Delete any collection values for this USM and module that aren't in the used IDs list
            if (!empty($usm_id) && !empty($usm_module_id)) {
                // Get all collection IDs for this module
                $collectionIds = USMCollection::where('usm_module_id', $usm_module_id)->pluck('id')->toArray();

                if (!empty($collectionIds)) {
                    // Delete values that aren't in the used list
                    USMCollectionValue::where('usm_id', $usm_id)
                        ->whereIn('usm_collection_id', $collectionIds)
                        ->when(!empty($usedValueIds), function ($query) use ($usedValueIds) {
                            return $query->whereNotIn('id', $usedValueIds);
                        })
                        ->delete(); // Using SoftDeletes
                }
            }

            \DB::commit();
            return response()->json(['message' => 'Collection values saved successfully']);
        } catch (\Exception $e) {
            \DB::rollBack();
            return response()->json(['message' => 'Failed to save collection values: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get component data for collections
     */
    public function getCollectionEditor(Request $request)
    {
        $usm_id = $request->usm_id;
        $usm_module_id = $request->usm_module_id;
        $scenario_mode = $request->scenario_mode;
        $scenario_id = $request->scenario_id;

        // Get component types for this module
        $componentTypes = ComponentType::select('id', 'name', 'level', 'source')
            ->with(['usmModuleComponentAttributes' => function ($query) use ($usm_module_id) {
                $query->where('usm_module_id', $usm_module_id);
            }])
            ->whereHas('usmModuleComponentAttributes', function ($q) use ($usm_module_id) {
                $q->where('usm_module_id', $usm_module_id);
            })->get();

        $uniqueComponentLevelIds = [];
        foreach ($componentTypes as $types) {
            foreach ($types->usmModuleComponentAttributes as $attribute) {
                $uniqueComponentLevelIds[] = $attribute->component_level_id;
            }
        }

        $levels = ComponentLevel::whereIn('id', array_unique($uniqueComponentLevelIds))->get();
        $componentLevelOptions = select_options($levels, 'id', ['name']);

        // Get existing collections for this module
        $collections = USMCollection::where('usm_id', $usm_id)->with('valueSets')
            ->where('usm_module_id', $usm_module_id)
            ->where('scenario_mode', $scenario_mode)
            ->when($scenario_id, function ($query) use ($scenario_id) {
                return $query->where('usm_scenario_id', $scenario_id);
            })
            ->when(!$scenario_id, function ($query) {
                return $query->whereNull('usm_scenario_id');
            })
            ->get();

        return response()->json([
            'component_level_options' => $componentLevelOptions,
            'component_types' => $componentTypes,
            'collections' => $collections,
        ]);
    }

    public function postCollectionEditor(Request $request)
    {
        $validated = $request->validate([
            'usm_id' => 'required|exists:universal_spec_managers,id',
            'usm_module_id' => 'required|exists:usm_modules,id',
            'scenario_mode' => 'required|string',
            'scenario_id' => 'nullable|integer',
            'collections' => 'nullable|array',
            'collections.*.id' => 'nullable|integer',
            'collections.*.name' => 'required|string|max:255',
            'collections.*.component_level_id' => 'required|exists:component_levels,id',
            'collections.*.component_type_id' => 'required|exists:component_types,id',
            'collections.*.value_sets' => 'nullable|array',
            'collections.*.value_sets.*.id' => 'nullable|integer',
            'collections.*.value_sets.*.name' => 'required|string|max:255',
            'collections.*.value_sets.*.sequence' => 'nullable|integer',
        ]);

        $usmId = $request->usm_id;
        $usmModuleId = $request->usm_module_id;
        $scenarioMode = $request->scenario_mode;
        $scenarioId = $request->scenario_id;

        // Process collections
        $savedCollections = [];

        foreach ($request->collections as $collectionData) {
            // Create or update collection
            $collection = isset($collectionData['id']) && $collectionData['id']
                ? USMCollection::find($collectionData['id'])
                : new USMCollection();

            if (!$collection) {
                $collection = new USMCollection();
            }

            $collection->usm_id = $usmId;
            $collection->usm_module_id = $usmModuleId;
            $collection->name = $collectionData['name'];
            $collection->level = $collectionData['level'];
            $collection->component_level_id = $collectionData['component_level_id'];
            $collection->component_type_id = $collectionData['component_type_id'];
            $collection->scenario_mode = $scenarioMode;
            $collection->usm_scenario_id = $scenarioId;
            $collection->sequence = $collectionData['sequence'];
            $collection->source = $collectionData['source'];
            $collection->save();

            // Process value sets for this collection
            $savedValueSetIds = [];

            if (isset($collectionData['value_sets']) && is_array($collectionData['value_sets'])) {
                foreach ($collectionData['value_sets'] as $valueSetIndex => $valueSetData) {
                    // Create or update value set
                    $valueSet = isset($valueSetData['id']) && $valueSetData['id']
                        ? USMCollectionValueSet::find($valueSetData['id'])
                        : new USMCollectionValueSet();

                    if (!$valueSet) {
                        $valueSet = new USMCollectionValueSet();
                    }

                    $valueSet->name = $valueSetData['name'];
                    $valueSet->usm_collection_id = $collection->id;
                    $valueSet->usm_id = $usmId;
                    $valueSet->sequence = $valueSetData['sequence'] ?? ($valueSetIndex + 1);
                    $valueSet->save();

                    $savedValueSetIds[] = $valueSet->id;
                }

                // Delete any value sets that weren't in the request
                USMCollectionValueSet::where('usm_collection_id', $collection->id)
                    ->whereNotIn('id', $savedValueSetIds)
                    ->delete();
            }

            // Add value sets to the collection data for response
            $collection->value_sets = USMCollectionValueSet::where('usm_collection_id', $collection->id)
                ->orderBy('sequence')
                ->get();

            $savedCollections[] = $collection;
        }

        // Get IDs of saved collections to check which ones were removed
        $savedCollectionIds = collect($savedCollections)->pluck('id')->toArray();

        // Find collections that are going to be deleted
        $collectionsToDelete = USMCollection::where('usm_id', $usmId)
            ->where('usm_module_id', $usmModuleId)
            ->where('scenario_mode', $scenarioMode)
            ->when($scenarioId, function ($query) use ($scenarioId) {
                return $query->where('usm_scenario_id', $scenarioId);
            })
            ->when(!$scenarioId, function ($query) {
                return $query->whereNull('usm_scenario_id');
            })
            ->whereNotIn('id', $savedCollectionIds)
            ->pluck('id')
            ->toArray();

        // First delete all value sets for collections being deleted
        if (!empty($collectionsToDelete)) {
            USMCollectionValueSet::whereIn('usm_collection_id', $collectionsToDelete)->delete();
        }

        // Delete any collections that weren't in the request
        $deleteUSMCollection = USMCollection::where('usm_id', $usmId)
            ->where('usm_module_id', $usmModuleId)
            ->where('scenario_mode', $scenarioMode)
            ->when($scenarioId, function ($query) use ($scenarioId) {
                return $query->where('usm_scenario_id', $scenarioId);
            })
            ->when(!$scenarioId, function ($query) {
                return $query->whereNull('usm_scenario_id');
            });

        //Delete all collections if no collections are provided
        if (empty($request->collections)) {
            $deleteUSMCollection->delete();
        } else {
            $deleteUSMCollection->whereNotIn('id', $savedCollectionIds)->delete();
        }

        return response()->json([
            'message' => 'Collections saved successfully',
            'collections' => $savedCollections
        ]);
    }

    /**
     * Export USM data as XML
     * 
     * @param USM $usm The USM to export
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function exportUSMXML(USM $usm)
    {
        try {
            // Load the USM to get module ID
            $usm = USM::with(['modules'])->find($usm->id);
            if (!$usm || $usm->modules->isEmpty()) {
                throw new \Exception("USM not found or has no modules");
            }

            // Initialize services with proper parameters
            $exportService = new GenerateUSMXmlService($usm->id);
            $filePath = $exportService->saveXMLToFile();

            return response()->download($filePath, basename($filePath), [
                'Content-Type' => 'application/xml',
                'Content-Disposition' => 'attachment; filename="' . basename($filePath) . '"',
            ])->deleteFileAfterSend(true);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function exportUPSXML(USM $usm)
    {
        try {
            // Load the USM to get module ID
            $usm = USM::with(['modules'])->find($usm->id);
            if (!$usm || $usm->modules->isEmpty()) {
                throw new \Exception("USM not found or has no modules");
            }

            // Initialize services with proper parameters
            $exportService = new GenerateUPSXmlService($usm->id);
            $filePaths = $exportService->generateAll();

            // Bundle into a ZIP
            $zipFilename = 'ups_export_' . $usm->name . '_' . date('Ymd_His') . '.zip';
            $zipPath = sys_get_temp_dir() . DIRECTORY_SEPARATOR . $zipFilename;
            $zip = new \ZipArchive();
            if ($zip->open($zipPath, \ZipArchive::CREATE | \ZipArchive::OVERWRITE) === true) {
                foreach ($filePaths as $file) {
                    $zip->addFile($file, basename($file));
                }
                $zip->close();
            } else {
                throw new \Exception("Could not create zip archive");
            }
            return response()->download($zipPath, $zipFilename, [
                'Content-Type' => 'application/zip',
                'Content-Disposition' => 'attachment; filename="' . $zipFilename . '"',
            ])->deleteFileAfterSend(true);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function exportStructureXML(USM $usm)
    {
        try {
            // Load the USM to get module ID
            $usm = USM::with(['modules'])->find($usm->id);
            // Initialize services with proper parameters
            $exportService = new GenerateStructureXmlService($usm->id);
            $filePath = $exportService->saveXMLToFile();

            return response()->download($filePath, basename($filePath), [
                'Content-Type' => 'application/xml',
                'Content-Disposition' => 'attachment; filename="' . basename($filePath) . '"',
            ])->deleteFileAfterSend(true);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function exportLineItemDataXML(USM $usm)
    {
        try {
            // Load the USM to get module ID
            $usm = USM::with(['modules'])->find($usm->id);
            // Initialize services with proper parameters
            $exportService = new GenerateLineItemDataXmlService($usm->id);
            $filePaths = $exportService->saveXMLToFile();

            // Bundle into a ZIP
            $zipFilename = 'lineitemdata_usm_' . $usm->name . '_' . date('Ymd_His') . '.zip';
            $zipPath = sys_get_temp_dir() . DIRECTORY_SEPARATOR . $zipFilename;
            $zip = new \ZipArchive();
            if ($zip->open($zipPath, \ZipArchive::CREATE | \ZipArchive::OVERWRITE) === true) {
                foreach ($filePaths as $file) {
                    $zip->addFile($file, basename($file));
                }
                $zip->close();
            } else {
                throw new \Exception("Could not create zip archive");
            }
            return response()->download($zipPath, $zipFilename, [
                'Content-Type' => 'application/zip',
                'Content-Disposition' => 'attachment; filename="' . $zipFilename . '"',
            ])->deleteFileAfterSend(true);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    private function presetComponentLevel($usm_id)
    {
        $defaultComponentLevels = config('settings.default_component_levels');
        foreach ($defaultComponentLevels as $level) {
            ComponentLevel::create(
                [
                    'usm_id' => $usm_id,
                    'id' => $level['id'],
                    'level' => $level['level'],
                    'name' => $level['name'],
                ],
            );
        }
    }
}
