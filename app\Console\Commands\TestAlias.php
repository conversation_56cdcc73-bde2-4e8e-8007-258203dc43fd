<?php

namespace App\Console\Commands;

use App\Services\CrossShipAliasResolver;
use Illuminate\Console\Command;

class TestAlias extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-alias';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Alias';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->comment('Start Test LDAP');


        $crossShipRows = [
            "RPQE,RPNZ,RPPU,RPNJ,RWQY",
            "RPPU,RPNJ",
            "RPQ5,RPP2,RPPV,RPJR,RWQZ",
            "RPPV,RPJR",
            "RPQT,RPP4,RPPX,RPNW",
            "RPPX,RPNW",
            "RPQC,RPP3,RPPW,RPNV,RWR2",
            "RPPW,RPNV",
            "RPSH,RPSW",
            "RPSH",
            "RPS<PERSON>,RPS<PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "RPS<PERSON>,RPSY",
            "RPSL,RPSY"
        ];

        $resolver = new CrossShipAliasResolver($crossShipRows);
        $this->aliasMap = $resolver->getAliasMap();
        $this->crossShipResult = $resolver->getCrossShipResult();
        $this->pivotSSPEC = $resolver->getPivotSSPEC();
        dd($crossShipRows, $this->aliasMap, $this->crossShipResult, $this->pivotSSPEC);
    }
}
