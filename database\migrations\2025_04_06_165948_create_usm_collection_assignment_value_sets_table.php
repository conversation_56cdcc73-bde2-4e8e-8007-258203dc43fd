<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('usm_collection_assignment_value_sets', function (Blueprint $table) {
            $table->id();
            $table->json('scenario_ids')->nullable(); // For ScenarioAttributes this cannot be empty will save as array
            $table->unsignedBigInteger('value_set_id');
            $table->unsignedBigInteger('usm_collection_assignment_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('usm_collection_assignment_value_sets');
    }
};
