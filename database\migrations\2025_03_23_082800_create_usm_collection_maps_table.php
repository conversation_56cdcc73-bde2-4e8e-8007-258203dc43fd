<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('usm_collection_maps', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('scenario_mode'); //NonScenarioAttributes or ScenarioAttributes;
            $table->boolean('linked')->default(false); //If true mean the collection is in use
            // $table->unsignedBigInteger('usm_scenario_id')->nullable();
            $table->unsignedBigInteger('usm_module_id')->nullable();
            $table->unsignedBigInteger('usm_id')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('usm_collection_maps');
    }
};
