<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ComponentLevelUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [];
        return  array_merge($rules, [
            'rows.*.id' => ['nullable'],
            'rows.*.level' => ['required'],
            'rows.*.name' => ['required'],
        ]);
    }
}
