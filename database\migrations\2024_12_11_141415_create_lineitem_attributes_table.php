<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lineitem_attributes', function (Blueprint $table) {
            $table->id();
            $table->boolean('active')->default(true);
            $table->smallInteger('sequence')->default(0);
            $table->string('attribute')->nullable();
            $table->string('format')->nullable();
            $table->string('value')->nullable();
            $table->text('expression')->nullable();
            $table->unsignedBigInteger('lineitem_manager_id');
            $table->unsignedBigInteger('lineitemable_id')->nullable();  //module origin id
            $table->string('lineitemable_type')->nullable();  //module origin table
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lineitem_attributes');
    }
};
