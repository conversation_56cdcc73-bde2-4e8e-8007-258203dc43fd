<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        //Create admin
        $user = User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'username' => 'admin',
            'password' => Hash::make('12345678'),
            'active' => true,
        ]);

        $this->call([
            IntelSeeder::class,
        ]);
    }
}
