<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class LineitemManager extends BaseModel
{
    use SoftDeletes;

    public $fillable = [
        'name',
        'module',
        // 'product_type_id',
        // 'product_id',
        // 'product_group_id',
        'created_user_id',
        'active',
        'approved', //No longer use, use speedbatch state instead
        'approved_user_id', //No longer use, use speedbatch state instead
        'approved_at', //No longer use, use speedbatch state instead
        // 'revision',
        // 'is_current',
        // 'previous_revision_id',
        // 'original_id',
        'speed_batch_id'
    ];

    protected $casts = [
        'active' => 'boolean',
        //'is_current' => 'boolean',
    ];

    protected $attributes = [
        'active' => true,
        //'is_current' => true,
        //'revision' => 0, //default
    ];

    protected $appends = [
        'module_label',
        'generate_table_name',
    ];

    // public function productType(): BelongsTo
    // {
    //     return $this->belongsTo(ProductType::class, 'product_type_id');
    // }

    // public function product(): BelongsTo
    // {
    //     return $this->belongsTo(Product::class, 'product_id');
    // }

    // public function productGroup(): BelongsTo
    // {
    //     return $this->belongsTo(ProductGroup::class, 'product_group_id');
    // }

    public function createdUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_user_id');
    }

    public function approvedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_user_id');
    }

    public function lineitemAttributes(): HasMany
    {
        return $this->hasMany(LineitemAttribute::class);
    }

    public function logs(): HasMany
    {
        return $this->hasMany(GenerateLineitemLog::class);
    }

    public function speedBatch(): BelongsTo
    {
        return $this->belongsTo(SpeedBatch::class, 'speed_batch_id');
    }

    //Mutator & Accessor
    public function generateTableName(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => isset($attributes['id']) ? ("lineitem_" . $attributes['id'] . "_" . $attributes['speed_batch_id']) : null
        );
    }

    public function moduleLabel(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => isset($attributes['module']) ? config('settings.modules')[$attributes['module']] : null
        );
    }

    public function originalId(): Attribute
    {
        //If row is the original, direct use own ID
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => isset($attributes['original_id']) ? $attributes['original_id'] : $attributes['id']
        );
    }

    /*
    * Append on demand, reduce query load
    */
    public function isGenerating(): Attribute
    {
        $log = $this->logs()->isGenerating($this->id)->first();
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => $log != null
        );
    }

    public function setApprove(): self
    {
        //Set other is_current to false
        /*
        self::where('original_id', $this->original_id)
            ->where('is_current', true)->update(['is_current' => false]);
        $this->update([
            'is_current' => true,
            'approved' => true,
            'approved_user_id' => auth()->id(),
            'approved_at' => now(),
        ]);
        */

        $this->update([
            'approved' => true,
            'approved_user_id' => auth()->id(),
            'approved_at' => now(),
        ]);

        return $this;
    }

    public function getTableModal(): SpeedDynamic
    {
        $baseModal = new SpeedDynamic;
        return $baseModal->setTable($this->generate_table_name);
    }

    //Scope function Below
    // Scope to filter by user permissions
    public function scopeAccessibleBy($query, User $user)
    {
        $permissions = $user->getAllProductPermissions();

        return $query->where(function ($query) use ($permissions) {
            // Case 1: When there are specific product group permissions
            if (!empty($permissions['productGroups'])) {
                // Only allow specifically permitted product groups
                $query->whereIn('product_group_id', array_keys($permissions['productGroups']));
            } else {
                // Case 2: When no specific product group permissions
                if (!empty($permissions['products'])) {
                    $query->where(function ($q) use ($permissions) {
                        $q->whereIn('product_id', array_keys($permissions['products']));
                    });
                }

                if (!empty($permissions['productTypes'])) {
                    $query->where(function ($q) use ($permissions) {
                        $q->whereIn('product_type_id', array_keys($permissions['productTypes']));
                    });
                }
            }
        });
    }

    public function scopeOfAllProductId($query, $product_type_id, $product_id, $product_group_id)
    {
        return $query->where('product_type_id', $product_type_id)
            ->where('product_id', $product_id)
            ->where('product_group_id', $product_group_id);
    }

    public function scopeOfCurrent($query)
    {
        return $query->where('is_current', true)->where('active', true);
    }

    // Helper method to check if a specific user has access to this lineitem
    public function isAccessibleBy(User $user): bool
    {
        // Get user's permissions
        $permissions = $user->getAllProductPermissions();

        // Check product type level access
        if (
            isset($this->product_type_id) &&
            isset($permissions['productTypes'][$this->product_type_id])
        ) {
            return true;
        }

        // Check product level access
        if (
            isset($this->product_id) &&
            isset($permissions['products'][$this->product_id])
        ) {
            // If user has product access and no specific product group permissions,
            // they have access to all product groups under this product
            if (empty($permissions['productGroups']) || is_null($this->product_group_id)) {
                return true;
            }
        }

        // Check specific product group access
        if (
            isset($this->product_group_id) &&
            isset($permissions['productGroups'][$this->product_group_id])
        ) {
            return true;
        }

        return false;
    }

    //Static Function Belows
    public static function header()
    {
        $headers = [];
        return array_merge($headers, [
            ['field' => 'name', 'title' => 'Name', 'sortable' => false],
            ['field' => 'module', 'title' => 'Module', 'sortable' => true],
            ['field' => '', 'title' => 'Created By'],
            ['field' => 'created_at', 'title' => 'Created At'],
        ]);
    }

    /*
    // Version history methods
    public function createNewVersion(array $data): self
    {
        $latest_revision = self::getLatestRevision($this->original_id ?? $this->id);

        $data = array_merge($data, [
            'revision' => $latest_revision == null ? 1 : $latest_revision->revision + 1,
            'is_current' => false,
            'previous_revision_id' => $this->id,
            'created_user_id' => auth()->id(),
            'original_id' => $latest_revision == null ? $this->id : $latest_revision->original_id,
        ]);

        // Create new version
        return self::create($data);
    }

    public static function getRevisionHistory(int $id)
    {
        return self::where('id', $id)
            ->orWhere(function ($query) use ($id) {
                $query->where(function ($q) use ($id) {
                    $manager = self::find($id);
                    $previousIds = [];
                    while ($manager && $manager->previous_revision_id) {
                        $previousIds[] = $manager->previous_revision_id;
                        $manager = $manager->previousVersion;
                    }
                    if (!empty($previousIds)) {
                        $q->whereIn('id', $previousIds);
                    }
                });
            })
            ->orderBy('revision', 'desc')
            ->get();
    }

    public static function getLatestRevision(int $id)
    {
        return self::where('original_id', $id)
            ->orderBy('revision', 'desc')
            ->select('revision', 'original_id')
            ->first();
    }
    */
}
