<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class LineitemAttribute extends BaseModel
{
    use SoftDeletes;

    public $fillable = ['active', 'attribute', 'format', 'value', 'expression', 'lineitem_manager_id', 'revision', 'previous_revision_id'];

    protected $casts = [
        'active' => 'boolean',
    ];

    public function manager(): BelongsTo
    {
        return $this->belongsTo(LineitemManager::class);
    }

    public function columnName(): Attribute
    {
        //Do addtional processing, if required
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => $attributes['attribute']
        );
    }
}
