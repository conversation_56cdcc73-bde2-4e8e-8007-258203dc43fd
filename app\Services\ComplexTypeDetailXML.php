<?php

namespace App\Services;

use App\Models\ComponentType;
use Illuminate\Support\Facades\Storage;
use SimpleXMLElement;

class ComplexTypeDetailXML
{
    /**
     * @var SimpleXMLElement
     */
    protected $xml;

    /**
     * Constructor loads and parses the upload.xml file
     */
    public function __construct()
    {
        try {
            //Future might have multiple sources, current just use first one, 
            //below logic does not support multiple sources yet
            $sourceTypes = ComponentType::sourceTypes();

            $file = Storage::disk('public')->path('component_types/upload-' . $sourceTypes[0] . '.xml');
            $xml_string = file_get_contents($file);
            $this->xml = new SimpleXMLElement($xml_string);
        } catch (\Exception $e) {
            throw new \Exception("Failed to load XML file: " . $e->getMessage());
        }
    }

    /**
     * Find a ComponentType by name and return its IOParams
     *
     * @param string $componentTypeName
     * @return array|null
     */
    public function getIOParamsByComponentTypeName($componentTypeName)
    {
        try {
            // The XML uses a default namespace, so we need to use local-name() in XPath
            // to find elements regardless of their namespace
            $xpath = "//*[local-name()='ComponentType' and @name='{$componentTypeName}']";
            $componentTypes = $this->xml->xpath($xpath);

            if (empty($componentTypes)) {
                return null;
            }

            $componentType = $componentTypes[0];

            // Get the IOParams section
            $ioParams = [];

            // Use local-name() for child elements as well due to namespace
            $ioParamsElements = $componentType->xpath("./*[local-name()='IOParams']");
            if (!empty($ioParamsElements)) {
                $ioParamsElement = $ioParamsElements[0];

                // Process NonScenarioAttributes
                $nonScenarioElements = $ioParamsElement->xpath("./*[local-name()='NonScenarioAttributes']");
                if (!empty($nonScenarioElements)) {
                    $ioParams[XML_NONSCENARIO] = $this->processAttributes($nonScenarioElements[0]);
                }

                // Process ScenarioAttributes
                $scenarioElements = $ioParamsElement->xpath("./*[local-name()='ScenarioAttributes']");
                if (!empty($scenarioElements)) {
                    $ioParams[XML_SCENARIO] = $this->processAttributes($scenarioElements[0]);
                }
            }

            return $ioParams;
        } catch (\Exception $e) {
            throw new \Exception("Error processing ComponentType {$componentTypeName}: " . $e->getMessage());
        }
    }

    /**
     * Process InputAttribute elements to convert to array structure
     *
     * @param SimpleXMLElement $attributesElement
     * @return array
     */
    protected function processAttributes($attributesElement)
    {
        $attributes = [];

        // Use XPath with local-name() to find all InputAttribute elements
        $inputAttributes = $attributesElement->xpath("./*[local-name()='InputAttribute']");

        foreach ($inputAttributes as $attribute) {
            $attributeData = [
                'name' => (string)$attribute['name'],
                'description' => (string)$attribute['description'],
            ];

            if (isset($attribute['compnent_sub_type_class'])) {
                $attributeData['component_sub_type_class'] = (string)$attribute['compnent_sub_type_class'];
            }

            if (isset($attribute['compnent_sub_type_category'])) {
                $attributeData['compnent_sub_type_category'] = (string)$attribute['compnent_sub_type_category'];
            }

            // Process SimpleValue if available
            $simpleValues = $attribute->xpath("./*[local-name()='SimpleValue']");
            if ($simpleValues && count($simpleValues) > 0) {
                $simpleValue = $simpleValues[0];

                $attributeData['type'] = (string)$simpleValue['type'];
                $attributeData['value'] = (string)$simpleValue['atdd_value'];
                $attributeData['units'] = (string)$simpleValue['units'];

                if (isset($simpleValue['type_ext'])) {
                    $attributeData['type_ext'] = (string)$simpleValue['type_ext'];
                }
            }

            // Process ComplexValue if available
            $complexValues = $attribute->xpath("./*[local-name()='ComplexValue']");
            if ($complexValues && count($complexValues) > 0) {
                $complexValuesData = [];

                // For each ComplexValue element
                foreach ($complexValues as $complexValue) {
                    $valueElements = $complexValue->xpath("./*[local-name()='Value']");

                    // Process each Value element within ComplexValue
                    foreach ($valueElements as $valueElement) {
                        $valueData = [
                            'type' => (string)$valueElement['type'],
                            'value' => (string)$valueElement['atdd_value'],
                            'units' => (string)$valueElement['units'],
                            'complex_name' => (string)$valueElement['name'],
                            'complex_description' => (string)$valueElement['description'],
                        ];

                        $complexValuesData[] = $valueData;
                    }
                }

                // Store complex values in their own key
                $attributeData['complex_values'] = $complexValuesData;
            }

            $attributes[] = $attributeData;
        }

        return $attributes;
    }

    /**
     * Get the full XML detail for a ComponentType by name
     *
     * @param string $componentTypeName
     * @return array|null
     */
    public function getDetailByComponentTypeName($componentTypeName)
    {
        try {
            // The XML uses a default namespace, so we need to use local-name() in XPath
            // to find elements regardless of their namespace
            $xpath = "//*[local-name()='ComponentType' and @name='{$componentTypeName}']";
            $componentTypes = $this->xml->xpath($xpath);

            if (empty($componentTypes)) {
                return null;
            }

            $componentType = $componentTypes[0];

            // Convert the XML element to an array
            $result = $this->xmlToArray($componentType);

            // Add the original XML string for display
            $result['xml_content'] = $componentType->asXML();

            return $result;
        } catch (\Exception $e) {
            throw new \Exception("Error processing ComponentType detail {$componentTypeName}: " . $e->getMessage());
        }
    }

    /**
     * Convert a SimpleXMLElement to an array (recursive)
     *
     * @param SimpleXMLElement $xml
     * @return array
     */
    protected function xmlToArray(SimpleXMLElement $xml)
    {
        $result = [];

        // Add attributes
        foreach ($xml->attributes() as $key => $value) {
            $result['@attributes'][$key] = (string) $value;
        }

        // Add children elements
        $children = $xml->children();
        if (!empty($children)) {
            foreach ($children as $name => $child) {
                $childArray = $this->xmlToArray($child);

                // Handle multiple children with the same tag name
                if (isset($result[$name])) {
                    if (!is_array($result[$name]) || !isset($result[$name][0])) {
                        $result[$name] = [$result[$name]];
                    }
                    $result[$name][] = $childArray;
                } else {
                    $result[$name] = $childArray;
                }
            }
        } else {
            // If no children, just use the string value
            $value = trim((string) $xml);
            if (!empty($value)) {
                $result['value'] = $value;
            }
        }

        return $result;
    }

    /**
     * Get all component type names from the XML
     *
     * @return array
     */
    public function getAllComponentTypeNames()
    {
        $names = [];
        $componentTypes = $this->xml->xpath("//*[local-name()='ComponentType']");

        foreach ($componentTypes as $componentType) {
            $names[] = (string)$componentType['name'];
        }

        return $names;
    }

    /**
     * Find a specific attribute by component type name, scenario mode, and attribute name
     *
     * @param string $componentTypeName
     * @param string $scenarioMode (NonScenarioAttributes or ScenarioAttributes)
     * @param string $attributeName
     * @return array|null
     */
    public function getIOParamsAttribute($componentTypeName, $scenarioMode, $attributeName)
    {
        try {
            // Get all IO params for this component type
            $ioParams = $this->getIOParamsByComponentTypeName($componentTypeName);

            if (!$ioParams || !isset($ioParams[$scenarioMode]) || !is_array($ioParams[$scenarioMode])) {
                return null;
            }

            // Find the specific attribute by name
            foreach ($ioParams[$scenarioMode] as $attribute) {
                if (isset($attribute['name']) && $attribute['name'] === $attributeName) {
                    return $attribute;
                }
            }

            return null;
        } catch (\Exception $e) {
            throw new \Exception("Error finding attribute {$attributeName} in {$scenarioMode} for ComponentType {$componentTypeName}: " . $e->getMessage());
        }
    }
}
