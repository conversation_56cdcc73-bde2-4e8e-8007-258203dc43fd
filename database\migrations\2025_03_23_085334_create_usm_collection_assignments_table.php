<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('usm_collection_assignments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('collection_id')->nullable();
            // $table->json('value_set_ids')->nullable();
            $table->json('component_ids')->nullable();
            $table->smallInteger('sequence')->default(0);
            $table->unsignedBigInteger('usm_collection_condition_id');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('usm_collection_assignments');
    }
};
