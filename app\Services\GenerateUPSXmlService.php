<?php

namespace App\Services;

use App\Models\Component;
use App\Models\USM;
use App\Models\USMModuleComponentAttribute;

class GenerateUPSXmlService
{
    public $usm_id;
    protected $usm;
    protected $lineDatabase;
    protected $lines;
    protected $firstLine;
    protected $components;
    protected $uniqueSSPEC = [];
    protected $aliasMap = [];
    protected $crossShipResult = [];
    protected $pivotSSPEC = [];
    protected $usedFinishedGoodsByScenario = []; //to remove uniqueSSPEC that is not using
    protected $groupByComponent = [];
    protected $groupByScenarioComponent = [];

    public function __construct(int $usm_id)
    {
        $this->usm_id = $usm_id;
        $this->usm = USM::with(['components', 'productGroup', 'modules', 'scenarios', 'createdUser', 'binMatrix.binMatrixItems'])->find($usm_id);
        $this->lineDatabase = new LineDatabase($this->usm->binMatrix->speed_batch_id);
        $allLines = $this->lineDatabase->getModel()->get();
        $this->firstLine = $allLines->first();
        $this->lines = $allLines->keyBy('QDF/SSPEC');

        //Get all components for this USM
        $this->components = Component::where('usm_id', $this->usm_id)->orderBy('level')->get();
        $groupByComponent = [];
        $groupByScenarioComponent = [];
        foreach ($this->usm->modules as $module) {
            $collectionMapping = new CollectionMappingService($this->usm_id, $module->id, $module->scenario_mode);
            $mappedCollectionValues = $collectionMapping->getMappedCollectionForValues();

            if ($module->scenario_mode === XML_NONSCENARIO) {
                foreach ($this->components as $component) {
                    $attributes = USMModuleComponentAttribute::with('values')->where('usm_module_id', $module->id)
                        ->where('usm_id', $this->usm_id)
                        ->where('scenario_mode', XML_NONSCENARIO)->get();

                    foreach ($attributes as $attribute) {
                        foreach ($attribute->values as $value) {
                            if ($value->component_id == $component->id) {
                                $collections = null;
                                if (isset($mappedCollectionValues[$component->id][$attribute->id])) {
                                    $collections = $mappedCollectionValues[$component->id][$attribute->id];
                                }

                                // unify parameter handling for both new and existing entries
                                $compKey = $component->name;
                                $attrKey = $attribute->name;
                                $entry = $groupByComponent[$compKey][$attrKey] ?? [
                                    'attribute'  => $attribute->attribute,
                                    'name'       => $attribute->name,
                                    'value'      => $value->input_value,
                                    'is_complex' => $attribute->is_complex,
                                    'parameters' => [],
                                    'is_finished_good_item' => false,
                                    'finished_good_item_values' => []
                                ];
                                $parameters = $entry['parameters'];

                                if ($attribute->is_complex) {
                                    if (!empty($collections)) {
                                        usort($collections, function ($a, $b) {
                                            return $a['value_set']['sequence'] - $b['value_set']['sequence'];
                                        });
                                        foreach ($collections as $collection) {
                                            $parameters[$collection['value_set']['sequence']][$attribute->parameter] = ['value' => $collection['input_value'], 'is_finished_good_item' => false, 'finished_good_item_values' => []];
                                        }
                                    } else {
                                        $parameters[][$attribute->parameter] = ['value' => $value->input_value, 'is_finished_good_item' => false, 'finished_good_item_values' => []];
                                    }
                                }

                                $entry['parameters'] = $parameters;
                                $groupByComponent[$compKey][$attrKey] = $entry;
                            }
                        }
                    }
                }
            } else {
                foreach ($this->usm->scenarios as $scenario) {
                    foreach ($this->components as $component) {
                        $attributes = USMModuleComponentAttribute::with(['values' => function ($q) use ($scenario) {
                            $q->where('usm_scenario_id', $scenario->id);
                        }])->where('usm_module_id', $module->id)
                            ->where('usm_id', $this->usm_id)
                            ->where('scenario_mode', XML_SCENARIO)
                            ->get();

                        foreach ($attributes as $attribute) {
                            foreach ($attribute->values as $value) {
                                if ($value->component_id == $component->id) {
                                    $collections = null;
                                    if (isset($mappedCollectionValues[$component->id][$attribute->id])) {
                                        $collections = $mappedCollectionValues[$component->id][$attribute->id];
                                    }

                                    // unify parameter handling for both new and existing entries
                                    $compKey = $component->name;
                                    $attrKey = $attribute->name;
                                    $entry = $groupByScenarioComponent[$scenario->name][$compKey][$attrKey] ?? [
                                        'attribute'  => $attribute->attribute,
                                        'name'       => $attribute->name,
                                        'value'      => $value->input_value,
                                        'collections' => [], //Use for Scenario
                                        'is_finished_good_item' => false,
                                        'finished_good_item_values' => []
                                    ];
                                    $entryCollections = $entry['collections'];


                                    if (!empty($collections)) {
                                        foreach ($collections as $collection) {
                                            if (in_array($scenario->id, $collection['scenario_ids'])) {
                                                $entryCollections[] = ['value' => $collection['input_value'], 'is_finished_good_item' => false, 'finished_good_item_values' => []];
                                            }
                                        }
                                    }


                                    $entry['collections'] = $entryCollections;
                                    $groupByScenarioComponent[$scenario->name][$compKey][$attrKey] = $entry;
                                }
                            }
                        }
                    }
                }
            }
        }

        $this->groupByComponent = $groupByComponent;
        $this->groupByScenarioComponent = $groupByScenarioComponent;
    }

    public function generateAll()
    {
        $file_paths = [];
        foreach ($this->usm->binMatrix->binMatrixItems as $binMatrixItem) {
            //Create Raw CrossShip First
            $crossShipRows = [];
            foreach ($binMatrixItem->binMatrixFlows as $binMatrixFlow) {
                $crossShipRows[] = $binMatrixFlow->cross_ship;

                $this->uniqueSSPEC = array_merge($this->uniqueSSPEC, array_map('trim', explode(',', $binMatrixFlow->cross_ship)));
            }
            $this->uniqueSSPEC = array_values(array_unique($this->uniqueSSPEC));

            // $crossShipRows = [
            //     "RPQE,RPNZ,RPPU,RPNJ,RWQY",
            //     "RPPU,RPNJ",
            //     "RPQ5,RPP2,RPPV,RPJR,RWQZ",
            //     "RPPV,RPJR",
            //     "RPQT,RPP4,RPPX,RPNW",
            //     "RPPX,RPNW",
            //     "RPQC,RPP3,RPPW,RPNV,RWR2",
            //     "RPPW,RPNV",
            //     "RPSH,RPSW",
            //     "RPSH",
            //     "RPSJ,RPST",
            //     "RPSJ",
            //     "RPSK,RPSU",
            //     "RPSK",
            //     "RPSL,RPSY",
            //     "RPSL,RPSY"
            // ];

            //Create Alias Map
            $resolver = new CrossShipAliasResolver($crossShipRows);
            $this->aliasMap = $resolver->getAliasMap();
            $this->crossShipResult = $resolver->getCrossShipResult();
            $this->pivotSSPEC = $resolver->getPivotSSPEC();
            //dd($crossShipRows, $this->aliasMap, $this->crossShipResult, $this->pivotSSPEC);

            foreach ($binMatrixItem->bomListExplode as $bomName) {
                $xml = $this->generateUPSXML($bomName, $binMatrixItem);
                $file_paths[] = $this->saveXMLToFile($bomName, $xml);
            }
        }

        return $file_paths;
    }

    /**
     * Generate the UPS export XML file
     * 
     * @return string The XML document as a string
     */
    public function generateUPSXML($bomName, $binMatrixItem)
    {
        if (!$this->usm) {
            throw new \Exception("USM with ID {$this->usm_id} not found");
        }

        // Create a new XML document
        $dom = new \DOMDocument('1.0', 'utf-8');
        $dom->formatOutput = true;

        // Create the root element <UPSAttributeData>
        $rootElement = $dom->createElement('UPSAttributeData');
        $dom->appendChild($rootElement);

        // Create Info element
        $infoElement = $dom->createElement('Info');
        $rootElement->appendChild($infoElement);

        // Add child elements to Info - these would be replaced with actual data
        $infoFields = [
            'Suite' => $this->usm->name . ' (' . $this->usm->revision . ' - ' . $this->usm->state . ')',
            'UpsTemplate' => 'UPSUpload_S144.0',
            'BinMatrix' => isset($this->usm->binMatrix) ? $this->usm->binMatrix->name . ' (' . $this->usm->binMatrix->revision . ' - ' . $this->usm->binMatrix->state . ')' : '',
            'FuseFileRelease' => '', // Replace with actual data
            'Structure' => '', // Replace with actual data
        ];

        foreach ($infoFields as $fieldName => $fieldValue) {
            $fieldElement = $dom->createElement($fieldName);
            $fieldElement->appendChild($dom->createTextNode($fieldValue));
            $infoElement->appendChild($fieldElement);
        }

        foreach ($this->usm->modules as $module) {
            $fieldElement = $dom->createElement("Module");
            $fieldElement->setAttribute('type', $module->keyword);
            $fieldElement->appendChild($dom->createTextNode($module->name));
            $infoElement->appendChild($fieldElement);
        }

        $infoFields2 = [
            'CreatedBy' => $this->usm->createdUser ? $this->usm->createdUser->name : 'system',
            'CreatedDateTime' => $this->usm->created_at ? $this->usm->created_at->format('n/j/Y g:i:s A \U\T\C') : date('n/j/Y g:i:s A \U\T\C'),
            'FileFormat' => '3',
        ];

        foreach ($infoFields2 as $fieldName => $fieldValue) {
            $fieldElement = $dom->createElement($fieldName);
            $fieldElement->appendChild($dom->createTextNode($fieldValue));
            $infoElement->appendChild($fieldElement);
        }

        $this->structureMap($dom, $rootElement);
        $this->crossShip($dom, $rootElement, $bomName, $binMatrixItem);

        $conditionsElement = $dom->createElement('Conditions');
        $rootElement->appendChild($conditionsElement);

        $this->lineItemAliases($dom, $rootElement);

        $this->nonScenario($dom, $rootElement);

        $this->scenario($dom, $rootElement);

        return $dom->saveXML();
    }

    private function structureMap($dom, $rootElement)
    {
        //Structure Map
        $structureMapElement = $dom->createElement('StructureMap');
        $rootElement->appendChild($structureMapElement);
        foreach ($this->pivotSSPEC as $sspec) {
            $structureElement = $dom->createElement('FinishedGoodItem');
            $structureElement->setAttribute('name', $sspec);
            $structureElement->appendChild($dom->createTextNode($this->usm->productGroup->name));
            $structureMapElement->appendChild($structureElement);
        }
    }

    private function crossShip($dom, $rootElement, $bomName, $binMatrixItem)
    {
        //Cross Ship
        $crossShipElement = $dom->createElement('CrossShip');
        $rootElement->appendChild($crossShipElement);
        foreach ($binMatrixItem->binMatrixFlows as $index => $flow) {
            $crossShipItem = $dom->createElement('TestItem');
            $crossShipItem->setAttribute('bom', $bomName);
            $crossShipItem->setAttribute('priority', $flow->flow);
            $crossShipItem->setAttribute('passbin', $flow->pass_bin);
            $crossShipItem->setAttribute('olb_bin', $flow->olb_bin);
            if (isset($this->crossShipResult[$index])) {
                $crossShipItem->appendChild($dom->createTextNode(implode(',', $this->crossShipResult[$index])));
            }
            $crossShipElement->appendChild($crossShipItem);
        }
    }

    private function lineItemAliases($dom, $rootElement)
    {
        //Line Item Aliases
        $lineItemAliasesElement = $dom->createElement('LineItemAliases');
        $rootElement->appendChild($lineItemAliasesElement);
        foreach ($this->aliasMap as $key => $alias) {
            $aliasesElement = $dom->createElement('Aliases');
            $aliasesElement->setAttribute('name', $key);
            foreach ($alias as $alias) {
                $aliasElement = $dom->createElement('Alias');
                $aliasElement->appendChild($dom->createTextNode($alias));
                $aliasesElement->appendChild($aliasElement);
            }
            $lineItemAliasesElement->appendChild($aliasesElement);
        }
    }

    private function nonScenario($dom, $rootElement)
    {
        //Non-Scenario
        $nonScenarioAttributesElement = $dom->createElement('NonScenarioAttributes');
        $rootElement->appendChild($nonScenarioAttributesElement);
        foreach ($this->usm->components as $component) {
            $componentElement = $dom->createElement('Component');
            $componentElement->setAttribute('name', $component->name);
            $nonScenarioAttributesElement->appendChild($componentElement);

            if (isset($this->groupByComponent[$component->name])) {
                foreach ($this->groupByComponent[$component->name] as $inputAttribute) {
                    $inputAttributeElement = $dom->createElement('InputAttribute');
                    $inputAttributeElement->setAttribute('name', $inputAttribute['name']);
                    $componentElement->appendChild($inputAttributeElement);

                    $newInputAttribute = $this->modifyValueWithExpression($inputAttribute);
                    if ($newInputAttribute['is_complex']) {
                        $allElement = $dom->createElement('All');
                        $inputAttributeElement->appendChild($allElement);
                        $complexValueElement = $dom->createElement('ComplexValue');
                        $allElement->appendChild($complexValueElement);
                        $parameters = array_values($newInputAttribute['parameters']); //Make the index start from 0

                        foreach ($parameters as $index => $valueGroup) {
                            $valueGroupElement = $dom->createElement('ValueGroup');
                            $valueGroupElement->setAttribute('index', $index);
                            $complexValueElement->appendChild($valueGroupElement);
                            foreach ($valueGroup as $attribute => $collectionValue) {
                                if ($collectionValue['is_finished_good_item']) {
                                    foreach ($collectionValue['finished_good_item_values'] as $sspec => $value) {
                                        $finishedGoodItemElement = $dom->createElement('FinishedGoodItem');
                                        $finishedGoodItemElement->setAttribute('name', $sspec);
                                        $valueElement = $dom->createElement('Value');
                                        $valueElement->setAttribute('name', $attribute);
                                        $valueElement->appendChild($dom->createTextNode($value));
                                        $finishedGoodItemElement->appendChild($valueElement);
                                        $valueGroupElement->appendChild($finishedGoodItemElement);
                                    }
                                } else {
                                    $valueElement = $dom->createElement('Value');
                                    $valueElement->setAttribute('name', $attribute);
                                    $valueGroupElement->appendChild($valueElement);
                                    $valueElement->appendChild($dom->createTextNode($collectionValue['value']));
                                }
                            }
                        }
                    } else {
                        if ($newInputAttribute['is_finished_good_item']) {
                            foreach ($newInputAttribute['finished_good_item_values'] as $sspec => $value) {
                                $finishedGoodItemElement = $dom->createElement('FinishedGoodItem');
                                $finishedGoodItemElement->setAttribute('name', $sspec);
                                $simpleValueElement = $dom->createElement('SimpleValue');
                                $simpleValueElement->appendChild($dom->createTextNode($value));
                                $finishedGoodItemElement->appendChild($simpleValueElement);
                                $inputAttributeElement->appendChild($finishedGoodItemElement);
                            }
                        } else {
                            $allElement = $dom->createElement('All');
                            $inputAttributeElement->appendChild($allElement);
                            $simpleValueElement = $dom->createElement('SimpleValue');
                            $simpleValueElement->appendChild($dom->createTextNode($newInputAttribute['value']));
                            $allElement->appendChild($simpleValueElement);
                        }
                    }
                }
            }
        }
    }

    private function scenario($dom, $rootElement)
    {
        //ScenarioAttribute
        $scenarioAttributesElement = $dom->createElement('ScenarioAttributes');
        $rootElement->appendChild($scenarioAttributesElement);

        foreach ($this->usm->scenarios as $scenario) {
            $this->usedFinishedGoodsByScenario = [];

            $scenarioElement = $dom->createElement('Scenario');
            $scenarioElement->setAttribute('name', $scenario->name);
            $scenarioAttributesElement->appendChild($scenarioElement);

            //Organized the data first only then populate in below 3 functions
            $organizedData = $this->organizeComponentScenarioData($scenario);

            $this->disabledObjects($dom, $scenarioElement, $scenario);
            $this->disabledFinishedGoods($dom, $scenarioElement);
            $this->scenarioComponentAttributes($dom, $scenarioElement, $scenario, $organizedData);
        }
    }

    private function organizeComponentScenarioData($scenario)
    {
        $this->usedFinishedGoodsByScenario = [];
        $organizedData = [];
        $scenarioKey = $scenario->name;
        if (!isset($this->groupByScenarioComponent[$scenarioKey])) {
            return $organizedData;
        }
        foreach ($this->groupByScenarioComponent[$scenarioKey] as $componentName => $attributes) {
            foreach ($attributes as $inputAttribute) {
                $newInputAttribute = $this->modifyValueWithExpression($inputAttribute);
                // Record used finished goods
                if (!empty($newInputAttribute['collections'])) {
                    foreach ($newInputAttribute['collections'] as $collection) {
                        if (!empty($collection['is_finished_good_item'])) {
                            foreach ($collection['finished_good_item_values'] as $sspec => $value) {
                                $this->usedFinishedGoodsByScenario[] = $sspec;
                            }
                        }
                    }
                } elseif (!empty($newInputAttribute['is_finished_good_item'])) {
                    foreach ($newInputAttribute['finished_good_item_values'] as $sspec => $value) {
                        $this->usedFinishedGoodsByScenario[] = $sspec;
                    }
                }
                $organizedData[$componentName][] = $newInputAttribute;
            }
        }
        $this->usedFinishedGoodsByScenario = array_values(array_unique($this->usedFinishedGoodsByScenario));
        return $organizedData;
    }

    private function disabledObjects($dom, $scenarioElement, $scenario)
    {
        //Return only ID from $scenario->components;
        $enableComponentIds = $scenario->components->pluck('id')->toArray();

        $disabledObjectsElement = $dom->createElement('DisabledObjects');
        $scenarioElement->appendChild($disabledObjectsElement);
        foreach ($this->components as $component) {
            if (!in_array($component->id, $enableComponentIds)) {
                $disabledObjectElement = $dom->createElement('Object');
                $disabledObjectElement->setAttribute('name', $component->name);
                $disabledObjectsElement->appendChild($disabledObjectElement);
            }
        }
    }

    private function disabledFinishedGoods($dom, $scenarioElement)
    {
        if (empty($this->uniqueSSPEC)) return;
        $finishedGoods = array_diff($this->uniqueSSPEC, $this->usedFinishedGoodsByScenario);
        if (empty($finishedGoods)) return;

        //Temporary load all, need to have a logic where used sspec should not show here.
        $disabledFinishedGoodsElement = $dom->createElement('DisabledFinishedGoods');
        $scenarioElement->appendChild($disabledFinishedGoodsElement);
        foreach ($finishedGoods as $finishedGood) {
            $disabledFinishedGoodElement = $dom->createElement('FinishedGood');
            $disabledFinishedGoodElement->setAttribute('name', $finishedGood);
            $disabledFinishedGoodsElement->appendChild($disabledFinishedGoodElement);
        }
    }

    private function scenarioComponentAttributes($dom, $scenarioElement, $scenario, $organizedData)
    {
        foreach ($organizedData as $componentName => $attributes) {
            $componentElement = $dom->createElement('Component');
            $componentElement->setAttribute('name', $componentName);
            $scenarioElement->appendChild($componentElement);

            foreach ($attributes as $attribute) {
                $inputAttributeElement = $dom->createElement('InputAttribute');
                $inputAttributeElement->setAttribute('name', $attribute['name']);
                $componentElement->appendChild($inputAttributeElement);

                if (!empty($attribute['collections'])) {
                    foreach ($attribute['collections'] as $collection) {
                        if (!empty($collection['is_finished_good_item'])) {
                            foreach ($collection['finished_good_item_values'] as $sspec => $value) {
                                $finishedGoodItemElement = $dom->createElement('FinishedGoodItem');
                                $finishedGoodItemElement->setAttribute('name', $sspec);
                                $simpleValueElement = $dom->createElement('SimpleValue');
                                $simpleValueElement->appendChild($dom->createTextNode($value));
                                $finishedGoodItemElement->appendChild($simpleValueElement);
                                $inputAttributeElement->appendChild($finishedGoodItemElement);
                            }
                        } else {
                            $allElement = $dom->createElement('All');
                            $inputAttributeElement->appendChild($allElement);
                            $simpleValueElement = $dom->createElement('SimpleValue');
                            $simpleValueElement->appendChild($dom->createTextNode($collection['value']));
                            $allElement->appendChild($simpleValueElement);
                        }
                    }
                } else {
                    if (!empty($attribute['is_finished_good_item'])) {
                        foreach ($attribute['finished_good_item_values'] as $sspec => $value) {
                            $finishedGoodItemElement = $dom->createElement('FinishedGoodItem');
                            $finishedGoodItemElement->setAttribute('name', $sspec);
                            $simpleValueElement = $dom->createElement('SimpleValue');
                            $simpleValueElement->appendChild($dom->createTextNode($value));
                            $finishedGoodItemElement->appendChild($simpleValueElement);
                            $inputAttributeElement->appendChild($finishedGoodItemElement);
                        }
                    } else {
                        $allElement = $dom->createElement('All');
                        $inputAttributeElement->appendChild($allElement);
                        $simpleValueElement = $dom->createElement('SimpleValue');
                        $simpleValueElement->appendChild($dom->createTextNode($attribute['value']));
                        $allElement->appendChild($simpleValueElement);
                    }
                }
            }
        }
    }

    /**
     * Save the USM XML to a file
     * 
     * @param string $path Optional path to save the file. If null, will use storage_path.
     * @return string The full path to the saved file
     */
    public function saveXMLToFile($bomName, $xml)
    {

        $fileName = 'LIPORT_' . $bomName . '.xml';
        $path = storage_path('app/exports/usm/' . $this->usm->id . '/' . $fileName);

        // Create directory if it doesn't exist
        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
        }
        file_put_contents($path, $xml);
        return $path;
    }

    private function modifyValueWithExpression($inputAttribute)
    {
        $parser = new RowParser();
        if (isset($inputAttribute['collections']) && !empty($inputAttribute['collections'])) {
            //For scenario
            foreach ($inputAttribute['collections'] as $index => $collection) {
                if ($parser->isExpression($collection['value'])) {
                    $parser->addExpression('parsed', $collection['value']);

                    $finished_goods = [];
                    foreach ($this->lines->toArray() as $sspec => $line) {
                        if (in_array($sspec, $this->pivotSSPEC)) {
                            $parsed = $parser->parseRow($line);
                            $finished_goods[$sspec] = $parsed['parsed'];
                        }
                    }
                    //Check is the value is same for all finished goods
                    $unique_goods = array_unique(array_values($finished_goods));
                    if (count($unique_goods) == 1) {
                        $inputAttribute['collections'][$index]['value'] = $unique_goods[0];
                    } else {
                        $inputAttribute['collections'][$index]['is_finished_good_item'] = true;
                        $inputAttribute['collections'][$index]['finished_good_item_values'] = $finished_goods;
                    }
                }
            }
        } else if (isset($inputAttribute['is_complex']) && $inputAttribute['is_complex']) {
            //For non scenario
            $parameters = $inputAttribute['parameters'];
            foreach ($parameters as $index => $valueGroup) {
                foreach ($valueGroup as $attribute => $collectionValue) {
                    if ($parser->isExpression($collectionValue['value'])) {
                        $parser->addExpression('parsed', $collectionValue['value']);

                        $finished_goods = [];
                        foreach ($this->lines->toArray() as $sspec => $line) {
                            if (in_array($sspec, $this->pivotSSPEC)) {
                                $parsed = $parser->parseRow($line);
                                $finished_goods[$sspec] = $parsed['parsed'];
                            }
                        }
                        //Check is the value is same for all finished goods
                        $unique_goods = array_unique(array_values($finished_goods));
                        if (count($unique_goods) == 1) {
                            $inputAttribute['parameters'][$index][$attribute]['value'] = $unique_goods[0];
                        } else {
                            $inputAttribute['parameters'][$index][$attribute]['is_finished_good_item'] = true;
                            $inputAttribute['parameters'][$index][$attribute]['finished_good_item_values'] = $finished_goods;
                        }
                    }
                }
            }
        } else {
            if ($parser->isExpression($inputAttribute['value'])) {
                $parser->addExpression('parsed', $inputAttribute['value']);

                $finished_goods = [];
                foreach ($this->lines->toArray() as $sspec => $line) {
                    if (in_array($sspec, $this->pivotSSPEC)) {
                        $parsed = $parser->parseRow($line);
                        $finished_goods[$sspec] = $parsed['parsed'];
                    }
                }
                //Check is the value is same for all finished goods
                $unique_goods = array_unique(array_values($finished_goods));
                if (count($unique_goods) == 1) {
                    $inputAttribute['value'] = $unique_goods[0];
                } else {
                    $inputAttribute['is_finished_good_item'] = true;
                    $inputAttribute['finished_good_item_values'] = $finished_goods;
                }
            }
        }

        return $inputAttribute;
    }
}
