<?php

namespace App\Services;

use App\Models\USM;
use App\Models\USMCollection;
use App\Models\USMCollectionMap;
use App\Models\USMCollectionValue;
use App\Models\USMModule;
use App\Models\Component;
use App\Models\USMModuleComponentAttribute;
use App\Models\USMModuleComponentAttributeValue;
use App\Models\USMScenario;

class GenerateUSMXmlService
{
    public $usm_id;
    protected $usm;
    protected $available_scenarios = [];

    public function __construct(int $usm_id)
    {
        $this->usm_id = $usm_id;
        $this->usm = USM::with(['productGroup', 'modules', 'scenarios', 'createdUser'])->find($usm_id);
    }

    /**
     * Generate the USM export XML file
     * 
     * @return string The XML document as a string
     */
    public function generateUSMExportXML()
    {
        if (!$this->usm) {
            throw new \Exception("USM with ID {$this->usm_id} not found");
        }

        // Create a new XML document
        $dom = new \DOMDocument('1.0', 'utf-8');
        $dom->formatOutput = true;

        // Create the root element <Suite>
        $rootElement = $dom->createElement('Suite');
        $dom->appendChild($rootElement);

        // Create Info element
        $infoElement = $dom->createElement('Info');
        $rootElement->appendChild($infoElement);

        // Add child elements to Info - these would be replaced with actual data
        $infoFields = [
            'Suite' => $this->usm->name . ' (' . $this->usm->revision . ' - ' . $this->usm->state . ')',
            'UpsTemplate' => 'UPSUpload_S144.0',
            'BinMatrix' => isset($this->usm->binMatrix) ? $this->usm->binMatrix->name . ' (' . $this->usm->binMatrix->revision . ' - ' . $this->usm->binMatrix->state . ')' : '',
            'FuseFileRelease' => '', // Replace with actual data
            'Structure' => '', // Replace with actual data
            'CreatedBy' => $this->usm->createdUser ? $this->usm->createdUser->name : '',
            'CreatedDateTime' => $this->usm->created_at ? $this->usm->created_at->format('n/j/Y g:i:s A \U\T\C') : date('n/j/Y g:i:s A \U\T\C'),
            'FileFormat' => '3'
        ];

        foreach ($infoFields as $fieldName => $fieldValue) {
            $fieldElement = $dom->createElement($fieldName);
            $fieldElement->appendChild($dom->createTextNode($fieldValue));
            $infoElement->appendChild($fieldElement);
        }

        //Structure Map
        $structureMapElement = $dom->createElement('StructureMap');
        $rootElement->appendChild($structureMapElement);
        $structureElement = $dom->createElement('Structure');
        $structureElement->setAttribute('name', $this->usm->productGroup->name);
        $structureElement->appendChild($dom->createTextNode($this->usm->productGroup->name));
        $structureMapElement->appendChild($structureElement);


        // Add Modules section
        $modulesElement = $dom->createElement('Modules');
        $rootElement->appendChild($modulesElement);

        foreach ($this->usm->modules as $module) {
            $moduleElement = $dom->createElement('Module');
            $moduleElement->setAttribute('name', $module->name);
            $moduleElement->setAttribute('revision', $this->usm->revision);
            $moduleElement->setAttribute('state', $this->usm->state);
            $moduleElement->setAttribute('keyword', $module->keyword);
            $modulesElement->appendChild($moduleElement);

            //Build Mapping Data
            $collectionMapping = new CollectionMappingService($this->usm_id, $module->id, $module->scenario_mode, $module->scenario_id);

            // Add attributes groups to module
            if ($module->scenario_mode === XML_SCENARIO) {
                foreach ($this->usm->scenarios as $scenario) {
                    $checked_component_ids = $scenario->components()
                        ->wherePivot('usm_module_id', $module->id)
                        ->pluck('id')
                        ->toArray();
                    $scenario->checked_component_ids = $checked_component_ids;

                    if ($scenario->in_xml) {
                        $this->available_scenarios[] = $scenario;
                        $this->addAttributeGroupsToModule($dom, $moduleElement, $module, $collectionMapping, $scenario);
                    }
                }
            } else {
                $this->addAttributeGroupsToModule($dom, $moduleElement, $module, $collectionMapping);
            }


            //Add Collection
            $collections = $collectionMapping->getCollections();
            $this->addCollection($dom, $moduleElement,  $collections, $module);

            //Add Collection Map
            $collectionMap = $collectionMapping->getCollectionMap();
            $this->addCollectionMap($dom, $moduleElement, $collectionMap, $module);
        }

        return $dom->saveXML();
    }

    /**
     * Add attributes to a module XML element
     * 
     * @param \DOMDocument $dom
     * @param \DOMElement $moduleElement
     * @param USMModule $module
     */
    protected function addAttributeGroupsToModule($dom, $moduleElement, $module, $collectionMapping, $scenario = null)
    {
        $collectionValues = $collectionMapping->getMappedCollectionForValues();

        $componentAttributes = USMModuleComponentAttribute::where('usm_module_id', $module->id)
            ->where('scenario_mode', $module->scenario_mode)
            ->with(['componentType.components', 'values'])
            ->get();

        $groupedAttributes = [];
        foreach ($componentAttributes as $attr) {
            $componentType = $attr->componentType;
            if (!$componentType) continue;

            $typeId = $componentType->id;

            // Initialize component type group if it doesn't exist
            if (!isset($groupedAttributes[$typeId])) {
                $groupedAttributes[$typeId] = [
                    'id' => $componentType->id,
                    'name' => $componentType->name,
                    'active' => $componentType->active,
                    'level' => $componentType->level,
                    'type' => $componentType->type,
                    'scenario_enabled' => $componentType->scenario_enabled,
                    'sub_elements' => $componentType->sub_elements,
                    'components' => $componentType->components,
                    'source' => $componentType->source,
                    'attrs' => []
                ];
            }

            // Convert the model to an array and add it to the group
            $attrArray = $attr->toArray();

            //Set is the value be used by collection
            foreach ($attrArray['values'] as $value) {
                if ($scenario != null && $scenario->id != $value['usm_scenario_id']) {
                    continue;
                } else if ($scenario == null && $value['usm_scenario_id'] != null) {
                    continue;
                }

                $attributeId = $value['usm_module_component_attribute_id'];
                $componentId = $value['component_id'];
                $isCollection = isset($collectionValues[$componentId][$attributeId]) ? true : false;
                $value['is_collection'] = $isCollection;
                $value['collection_values'] = $isCollection ? $collectionValues[$componentId][$attributeId] : [];
                //Create new attr_values, don't re-use values
                $attrArray['attr_values'][$componentId] = $value;
            }
            $groupedAttributes[$typeId]['attrs'][] = $attrArray;
        }

        //Initialize empty values for any component that doesn't have values yet
        foreach ($groupedAttributes as &$group) {
            if (!empty($group['components'])) {
                foreach ($group['attrs'] as &$attrArray) {
                    foreach ($group['components'] as $component) {
                        if (!isset($attrArray['attr_values'][$component['id']])) {
                            // Create an empty default value structure
                            $attrArray['attr_values'][$component['id']] = [
                                'id' => null,
                                "usm_id" => $this->usm_id,
                                "component_id" => $component['id'],
                                "usm_module_component_attribute_id" => $attrArray['id'],
                                "input_value" => "",
                                "is_collection" => isset($collectionValues[$component['id']][$attrArray['id']]) ? true : false,
                                "collection_values" => isset($collectionValues[$component['id']][$attrArray['id']]) ? $collectionValues[$component['id']][$attrArray['id']] : [],
                            ];
                        }
                    }
                }
            }
        }

        $attributeGroupsElement = $dom->createElement('AttributeGroups');
        $moduleElement->appendChild($attributeGroupsElement);

        foreach ($groupedAttributes as $typeId => $typeAttributes) {
            $attributesGroupElement = $dom->createElement('AttributeGroup');
            $attributesGroupElement->setAttribute('mode', $this->outputScenarioLabel($module->scenario_mode));
            $attributesGroupElement->setAttribute('sourceType', $typeAttributes['source']);
            $attributesGroupElement->setAttribute('levelCategory', $typeAttributes['level']);
            $attributesGroupElement->setAttribute('componentGroupType', $typeAttributes['name']);
            if ($scenario != null) {
                $attributesGroupElement->setAttribute('scenario', $scenario->name);
            }
            $attributeGroupsElement->appendChild($attributesGroupElement);

            if ($scenario != null) {
                $scenarioEnablingExpressionElement = $dom->createElement('ScenarioEnablingExpression');
                $scenarioEnablingExpressionElement->appendChild($dom->createTextNode($scenario->expression));
                $attributesGroupElement->appendChild($scenarioEnablingExpressionElement);
            }

            // Add attributes and values
            $this->addAttributesToAttributeGroup($dom, $attributesGroupElement, $typeAttributes, $module, $scenario);
        }
    }

    /**
     * Add attributes and their values to a component XML element
     * 
     * @param \DOMDocument $dom
     * @param \DOMElement $attributesGroupElement
     * @param array $typeAttributes
     * @param USMModule $module
     */
    protected function addAttributesToAttributeGroup($dom, $attributesGroupElement, $typeAttributes, $module, $scenario = null)
    {
        $attributesElement = $dom->createElement('Attributes');
        $attributesGroupElement->appendChild($attributesElement);

        foreach ($typeAttributes['attrs'] as $attribute) {
            $attributeElement = $dom->createElement('Attribute');
            $attributeElement->setAttribute('name', $attribute['name']);
            if (isset($attribute['parameter']) && !empty($attribute['parameter'])) {
                $attributeElement->setAttribute('parameter', $attribute['parameter']);
            }
            $attributesElement->appendChild($attributeElement);

            if ($module->scenario_mode === XML_NONSCENARIO) {
                //Add non-scenario values
                $this->addNonScenarioValues($dom, $attributeElement, $attribute, $module);
            } else {
                //Add scenario values
                $this->addScenarioValues($dom, $attributeElement, $attribute, $module, $scenario);
            }
        }
    }

    /**
     * Add non-scenario attributes to the attributes element
     * 
     * @param \DOMDocument $dom
     * @param \DOMElement $attributesElement
     * @param array $attributes
     * @param USMModule $module
     */
    protected function addNonScenarioValues($dom, $attributeElement, $attribute, $module)
    {
        $valuesElement = $dom->createElement('Values');
        $attributeElement->appendChild($valuesElement);

        if (isset($attribute['component_type']) && isset($attribute['component_type']['components'])) {
            foreach ($attribute['component_type']['components'] as $component) {
                if ($attribute['attr_values'][$component['id']]['is_collection'] == false) {
                    $valueElement = $dom->createElement('Value');
                    $valueElement->setAttribute('componentGroup', $component['name']);
                    $valueElement->appendChild($dom->createTextNode($attribute['attr_values'][$component['id']]['input_value']));
                    $valuesElement->appendChild($valueElement);
                }
            }
        }
    }

    protected function addScenarioValues($dom, $attributeElement, $attribute, $module, $scenario)
    {
        $valuesElement = $dom->createElement('Values');
        $attributeElement->appendChild($valuesElement);

        if (isset($attribute['component_type']) && isset($attribute['component_type']['components'])) {
            foreach ($attribute['component_type']['components'] as $component) {
                if ($attribute['attr_values'][$component['id']]['is_collection'] == false && in_array($component['id'], $scenario->checked_component_ids)) {
                    $valueElement = $dom->createElement('Value');
                    $valueElement->setAttribute('componentGroup', $component['name']);
                    $valueElement->appendChild($dom->createTextNode($attribute['attr_values'][$component['id']]['input_value']));
                    $valuesElement->appendChild($valueElement);
                }
            }
        }
    }

    /**
     * Get collection values for an attribute and component
     * 
     * @param int $attributeId
     * @param int $componentId
     * @param int|null $scenarioId
     * @return string
     */
    protected function getCollectionValues($attributeId, $componentId, $scenarioId = null)
    {
        // This is a placeholder method - you would implement this based on your collection structure
        // It should return the collection values as a string, formatted for the XML

        // Example implementation:
        $collectionMap = USMCollectionMap::where('usm_id', $this->usm_id)
            ->where('scenario_mode', $scenarioId ? 'ScenarioAttributes' : 'NonScenarioAttributes')
            ->when($scenarioId, function ($query) use ($scenarioId) {
                return $query->where('usm_scenario_id', $scenarioId);
            })
            ->when(!$scenarioId, function ($query) {
                return $query->whereNull('usm_scenario_id');
            })
            ->with(['conditions.assignments' => function ($query) use ($componentId) {
                $query->whereJsonContains('component_ids', $componentId);
            }])
            ->first();

        if (!$collectionMap) {
            return '';
        }

        $values = [];

        foreach ($collectionMap->conditions as $condition) {
            foreach ($condition->assignments as $assignment) {
                if (!in_array($componentId, $assignment->component_ids)) {
                    continue;
                }

                // Get collection values for this assignment
                if ($assignment->valueSets) {
                    foreach ($assignment->valueSets as $valueSet) {
                        if ($valueSet->usm_module_component_attribute_id == $attributeId) {
                            $values[] = $valueSet->input_value;
                        }
                    }
                }
            }
        }

        return implode(', ', $values);
    }

    public function addCollection($dom, $moduleElement, $collections, $module)
    {
        $collectionsElement = $dom->createElement('Collections');
        $moduleElement->appendChild($collectionsElement);

        if ($collections == null) return;

        foreach ($collections as $collection) {
            $collectionElement = $dom->createElement('Collection');
            $collectionsElement->appendChild($collectionElement);

            $collectionElement->setAttribute('name', $collection['name']);
            $collectionElement->setAttribute('mode', $this->outputScenarioLabel($collection['scenario_mode']));
            $collectionElement->setAttribute('sourceType', $collection['source']);
            $collectionElement->setAttribute('levelCategory', $collection['level']);
            $collectionElement->setAttribute('componentGroupType', $collection['component_type']);


            $attributesElement = $dom->createElement('Attributes');
            $collectionElement->appendChild($attributesElement);

            foreach ($collection['attributes'] as $attribute) {
                $attributeElement = $dom->createElement('Attribute');
                $attributeElement->setAttribute('name', $attribute['name']);
                if (isset($attribute['parameter']) && !empty($attribute['parameter'])) {
                    $attributeElement->setAttribute('parameter', $attribute['parameter']);
                }
                $attributesElement->appendChild($attributeElement);

                $instanceElement = $dom->createElement('Instance');
                $instanceElement->setAttribute('index', 0);
                $attributeElement->appendChild($instanceElement);
                //Empty Token
                $tokensElement = $dom->createElement('Tokens');
                $instanceElement->appendChild($tokensElement);

                $valueSetsElement = $dom->createElement('ValueSets');
                $instanceElement->appendChild($valueSetsElement);

                foreach ($attribute['values'] as $value) {
                    $valueSetElement = $dom->createElement('ValueSet');
                    $valueSetsElement->appendChild($valueSetElement);
                    $valueSetElement->setAttribute('name', $value['valueSet']);
                    $valueSetElement->appendChild($dom->createTextNode($value['value']));
                }
            }
        }
    }

    /**
     * Add collection map to the XML
     * 
     * @param \DOMDocument $dom
     * @param \DOMElement $moduleElement
     * @param USMCollectionMap $collectionMap
     * @param USMModule $module
     */
    public function addCollectionMap($dom, $moduleElement, $collectionMap, $module)
    {
        $collectionMapsElement = $dom->createElement('CollectionMaps');
        $moduleElement->appendChild($collectionMapsElement);

        if ($collectionMap == null) return;

        $collectionMapElement = $dom->createElement('CollectionMap');
        $collectionMapElement->setAttribute('name', $collectionMap->name);
        $collectionMapElement->setAttribute('mode', $this->outputScenarioLabel($module->scenario_mode));
        $collectionMapsElement->appendChild($collectionMapElement);

        $scenariosElement = $dom->createElement('Scenarios');
        $collectionMapElement->appendChild($scenariosElement);
        if ($collectionMap->scenario_mode === XML_SCENARIO) {
            foreach ($this->available_scenarios as $scenario) {
                $scenarioElement = $dom->createElement('Scenario');
                $scenarioElement->setAttribute('name', $scenario->name);
                $scenariosElement->appendChild($scenarioElement);
            }
        }

        $conditionsElement = $dom->createElement('Conditions');
        $collectionMapElement->appendChild($conditionsElement);
        foreach ($collectionMap->conditions as $condition) {
            $conditionElement = $dom->createElement('Condition');
            $conditionElement->setAttribute('comment', $condition->comment);
            $conditionElement->setAttribute('expression', $condition->expression);
            $conditionsElement->appendChild($conditionElement);
            //Create empty conditions (unknown reason)
            $conditionElement->appendChild($dom->createElement('Conditions'));

            $assignmentsElement = $dom->createElement('Assignments');
            $conditionElement->appendChild($assignmentsElement);
            foreach ($condition->assignments as $assignment) {
                $assignmentElement = $dom->createElement('Assignment');
                $assignmentElement->setAttribute('collection', $assignment->collection->name);
                $assignmentsElement->appendChild($assignmentElement);

                //Add ValueSets
                $valueSetsElement = $dom->createElement('ValueSets');
                $assignmentElement->appendChild($valueSetsElement);
                foreach ($assignment->valueSets as $valueSet) {
                    if ($collectionMap->scenario_mode === XML_SCENARIO) {
                        if ($valueSet->collection_valuesets != null) {
                            foreach ($valueSet->collection_valuesets as $scenario_name => $valueSet_name) {
                                $valueSetElement = $dom->createElement('ValueSet');
                                $valueSetElement->setAttribute('scenario', $scenario_name);
                                $valueSetElement->appendChild($dom->createTextNode($valueSet_name));
                                $valueSetsElement->appendChild($valueSetElement);
                            }
                        }
                    } else {
                        $valueSetElement = $dom->createElement('ValueSet');
                        $valueSetElement->appendChild($dom->createTextNode($valueSet->valueSet->name));
                        $valueSetsElement->appendChild($valueSetElement);
                    }
                }

                //Add ComponentGroups
                $componentGroupsElement = $dom->createElement('ComponentGroups');
                $assignmentElement->appendChild($componentGroupsElement);
                foreach ($assignment->components as $component) {
                    $componentGroupElement = $dom->createElement('ComponentGroup');
                    $componentGroupElement->appendChild($dom->createTextNode($component->name));
                    $componentGroupsElement->appendChild($componentGroupElement);
                }
            }
        }
    }

    /**
     * Save the USM XML to a file
     * 
     * @param string $path Optional path to save the file. If null, will use storage_path.
     * @return string The full path to the saved file
     */
    public function saveXMLToFile($path = null)
    {
        $xml = $this->generateUSMExportXML();

        if ($path === null) {
            $fileName = 'usm_export_' . $this->usm_id . '_' . date('Ymd_His') . '.xml';
            $path = storage_path('app/exports/usm/' . $this->usm->id . '/' . $fileName);

            // Create directory if it doesn't exist
            if (!file_exists(dirname($path))) {
                mkdir(dirname($path), 0755, true);
            }
        }

        file_put_contents($path, $xml);

        return $path;
    }

    private function outputScenarioLabel($mode)
    {
        return $mode === XML_NONSCENARIO ? 'non-scenario' : 'scenario';
    }
}
