<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class USMCollectionAssignment extends Model
{
    use SoftDeletes;

    protected $table = 'usm_collection_assignments';

    protected $fillable = [
        'usm_collection_condition_id',
        'collection_id',
        'component_ids',
        'sequence'
    ];

    protected $casts = [
        'component_ids' => 'array'
    ];

    /**
     * Get the condition that owns the assignment.
     */
    public function condition()
    {
        return $this->belongsTo(USMCollectionCondition::class, 'usm_collection_condition_id');
    }

    public function valueSets()
    {
        return $this->hasMany(USMCollectionAssignmentValueSet::class, 'usm_collection_assignment_id');
    }

    public function collection()
    {
        return $this->belongsTo(USMCollection::class, 'collection_id');
    }
}
