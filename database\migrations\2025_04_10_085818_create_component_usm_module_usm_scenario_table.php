<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Many to Many
     */
    public function up(): void
    {
        Schema::create('component_usm_module_usm_scenario', function (Blueprint $table) {
            $table->unsignedBigInteger('component_id');
            $table->unsignedBigInteger('usm_module_id');
            $table->unsignedBigInteger('usm_scenario_id');
            $table->primary(['component_id', 'usm_scenario_id', 'usm_module_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('component_usm_module_usm_scenario');
    }
};
