<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class FuseConfigItem extends BaseModel
{
    use SoftDeletes;
    
    protected $fillable = [
        'register',
        'size',
        'datatype',
        'pin_to_read',
        'pin_to_modify',
        'direction',
        'sequence',
        'fuse_config_id',
    ];

    public function fuseConfigItemAddresses(): HasMany
    {
        return $this->hasMany(FuseConfigItemAddress::class)->orderBy('start_address');
    }
}
