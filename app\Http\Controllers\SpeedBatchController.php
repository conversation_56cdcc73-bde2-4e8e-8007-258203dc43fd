<?php

namespace App\Http\Controllers;

use App\Http\Requests\SpeedBatchCreateRequest;
use App\Http\Requests\SpeedBatchUpdateRequest;
use App\Imports\SpeedImport;
use App\Jobs\GenerateBatchTableJob;
use App\Models\GenerateBatchLog;
use App\Models\SpeedBatch;
use App\Models\SpeedPreview;
use App\Services\LineDatabase;
use App\Traits\WithProductData;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use Maatwebsite\Excel\Facades\Excel;

class SpeedBatchController extends Controller
{
    use WithProductData;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Build Filter
        $filters = $this->filterSessions($request, 'speed', [
            'keyword' => '',
            'product_type_id' => null,
            'product_id' => null,
            'product_group_id' => null,
        ]);

        $list = SpeedBatch::with('createdUser', 'product', 'productType', 'productGroup')
            ->when(!empty($filters['keyword']), function ($q) use ($filters) {
                $q->where(function ($query) use ($filters) {
                    $query->where('name', 'ilike', '%' . $filters['keyword'] . '%')
                        ->orWhereHas('createdUser', function ($query) use ($filters) {
                            $query->where('name', 'ilike', '%' . $filters['keyword'] . '%');
                        });
                });
            })->byProducts($filters)
            ->filterSort($filters)
            ->paginate(config('table.per_page'));

        return Inertia::render('SpeedBatch/Index', $this->withProductData([
            'header' => SpeedBatch::header(),
            'filters' => $filters,
            'list' => $list,
        ]));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $newColumns = $request->session()->get('newColumns'); //This data is stored in session from store()
        $compareResult = $request->session()->get('compareResult'); //This data is stored in session from preview()

        $data = $speedbatch ?? new SpeedBatch;
        return Inertia::render('SpeedBatch/Create', $this->withProductData([
            'data' => $data,
            'newColumns' => $newColumns ?? [],
            'compareResult' => $compareResult ?? null,
        ]));
    }

    /**
     * New import check does it have new column and shows in the UI
     * to let user verify
     */
    public function store(SpeedBatchCreateRequest $request)
    {
        //Store file in session and check the columns
        $data = $request->validated();
        try {
            //Store the file so can be use in few stages, check new column, preview and import
            $file = $request->file('file')->store('speedbatch', 'public');
            $data['file_path'] = $file;
            //Keep file in session
            $request->session()->put('speedbatch_file', $file);
            //Retrieve from session
            $file = $request->session()->get('speedbatch_file');
            //Get from public disk
            $filePath = Storage::disk('public')->path($file);
            //Keep Batch Data in session
            unset($data['file']);
            $request->session()->put('speedbatch_data', $data);

            //Check the column first and shows to the users
            $import = new SpeedImport(SPEED_PREVIEW_TABLE, false);
            Excel::import($import, $filePath);

            $totalRow = 0;
            $batch = SpeedBatch::byProducts($data)->orderBy('id', 'desc')->select(['id'])->first();

            if ($totalRow == 0) {
                //First time, no need do any check, direct run import
                return $this->createBatch($file, $data);
            } else if (count($import->getNewColumns()) > 0) {
                //Show the new columns in html, return to the same create page
                $newColumns = $import->getNewColumns();
                return Redirect::route('speedbatch.create')->with('newColumns', $newColumns);
            } else {
                //No new column, go to comparison
                return $this->different($request);
            }
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            return Redirect::back()->withErrors(excelFailureMessage($e));
        } catch (\Exception $e) {
            return Redirect::route('speedbatch.index')->withErrors(['errors' => $e->getMessage()]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(SpeedBatch $speedbatch)
    {
        // Load relationships if not already loaded
        $speedbatch->load(['product', 'productType', 'productGroup', 'createdUser', 'stateUpdateUser']);
        $line = new LineDatabase($speedbatch->id);

        return Inertia::render('SpeedBatch/Show', [
            'data' => $speedbatch,
            'isSpeedOnly' => $line->isSpeedTable()
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(?SpeedBatch $speedbatch = null, bool $createRevision = false)
    {
        // Load relationships if not already loaded
        $speedbatch->load(['createdUser', 'stateUpdateUser'])->append('is_generating');
        $line = new LineDatabase($speedbatch->id);

        return Inertia::render('SpeedBatch/Edit', $this->withProductData([
            'data' => $speedbatch,
            'isSpeedOnly' => $line->isSpeedTable(),
            'createRevision' => $createRevision,
            'states' => SpeedBatch::states(),
            'logs' => $speedbatch ? $this->getGenerateBatchLogs($speedbatch->id) : [],
        ]));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(SpeedBatchUpdateRequest $request, ?SpeedBatch $speedbatch = null)
    {
        $data = $request->validated();
        $speedbatch->update($data);

        return Redirect::route('speedbatch.edit', $speedbatch->id)->with('message', 'Speed batch updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SpeedBatch $speedbatch)
    {
        if ($speedbatch->state !== STATE_SNAPSHOT) {
            return Redirect::route('speedbatch.index')->with('message', 'Speed batch deleted successfully.');
        }

        return Redirect::route('speedbatch.index')->with('error', STATE_SNAPSHOT . 'state unable to delete.');
    }

    /**
     * Check is the new import row data different that previous batch 
     * Store the data in "speed_preview" to do comparion with "speed"
     */
    public function different(Request $request)
    {
        try {
            $speedBatchData = $request->session()->get('speedbatch_data');
            $file = $request->session()->get('speedbatch_file');
            //Get from public disk
            $filePath = Storage::disk('public')->path($file);

            //Start the import
            $import = new SpeedImport(SPEED_PREVIEW_TABLE, true);
            Excel::import($import, $filePath);

            //To store in session
            $newColumns = $import->getNewColumns();
            $compareResult = $this->compareAllRowsSequentially($speedBatchData);

            if (isset($compareResult['differences']) && empty($compareResult['differences'])) {
                return Redirect::route('speedbatch.index')->with('message', 'Skip import, no difference found.');
            }

            return Redirect::route('speedbatch.create')
                ->with('newColumns', $newColumns)
                ->with('compareResult', $compareResult);
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            return Redirect::back()->withErrors(excelFailureMessage($e));
        } catch (\Exception $e) {
            return Redirect::route('speedbatch.create')->withErrors(['errors' => $e->getMessage()]);
        }
    }

    /*
    * After compare all rows, start import
    */
    public function import(Request $request)
    {
        $file = $request->session()->get('speedbatch_file');
        $data = $request->session()->get('speedbatch_data');

        return $this->createBatch($file, $data);
    }

    private function createBatch($file, $batchData)
    {
        try {
            //Get from public disk
            $filePath = Storage::disk('public')->path($file);
            $batchData['created_user_id'] = auth()->id();

            $liraBatch = SpeedBatch::create($batchData);

            $generate = GenerateBatchLog::create([
                'speed_batch_id' => $liraBatch->id,
                'started_at' => now(),
                'user_id' => auth()->id(),
                'messages' => formatLog('Preparing to generate batch table.')
            ]);

            GenerateBatchTableJob::dispatch($generate->id);

            return Redirect::route('speedbatch.edit', [
                'speedbatch' => $liraBatch->id,
                'goto' => 'tab_6'
            ])->with('message', 'Excel imported start generating table.');
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            return Redirect::back()->withErrors(excelFailureMessage($e));
        } catch (\Exception $e) {
            return Redirect::route('speedbatch.create')->withErrors(['errors' => $e->getMessage()]);
        }
    }

    private function compareAllRowsSequentially($speedBatchData): array
    {
        $exclude_columns = ['id', 'batch_id', 'created_at', 'line_item_id', 'updated_at', 'deleted_at'];
        $differences = [];
        $columnsWithDifferences = []; // Track columns with differences

        // Fetch all rows from both models, use latest batch by products
        $batch = SpeedBatch::byProducts($speedBatchData)->orderBy('id', 'desc')->select(['id'])->first();
        $rows1 = Speed::where('batch_id', $batch->id)->get();
        $rows2 = SpeedPreview::get();

        // Determine the number of rows to compare
        $rowCount = max($rows1->count(), $rows2->count());

        // Get all columns from both tables (excluding excluded columns)
        $columns1 = array_diff(array_keys($rows1->first()->getAttributes()), $exclude_columns);
        $columns2 = array_diff(array_keys($rows2->first()->getAttributes()), $exclude_columns);
        $allColumns = array_unique(array_merge($columns1, $columns2)); // Union of all columns

        // Iterate through the rows sequentially
        for ($i = 0; $i < $rowCount; $i++) {
            $row1 = $rows1[$i] ?? null;
            $row2 = $rows2[$i] ?? null;

            if ($row1 && $row2) {
                // Compare attributes of the two rows
                $rowDifferences = [];
                foreach ($allColumns as $column) {
                    $value1 = $row1->$column ?? null;
                    $value2 = $row2->$column ?? null;

                    if ($value1 !== $value2) {
                        $rowDifferences[$column] = [
                            'row1' => $value1,
                            'row2' => $value2,
                        ];
                        // Track columns with differences
                        if (!in_array($column, $columnsWithDifferences)) {
                            $columnsWithDifferences[] = $column;
                        }
                    }
                }

                // If differences exist, add them to the result
                if (!empty($rowDifferences)) {
                    $differences[$i] = $rowDifferences;
                }
            } elseif ($row1) {
                // Row exists in $rows1 but not in $rows2
                $differences[$i] = [
                    'status' => 'Row missing in New Table',
                    'row1' => $row1->getAttributes(),
                    'row2' => null,
                ];
            } elseif ($row2) {
                // Row exists in $rows2 but not in $rows1
                $differences[$i] = [
                    'status' => 'Row missing in Existing Table',
                    'row1' => null,
                    'row2' => $row2->getAttributes(),
                ];
            }
        }

        return [
            'differences' => $differences,
            'columnsWithDifferences' => $columnsWithDifferences,
        ];
    }

    public function postState(Request $request, SpeedBatch $speedBatch)
    {
        $speedBatch->setState($request->state);

        $data = SpeedBatch::with('stateUpdateUser')->find($speedBatch->id);

        return response()->json(['message' => 'State updated successfully', 'data' => $data]);
    }

    public function getFormat($speedBatchId)
    {
        $batch = SpeedBatch::find($speedBatchId);
        if (!$batch) {
            return response()->json(['error' => 'Batch not found'], 404);
        }

        // Get columns and their data types
        $model = $batch->getBasedModal();
        $columns = $model->getColumnsWithDataTypes();
        $excludedColumns = ['id', 'created_at', 'updated_at', 'deleted_at'];
        $columns = array_diff_key($columns, array_flip($excludedColumns));

        // Convert types to readable format using convertDataType helper
        foreach ($columns as $col => &$type) {
            $type = convertDataType($type);
        }
        unset($type);

        return response()->json($columns);
    }


    public function getLogs(SpeedBatch $speedBatch)
    {
        $logs = $this->getGenerateBatchLogs($speedBatch->id);
        return response()->json($logs);
    }


    public function getGenerateBatchLogs($speedBatchId)
    {
        $logs = GenerateBatchLog::with(['user'])->where('speed_batch_id', $speedBatchId)->orderBy('created_at', 'desc')->limit(6)->get();
        return $logs;
    }
}
