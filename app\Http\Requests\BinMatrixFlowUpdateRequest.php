<?php

namespace App\Http\Requests;

use App\Models\BinMatrix;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BinMatrixFlowUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [];
        return  array_merge($rules, [
            'rows.*.olb_bin' => ['required'],
            'rows.*.pass_bin' => ['required'],
            'rows.*.cross_ship' => ['required']
        ]);
    }
}
