<?php

namespace Database\Seeders;

use App\Models\Component;
use Illuminate\Database\Seeder;

class ComponentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Level 1 components (Categories)
        $electronics = Component::create([
            'name' => 'Electronics',
            'type' => 'Category',
            'notes' => 'Electronic components and systems',
            'component_level_id' => 1,
            'level' => 1,
            'parent_id' => null,
            'usm_id'=>1
        ]);

        $mechanics = Component::create([
            'name' => 'Mechanics',
            'type' => 'Category',
            'notes' => 'Mechanical components and systems',
            'component_level_id' => 1,
            'level' => 1,
            'parent_id' => null,
            'usm_id'=>1
        ]);

        // Level 2 components (Subcategories)
        $digitalElectronics = Component::create([
            'name' => 'Digital Electronics',
            'type' => 'Subcategory',
            'notes' => 'Digital electronic components',
            'component_level_id' => 2,
            'level' => 2,
            'parent_id' => $electronics->id,
            'usm_id'=>1
        ]);

        $analogElectronics = Component::create([
            'name' => 'Analog Electronics',
            'type' => 'Subcategory',
            'notes' => 'Analog electronic components',
            'component_level_id' => 2,
            'level' => 2,
            'parent_id' => $electronics->id,
            'usm_id'=>1
        ]);

        $drives = Component::create([
            'name' => 'Drives',
            'type' => 'Subcategory',
            'notes' => 'Mechanical drive systems',
            'component_level_id' => 2,
            'level' => 2,
            'parent_id' => $mechanics->id,
            'usm_id'=>1
        ]);

        // Level 3 components (Components)
        $microcontrollers = Component::create([
            'name' => 'Microcontrollers',
            'type' => 'Component',
            'notes' => 'Programmable microcontroller units',
            'component_level_id' => 3,
            'level' => 3,
            'parent_id' => $digitalElectronics->id,
            'usm_id'=>1
        ]);

        $fpga = Component::create([
            'name' => 'FPGA',
            'type' => 'Component',
            'notes' => 'Field-programmable gate arrays',
            'component_level_id' => 3,
            'level' => 3,
            'parent_id' => $digitalElectronics->id,
            'usm_id'=>1
        ]);

        $opamps = Component::create([
            'name' => 'Op-Amps',
            'type' => 'Component',
            'notes' => 'Operational amplifiers',
            'component_level_id' => 3,
            'level' => 3,
            'parent_id' => $analogElectronics->id,
            'usm_id'=>1
        ]);

        // Level 4 components (Subcomponents)
        Component::create([
            'name' => 'AVR',
            'type' => 'Subcomponent',
            'notes' => 'AVR microcontroller family',
            'component_level_id' => 4,
            'level' => 4,
            'parent_id' => $microcontrollers->id,
            'usm_id'=>1
        ]);

        Component::create([
            'name' => 'ARM',
            'type' => 'Subcomponent',
            'notes' => 'ARM microcontroller family',
            'component_level_id' => 4,
            'level' => 4,
            'parent_id' => $microcontrollers->id,
            'usm_id'=>1
        ]);

        Component::create([
            'name' => 'Intel',
            'type' => 'Subcomponent',
            'notes' => 'Intel FPGA family',
            'component_level_id' => 4,
            'level' => 4,
            'parent_id' => $fpga->id,
            'usm_id'=>1
        ]);

        Component::create([
            'name' => 'Xilinx',
            'type' => 'Subcomponent',
            'notes' => 'Xilinx FPGA family',
            'component_level_id' => 4,
            'level' => 4,
            'parent_id' => $fpga->id,
            'usm_id'=>1
        ]);
    }
}
