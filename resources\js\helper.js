//Add all your global function over here
import { usePage } from '@inertiajs/vue3';

export function formatDate(value) {
    if (value) {
        const date = new Date(value);
        return date.toLocaleString();
    }
}

//Display message in AuthenticatedLayout FlashAlert component
export function setFlashMessage(message) {
    usePage().props.flash.message = message;
}

//Display error in AuthenticatedLayout FlashAlert component
export function setFlashError(message) {
    usePage().props.flash.error = message;
}

//Auto select the tab base on the #href
export function selectTab() {
    if (window.location.hash) {
        // Get the hash (tab ID) from the URL
        const hash = window.location.hash;

        // Find all tab navigation links
        const tabLinks = document.querySelectorAll('.nav-link');

        // Find all tab panes
        const tabPanes = document.querySelectorAll('.tab-pane');

        // Remove active and show classes from all tabs
        tabLinks.forEach(link => {
            link.classList.remove('active');
        });

        tabPanes.forEach(pane => {
            pane.classList.remove('active', 'show');
        });

        // Set the active tab based on the hash
        const activeTabLink = document.querySelector(`.nav-link[href="${hash}"]`);
        const activeTabPane = document.querySelector(hash);

        if (activeTabLink && activeTabPane) {
            activeTabLink.classList.add('active');
            activeTabPane.classList.add('active', 'show');
        }
    }
}