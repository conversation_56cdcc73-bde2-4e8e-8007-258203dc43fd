<?php

namespace Database\Seeders;

use App\Models\Product;
use App\Models\ProductGroup;
use App\Models\ProductType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class IntelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        ProductType::truncate();
        Product::truncate();
        ProductGroup::truncate();

        //Create admin
        $pt = ProductType::create([
            'name' => 'FPGA',
            'active' => true,
        ]);

        $product = Product::create([
            'name' => 'FALCON MESA',
            'active' => true,
            'product_type_id' => $pt->id
        ]);

        ProductGroup::create([
            'name' => 'FALCON MESA 1',
            'active' => true,
            'product_id' => $product->id
        ]);

        ProductGroup::create([
            'name' => 'FALCON MESA 2',
            'active' => true,
            'product_id' => $product->id
        ]);
    }
}
