<script setup>
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TreeNode from '@/Components/TreeNode.vue';
import { useForm, Link } from '@inertiajs/vue3';
import { ref, onMounted, computed } from 'vue';
import Select from '@/Components/Select.vue';

const props = defineProps({
    detailForm: {
        type: Object,
        default: () => ({}),
    },
    usm_id: {
        type: Number,
        default: '',
    },
});

const routeGroupName = 'components';
const allComponentTypes = ref([]);
const componentTypes = ref([]);
const levels = ref([]);
const tree = ref([]);
const selectedComponent = ref(null);
const expandedNodes = ref(new Set());
const processing = ref(false);
const disabledAddRootButton = ref(false);

onMounted(() => {
    fetchInitialData();
});

const fetchInitialData = async () => {
    axios
        .get(route(routeGroupName + '.get', props.usm_id))
        .then(response => {
            allComponentTypes.value = response.data.component_types;
            componentTypes.value = response.data.component_types;
            levels.value = response.data.levels;
            tree.value = response.data.tree;
        })
        .catch(error => {
            console.error('Error fetching data:', error);
        });
};

const form = useForm({
    name: '',
    component_type_id: null,
    notes: '',
    component_level_id: '',
    level: '',
    parent_id: null,
    usm_id: props.usm_id,
});

const maxLevel = computed(() => {
    return levels.value ? levels.value.length : 0;
});

const disableSubmitButton = computed(() => {
    return !selectedComponent.value || !processing.value;
});

const selectComponent = component => {
    selectedComponent.value = component;
    form.name = component.name || '';
    form.component_type_id = component.component_type_id || null;
    form.notes = component.notes || '';
    form.component_level_id = component.component_level_id;
    form.level = component.level;
    form.parent_id = component.parent_id;

    // Filter component types based on component level
    if (component && component.component_level_id) {
        const componentLevel = levels.value.find(level => level.id === component.component_level_id);
        if (componentLevel) {
            componentTypes.value = allComponentTypes.value.filter(type => type.level === componentLevel.name);

            // Reset component_type_id if it's not in the filtered list
            if (form.component_type_id && !componentTypes.value.some(type => type.id === form.component_type_id)) {
                form.component_type_id = null;
            }
        }
    } else {
        // If no level is associated, show all component types
        componentTypes.value = [...allComponentTypes.value];
    }
};

const toggleExpand = nodeId => {
    if (expandedNodes.value.has(nodeId)) {
        expandedNodes.value.delete(nodeId);
    } else {
        expandedNodes.value.add(nodeId);
    }
};

const hasChildren = node => {
    return node.children && node.children.length > 0;
};

const addSameLevelChild = parentNode => {
    // Find the component level object that matches the parent's component_level_id
    const componentLevel = levels.value.find(level => level.id === parentNode.component_level_id);

    // When adding a sibling, we need the same parent_id as the selected node
    // If the node is a root node, parent_id should be null
    const newComponent = {
        name: componentLevel ? componentLevel.name : 'New Component', // Use the component level name as default
        component_type_id: null,
        notes: '',
        component_level_id: parentNode.component_level_id,
        level: parentNode.level,
        parent_id: parentNode.parent_id,
        usm_id: props.usm_id,
    };

    saveNewComponent(newComponent);
};

const addNextLevelChild = parentNode => {
    // Check if we're at the max level
    if (parentNode.level >= maxLevel.value) {
        alert(`Cannot add more levels. Maximum level (${maxLevel.value}) reached.`);
        return;
    }

    const nextLevel = getNextLevelId(parentNode.level);
    if (!nextLevel) {
        alert('Cannot find next level configuration. Please set up component levels first.');
        return;
    }

    // Find the component level object that matches the next level ID
    const componentLevel = levels.value.find(level => level.id === nextLevel);

    const newComponent = {
        name: componentLevel ? componentLevel.name : 'New Child Component', // Use the component level name as default
        component_type_id: null,
        notes: '',
        component_level_id: nextLevel,
        level: parentNode.level + 1,
        parent_id: parentNode.id,
        usm_id: props.usm_id,
    };

    saveNewComponent(newComponent);
};

const getNextLevelId = currentLevel => {
    // Find the component level ID for the next level
    const nextLevel = levels.value.find(level => level.level === currentLevel + 1);
    return nextLevel ? nextLevel.id : null;
};

const saveNewComponent = componentData => {
    processing.value = true;
    axios
        .post(route('components.store'), componentData)
        .then(response => {
            // Refresh the tree after adding a new component
            fetchInitialData();
            // Expand the parent node if adding a child
            if (componentData.parent_id) {
                expandedNodes.value.add(componentData.parent_id);
            }
        })
        .catch(error => {
            console.error('Error creating component:', error);
            if (error.response && error.response.data) {
                console.error('Error details:', error.response.data);
                alert('Error creating component: ' + (error.response.data.message || 'Unknown error'));
            } else {
                alert('Error creating component. Check console for details.');
            }
        })
        .finally(() => {
            processing.value = false;
        });
};

const addRootComponent = () => {
    disabledAddRootButton.value = true;
    if (!levels.value || levels.value.length === 0) {
        alert('No component levels defined. Please set up component levels first.');
        return;
    }

    // Find the first level (level 1)
    const firstLevel = levels.value.find(level => level.level === 1);
    if (!firstLevel) {
        alert('Cannot find level 1 configuration. Please set up component levels first.');
        return;
    }

    const newComponent = {
        name: firstLevel.name || 'New Root Component', // Use the component level name as default
        component_type_id: null,
        notes: '',
        component_level_id: firstLevel.id,
        level: 1,
        parent_id: null,
        usm_id: props.usm_id,
    };

    saveNewComponent(newComponent);
};

const saveComponentDetails = () => {
    if (!selectedComponent.value) return;
    processing.value = true;
    axios
        .put(route('components.update', selectedComponent.value.id), form)
        .then(response => {
            // Refresh the tree after updating
            fetchInitialData();
            form.recentlySuccessful = true;
            // Reset success state after delay (similar to Inertia's default behavior)
            setTimeout(() => {
                form.recentlySuccessful = false;
            }, 2000);
        })
        .catch(error => {
            console.error('Error updating component:', error);
            disabledAddRootButton.value = false;
        })
        .finally(() => {
            processing.value = false;
        });
};

const deleteComponent = node => {
    if (!node || !node.id) return;

    if (!confirm(`Are you sure you want to delete "${node.name}"? This will also delete all child components.`)) {
        return;
    }
    processing.value = true;
    axios
        .delete(route('components.destroy', node.id))
        .then(response => {
            // If the deleted component was selected, clear the selection
            if (selectedComponent.value && selectedComponent.value.id === node.id) {
                selectedComponent.value = null;
                form.reset();
            }
            // Refresh the tree after deleting
            fetchInitialData();
        })
        .catch(error => {
            console.error('Error deleting component:', error);
            alert('Error deleting component: ' + (error.response?.data?.message || 'Unknown error'));
        }).finally(() => {
            processing.value = false;
        });
};
</script>

<template>
    <form @submit.prevent="saveComponentDetails">
        <div class="row">
            <div class="col-3">
                <nav id="bullet" class="h-100 flex-column align-items-stretch pe-4 border-end">
                    <div class="component-tree">
                        <div v-if="tree && tree.length > 0">
                            <ul class="tree-root">
                                <tree-node v-for="node in tree" :key="node.id" :node="node" :max-level="maxLevel"
                                    :selected-node-id="selectedComponent ? selectedComponent.id : null"
                                    :expanded-nodes="expandedNodes" :enableButtons="true" :is-processing="processing"
                                    @toggle-expand="toggleExpand" @select-node="selectComponent"
                                    @add-same-level="addSameLevelChild" @add-next-level="addNextLevelChild"
                                    @delete-node="deleteComponent" />
                            </ul>
                        </div>
                        <div v-else class="alert alert-info">No components found. Add a component to get started.</div>
                    </div>
                </nav>
            </div>

            <div class="col-9">
                <h1 class="h3 mb-3 fw-normal">Component Details:</h1>
                <div class="row g-3">
                    <div class="col-md-6">
                        <InputLabel for="name" value="Name" />
                        <TextInput id="name" type="text" class="form-control" v-model="form.name"
                            :disabled="!selectedComponent" />
                        <InputError :message="form.errors.name" />
                    </div>

                    <div class="col-md-6">
                        <InputLabel for="type" value="Type" />
                        <Select id="type" v-model="form.component_type_id" :options="componentTypes"
                            :placeholder="'Select Type'" :disabled="!selectedComponent" />
                        <InputError :message="form.errors.component_type_id" />
                    </div>

                    <div class="col-md-12">
                        <InputLabel for="notes" value="Notes" />
                        <textarea id="notes" v-model="form.notes" class="form-control" />
                        <InputError :message="form.errors.notes" />
                    </div>

                    <div class="col-12">
                        <PrimaryButton type="submit" :disabled="!disableSubmitButton">Save</PrimaryButton>
                        <Transition enter-active-class="fade transition ease-in-out duration-500"
                            enter-from-class="opacity-0" enter-to-class="opacity-100"
                            leave-active-class="fade transition ease-in-out duration-500" leave-to-class="opacity-0">
                            <span v-if="form.recentlySuccessful" class="ms-2 text-success">Saved.</span>
                        </Transition>
                        <button v-if="!tree || tree.length === 0" type="button" class="btn btn-outline-primary ms-2"
                            @click="addRootComponent()" :disabled="disabledAddRootButton">Add Root Component</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</template>

<style scoped>
.component-tree {
    padding: 10px 0;
    position: relative;
}

.tree-root,
.nested-tree {
    list-style-type: none;
    padding-left: 0;
}

.nested-tree {
    padding-left: 20px;
    position: relative;
}

/* Add vertical lines to nested items */
.nested-tree::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 8px;
    width: 1px;
    background-color: #dee2e6;
}

.tree-node {
    margin: 5px 0;
    position: relative;
}

/* Add horizontal lines to connect to vertical lines */
.nested-tree .tree-node::before {
    content: '';
    position: absolute;
    top: 12px;
    left: -12px;
    width: 12px;
    height: 1px;
    background-color: #dee2e6;
}

.expand-btn {
    background: none;
    border: none;
    padding: 0 5px;
    cursor: pointer;
}

.expand-placeholder {
    width: 24px;
    display: inline-block;
}

.node-label {
    padding: 3px 8px;
    cursor: pointer;
    border-radius: 3px;
    margin-right: 10px;
}

.node-label:hover {
    background-color: #f0f0f0;
}

.node-label.selected {
    background-color: #e0e0ff;
    font-weight: bold;
}

.node-actions {
    display: none;
    margin-left: auto;
}

.tree-node:hover .node-actions {
    display: flex;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}
</style>
