<script setup>
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, useForm, Link } from '@inertiajs/vue3';
import { onMounted, ref, computed, watch, onBeforeUnmount } from 'vue';
import ProductSelectors from '@/Components/ProductSelectors.vue';
import DynamicTable from '@/Components/DynamicTable.vue';
import LineitemIndex from './LineitemIndex.vue';
import { selectTab, formatDate } from '@/helper';
import { States } from '@/enums';
import Format from '@/Pages/SpeedBatch/Format.vue';
import LogCard from '@/Components/LogCard.vue';

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    productTypes: {
        type: Object,
    },
    products: {
        type: Object,
    },
    productGroups: {
        type: Object,
    },
    isSpeedOnly: {
        type: Boolean,
        default: true
    },
    createRevision: {
        type: Boolean,
    },
    states: {
        type: Array,
    },
    logs: {
        type: Array,
        default: () => [],
    },
});

const routeGroupName = 'speedbatch';
const headerTitle = ref('Speed Module');
const localData = ref({ ...props.data });

const storeForm = useForm({
    name: props.data.name,
    product_type_id: props.data.product_type_id,
    product_id: props.data.product_id,
    product_group_id: props.data.product_group_id,
});

onMounted(() => {
    // Get query string parameter 'goto'
    const urlParams = new URLSearchParams(window.location.search);
    const gotoParam = urlParams.get('goto');
    if (gotoParam != null) {
        window.location.hash = gotoParam;
    }

    selectTab();

    // Check if generation is in progress when component mounts
    if (localData.value.is_generating) {
        // Start refreshing logs immediately
        refreshLogs();
        startLogRefreshing();
    }
});

// Clean up interval when component p unmounted
onBeforeUnmount(() => {
    if (logRefreshInterval) {
        clearInterval(logRefreshInterval);
        logRefreshInterval = null;
    }
});

watch(
    () => props.data,
    newVal => {
        localData.value = { ...newVal };
    },
);


const isCreateRevision = computed(() => {
    return props.createRevision ? true : false;
});

const editDisabled = computed(() => {
    return localData.value.state == States.SNAPSHOT;
});

const isGenerating = computed(() => {
    return localData.value.is_generating;
});

const setState = state => {
    const c = confirm('Change the state to ' + state);
    if (c) {
        axios
            .post(route(routeGroupName + '.state.update', props.data.id), { state: state })
            .then(response => {
                localData.value = response.data.data;
                alert(response.data.message);
            })
            .catch(error => {
                alert(error.response.data.message);
            });
    }
};

//Refresh log
// Variable to store the interval ID for log refreshing
let logRefreshInterval = null;
const startLogRefreshing = () => {
    // Clear any existing interval first
    if (logRefreshInterval) {
        clearInterval(logRefreshInterval);
    }

    // Set up a new interval to refresh logs every 3 seconds
    logRefreshInterval = setInterval(() => {
        refreshLogs();

        // Check if generation is complete and stop refreshing if it is
        if (!localData.value.is_generating) {
            clearInterval(logRefreshInterval);
            logRefreshInterval = null;
        }
    }, 3000);
};

const refreshLogs = () => {
    if (!localData.value.id) return;

    axios.get(route(routeGroupName + '.logs', localData.value.id))
        .then(response => {
            // Update logs in props (this is a workaround since props are read-only)
            if (props.logs && Array.isArray(props.logs)) {
                props.logs.splice(0, props.logs.length, ...response.data);

                // Check if the most recent log has completed_at, which means generation is done
                if (response.data.length > 0) {
                    const latestLog = response.data[0]; // First log is the most recent
                    if (latestLog.completed_at) {
                        localData.value.is_generating = false;
                    }
                }
            }
        })
        .catch(error => {
            console.error('Error refreshing logs:', error);
        });
};
</script>

<template>

    <Head :title="headerTitle" />

    <AuthenticatedLayout>
        <template #header> {{ headerTitle }} </template>
        <span v-if="props.createRevision">(New Revision {{ props.data.revision }})</span>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <ul class="nav nav-tabs card-header-tabs">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="tab" href="#tab_1">Detail</a>
                    </li>
                    <li v-if="!isGenerating" class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#tab_2">Table</a>
                    </li>
                    <li v-if="!isGenerating" class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#tab_3">Lineitem</a>
                    </li>
                    <li v-if="!isGenerating" class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#tab_4">Lineitem Table</a>
                    </li>
                    <li v-if="!isGenerating" class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#tab_5">Format</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#tab_6">Logs</a>
                    </li>
                </ul>
                <div v-if="data.id != null && isCreateRevision == false" class="d-flex gap-2">
                    <div class="dropdown">
                        <button class="btn btn-sm btn-info dropdown-toggle" type="button" data-bs-toggle="dropdown"
                            aria-expanded="false"><i class="bi bi-check-circle"></i> {{ localData.state }}</button>
                        <ul class="dropdown-menu dropdown-menu">
                            <li v-for="state in states" :key="state">
                                <button type="button" class="dropdown-item" @click="setState(state)"
                                    :disabled="state == localData.state">{{ state }}
                                </button>
                            </li>
                            <template v-if="localData.state_updated_at">
                                <li>
                                    <hr class="dropdown-divider" />
                                </li>
                                <li>
                                    <button type="button" class="dropdown-item" disabled>
                                        {{ localData.state_update_user.name }} <br />
                                        {{ formatDate(localData.state_updated_at) }}
                                    </button>
                                </li>
                            </template>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="tab-content">
                    <div class="tab-pane fade pt-10 active show" id="tab_1" role="tabpanel" aria-labelledby="tab_1">
                        <form
                            @submit.prevent="storeForm.patch(route(routeGroupName + '.update', props.data.id), { preserveScroll: true, preserveState: true })">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <InputLabel for="name" value="Name" />
                                    <TextInput id="name" type="text" v-model="storeForm.name"
                                        :invalid="storeForm.errors.name" required />
                                    <InputError :message="storeForm.errors.name" />
                                </div>

                                <ProductSelectors v-model="storeForm" :product-types="productTypes" :products="products"
                                    :product-groups="productGroups" :errors="storeForm.errors" />
                            </div>

                            <div class="row g-3 my-2">
                                <div class="col-12">
                                    <div class="text-end">
                                        Revision: {{ data.revision }} <br />
                                        <template v-if="data.created_user">Created By: {{ data.created_user.name }}
                                        </template>
                                    </div>
                                </div>
                            </div>
                            <PrimaryButton v-if="editDisabled == false" type="submit" :disabled="storeForm.processing">
                                Save
                            </PrimaryButton>
                        </form>
                    </div>
                    <div v-if="!isGenerating" class="tab-pane fade pt-10" id="tab_2" role="tabpanel"
                        aria-labelledby="tab_2">
                        <DynamicTable :batchId="data.id" :tableType="'speed'" />
                    </div>
                    <div v-if="!isGenerating" class="tab-pane fade pt-10" id="tab_3" role="tabpanel"
                        aria-labelledby="tab_3">
                        <LineitemIndex :speed_batch_id="data.id" :editDisabled="editDisabled" />
                    </div>
                    <div v-if="!isGenerating" class="tab-pane fade pt-10" id="tab_4" role="tabpanel"
                        aria-labelledby="tab_4">
                        <label v-if="isSpeedOnly">No lineitem found, please generate first.</label>
                        <DynamicTable v-else :batchId="data.id" :editable="false" />
                    </div>
                    <div v-if="!isGenerating" class="tab-pane fade pt-10" id="tab_5" role="tabpanel"
                        aria-labelledby="tab_5">
                        <Format :batchId="data.id" />
                    </div>
                    <div class="tab-pane fade pt-10" id="tab_6" role="tabpanel" aria-labelledby="tab_3">
                        <div class="mb-4">
                            <h4>Generation Logs</h4>
                        </div>

                        <div v-if="logs && logs.length > 0" class="logs-container">
                            <LogCard v-for="(log, index) in logs" :key="index" :log="log" />
                        </div>

                        <div v-else class="alert alert-info">
                            No generation logs found for this Speed table.
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex">
                    <div class="me-auto">
                        <Link class="btn btn-secondary me-2" :href="route(routeGroupName + '.index')">Back </Link>
                    </div>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>
