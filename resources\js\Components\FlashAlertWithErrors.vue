<script setup>
import { computed } from 'vue';

const props = defineProps({
    status: {
        type: String,
        default: 'danger',
    },
    errors: {
        type: Object,
        default: () => ({}),
    },
});

const hasErrors = computed(() => Object.keys(props.errors).length > 0);
const alertClass = computed(() => 'alert-' + props.status);

const emit = defineEmits(['close']);

const clearErrors = () => {
    emit('close');
};
</script>

<template>
    <div v-if="hasErrors" class="alert alert-dismissible fade show" :class="alertClass" role="alert">
        <template v-if="props.errors.errors">
            <ul class="mb-0">
                <template v-if="typeof props.errors.errors === 'string' && props.errors.errors.trim() !== ''">
                    {{ props.errors.errors }}
                </template>
                <li v-else v-for="(messages, field) in props.errors.errors" :key="field">
                    <template v-if="Array.isArray(messages)">
                        <div v-for="(message, idx) in messages" :key="idx">
                            {{ message }}
                        </div>
                    </template>
                    <template v-else>
                        {{ messages }}
                    </template>
                </li>
            </ul>
        </template>
        <label v-else-if="props.errors.message">{{ props.errors.message }}</label>
        <ul v-else-if="Object.keys(props.errors).length" class="mb-0">
            <li v-for="(message, field) in props.errors" :key="field">
                {{ message }}
            </li>
        </ul>

        <button type="button" class="btn-close" aria-label="Close" @click="clearErrors"></button>
    </div>
</template>
