<?php

namespace App\Jobs;

use App\Models\GenerateLineitemLog;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Throwable;

class GenerateLineitemTableJob implements ShouldQueue
{
    use Queueable;

    public $generate_lineitem_log_id;
    /**
     * Create a new job instance.
     */
    public function __construct($generate_lineitem_log_id)
    {
        $this->generate_lineitem_log_id = $generate_lineitem_log_id;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        //run app:generate-line-item-table
        \Artisan::call('app:generate-line-item-table ' . $this->generate_lineitem_log_id);
    }

    /**
     * Handle a job failure.
     */
    public function failed(?Throwable $exception): void
    {
        $log = GenerateLineitemLog::where('id', $this->generate_lineitem_log_id)->first();
        $log->failed = true;
        $log->exception .= "[" . now()->format('Y-m-d H:i:s') . "] " . $exception->getMessage() . "\n";
        $log->save();
    }
}
