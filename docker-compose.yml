services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: fcc-altera
    working_dir: /var/www
    volumes:
      - .:/var/www
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
    depends_on:
      - db
    networks:
      - laravel

  db:
    image: postgres:17
    container_name: fcc-altera-db
    restart: always
    environment:
      POSTGRES_DB: fcc
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: root
    volumes:
      - dbdata:/var/lib/postgresql/data
    networks:
      - laravel

  nginx:
    image: nginx:alpine
    container_name: laravel-nginx
    ports:
      - "8000:80"
    volumes:
      - .:/var/www
      - ./nginx/default.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - app
    networks:
      - laravel

volumes:
  dbdata:

networks:
  laravel: