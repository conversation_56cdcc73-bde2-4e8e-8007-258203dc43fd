<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class FuseConfigItemAddress extends BaseModel
{
    use SoftDeletes;
    
    protected $fillable = [
        'setting',
        'stop_address',
        'start_address',
        'size',
        'fuse_type',
        'encoding_type',
        'data_map_type',
        'xml_default',
        'fuse_group',
        'fuselink_group',
        'fuse_class',
        'de_rv_xd',
        'expression',
        'comment',
        'fuse_config_item_id',
    ];
}
