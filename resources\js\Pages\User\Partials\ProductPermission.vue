<script setup>
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import Checkbox from '@/Components/Checkbox.vue';
import { useForm, Link } from '@inertiajs/vue3';
import { ref } from 'vue';
import { VueElement } from 'vue';

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    product_types: {
        type: Object,
    },
    product_permissions: {
        type: Object,
    },
});

const routeGroupName = 'users.product_permission';

const form = useForm({
    productTypes: props.product_permissions?.productTypes ?? {},
    products: props.product_permissions?.products ?? {},
    productGroups: props.product_permissions?.productGroups ?? {},
});

// Find parent type for a product
const findParentType = productId => {
    for (const type of props.product_types) {
        if (type.products.some(product => product.id === productId)) {
            return type;
        }
    }
    return null;
};

// Find parent product and type for a group
const findParentProduct = groupId => {
    for (const type of props.product_types) {
        for (const product of type.products) {
            if (product.product_groups.some(group => group.id === groupId)) {
                return { product, type };
            }
        }
    }
    return null;
};

// Handle product selection
const handleProductChange = (productId, checked) => {
    const parentType = findParentType(productId);

    // If checked, select parent type
    if (checked && parentType) {
        form.productTypes[parentType.id] = true;
    }
    // If unchecked, check if other products are selected before unchecking parent type
    else if (parentType) {
        const hasSelectedProducts = parentType.products.some(p => p.id !== productId && form.products[p.id]);
        if (!hasSelectedProducts) {
            form.productTypes[parentType.id] = false;
        }
    }
};

// Handle group selection
const handleGroupChange = (groupId, checked) => {
    const parents = findParentProduct(groupId);
    if (parents) {
        const { product, type } = parents;

        // Auto select parent product and type if checked
        if (checked) {
            form.products[product.id] = true;
            form.productTypes[type.id] = true;
        } else {
            // Check if any other groups under this product are selected
            const hasSelectedGroups = product.product_groups.some(group => group.id !== groupId && form.productGroups[group.id]);
            if (!hasSelectedGroups) {
                form.products[product.id] = false;

                // Check if any other products under this type are selected
                const hasSelectedProducts = type.products.some(p => p.id !== product.id && form.products[p.id]);
                if (!hasSelectedProducts) {
                    form.productTypes[type.id] = false;
                }
            }
        }
    }
};

const selectAll = () => {
    // Select all types, products, and groups
    for (const type of props.product_types) {
        form.productTypes[type.id] = true;

        for (const product of type.products) {
            form.products[product.id] = true;

            for (const group of product.product_groups) {
                form.productGroups[group.id] = true;
            }
        }
    }
    document.getElementById('pSelectAll').style = 'display:none';
    document.getElementById('pRemoveAll').style = 'display:block';
};

const removeAll = () => {
    // Deselect all types, products, and groups
    for (const type of props.product_types) {
        form.productTypes[type.id] = false;

        for (const product of type.products) {
            form.products[product.id] = false;

            for (const group of product.product_groups) {
                form.productGroups[group.id] = false;
            }
        }
    }
    document.getElementById('pRemoveAll').style = 'display:none';
    document.getElementById('pSelectAll').style = 'display:block';
};
</script>

<template>
    <form @submit.prevent="form.patch(route(routeGroupName + '.update', data.id))">
        <div class="row g-3">
            <div class="kt-checkbox-list">
                <div class="d-flex align-items-center mb-2">
                    <InputLabel for="selectAll" value="Check to Access" class="me-3" />
                    <PrimaryButton id="pSelectAll" style="display: block" type="button" @click="selectAll()">Select All</PrimaryButton>
                    <PrimaryButton id="pRemoveAll" style="display: none" type="button" @click="removeAll()">Remove All</PrimaryButton>
                </div>
                <ul v-for="(type, index) in product_types">
                    <li>
                        <InputLabel class="kt-checkbox">
                            <input type="hidden" :name="'form.productTypes[' + type.id + ']'" value="0" />
                            <Checkbox :id="type.name" :name="'form.productTypes[' + type.id + ']'" v-model:checked="form.productTypes[type.id]" value="1">
                                {{ type.name }}
                            </Checkbox>
                        </InputLabel>
                    </li>
                    <ul v-if="type.products && type.products.length > 0" v-for="(product, index) in type.products">
                        <li>
                            <InputLabel class="kt-checkbox">
                                <input type="hidden" :name="'form.products[' + product.id + ']'" value="0" />
                                <Checkbox :id="product.name" :name="'form.products[' + product.id + ']'" v-model:checked="form.products[product.id]" @change="handleProductChange(product.id, $event.target.checked)" value="1">
                                    {{ product.name }}
                                </Checkbox>
                            </InputLabel>
                        </li>
                        <ul v-if="product.product_groups && product.product_groups.length > 0" v-for="(group, index) in product.product_groups">
                            <li>
                                <InputLabel class="kt-checkbox">
                                    <input type="hidden" :name="'form.productGroups[' + group.id + ']'" value="0" />
                                    <Checkbox :id="group.name" :name="'form.productGroups[' + group.id + ']'" v-model:checked="form.productGroups[group.id]" @change="handleGroupChange(group.id, $event.target.checked)" value="1">
                                        {{ group.name }}
                                    </Checkbox>
                                </InputLabel>
                            </li>
                        </ul>
                    </ul>
                </ul>
            </div>
            <div class="col-12">
                <PrimaryButton type="submit" v-html="'Save'"></PrimaryButton>
            </div>
        </div>
    </form>
</template>
