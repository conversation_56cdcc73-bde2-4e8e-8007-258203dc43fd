<script setup>
import FlashAlertWithErrors from '@/Components/FlashAlertWithErrors.vue';
import Modal from '@/Components/Modal.vue';
import Select from '@/Components/Select.vue';
import TextInput from '@/Components/TextInput.vue';
import { ScenarioMode } from '@/enums';
import { useForm } from '@inertiajs/vue3';
import axios from 'axios';
import { onMounted, ref, computed, watch } from 'vue';

const collectionEditorModal = ref(null);
const componentLevelOptions = ref([]);
const componentTypes = ref([]);
const loading = ref(false);
const rowVisibility = ref([]);

const props = defineProps({
    usm_id: {
        type: Number,
        required: true,
    },
    usm_module_id: {
        type: Number,
        required: true,
    },
    scenario_mode: {
        type: String,
        default: 'NonScenarioAttributes',
    },
    scenario_id: {
        type: Number,
        default: null,
    },
    editDisabled: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(['saveSuccess']);

const scenarioModeLabel = ref(props.scenario_mode);
const scenarioId = ref(props.scenario_id);

const form = useForm({
    usm_id: props.usm_id,
    usm_module_id: props.usm_module_id,
    scenario_mode: scenarioModeLabel.value,
    scenario_id: scenarioModeLabel.value === ScenarioMode.SCENARIO ? scenarioId.value : null,
    collections: [],
});

const loadCollectionData = () => {
    loading.value = true;
    axios
        .get(
            route('usm.collection_editor', {
                usm_id: props.usm_id,
                usm_module_id: props.usm_module_id,
                scenario_mode: scenarioModeLabel.value,
                scenario_id: scenarioModeLabel.value === ScenarioMode.SCENARIO ? scenarioId.value : null,
            }),
        )
        .then(response => {
            componentLevelOptions.value = response.data.component_level_options;
            componentTypes.value = response.data.component_types;

            // If any collections exist in the response, load them
            if (response.data.collections) {
                form.collections = response.data.collections;
                // Initialize all rows as visible by default
                rowVisibility.value = Array(form.collections.length).fill(true);
            } else {
                form.collections = [];
                rowVisibility.value = [];
            }
            loading.value = false;
        })
        .catch(error => {
            console.error('Error loading collection data:', error);
            loading.value = false;
        });
};

onMounted(() => {
    const modalElement = document.getElementById('collectionEditorModal-' + props.usm_module_id);
    modalElement.addEventListener('show.bs.modal', () => {
        loadCollectionData();
    });
});

const save = () => {
    loading.value = true;
    axios
        .post(route('usm.collection_editor'), form.data())
        .then(response => {
            loading.value = false;
            emit('saveSuccess');
            closeModal();
            // Optional: Add success notification if needed
        })
        .catch(error => {
            loading.value = false;
            if (error.response && error.response.data) {
                form.setError(error.response.data);
            }
        });
};

const closeModal = () => {
    form.clearErrors();
    resetInput();
    collectionEditorModal.value.close();
};

const resetInput = () => {
    form.reset();
    form.usm_id = props.usm_id;
    form.usm_module_id = props.usm_module_id;
};

//function on this page
const addRow = () => {
    // Get the first key from the componentLevelOptions object
    const firstLevelId = Object.keys(componentLevelOptions.value).length > 0 ? Object.keys(componentLevelOptions.value)[0] : null;

    // Get the level name corresponding to the first ID
    const levelName = firstLevelId ? componentLevelOptions.value[firstLevelId] : null;

    form.collections.push({
        id: null,
        name: '',
        component_level_id: firstLevelId,
        component_type_id: null,
        level: levelName,
        source: null,
        sequence: form.collections.length + 1,
        value_sets: [],
    });

    // Set visibility to true by default for the new row
    rowVisibility.value.push(true);
};

const removeRow = index => {
    form.collections.splice(index, 1);
    // Also remove the corresponding visibility state
    rowVisibility.value.splice(index, 1);
    form.collections.forEach((row, index) => {
        row.sequence = index + 1;
    });
};

const toggleRow = index => {
    rowVisibility.value[index] = !rowVisibility.value[index];
};

const addValueSet = (collectionIndex, collectionId) => {
    // Create a new value set object
    const newValueSet = {
        id: null,
        name: '',
        sequence: form.collections[collectionIndex].value_sets.length + 1,
        usm_collection_id: collectionId,
        usm_id: props.usm_id,
    };

    // Add it to the specified collection's value_sets array
    form.collections[collectionIndex].value_sets.push(newValueSet);
};

const removeValueSet = (collectionIndex, valueSetIndex) => {
    // Remove the value set at the specified index
    form.collections[collectionIndex].value_sets.splice(valueSetIndex, 1);

    // Update sequence numbers for remaining value sets
    form.collections[collectionIndex].value_sets.forEach((valueSet, index) => {
        valueSet.sequence = index + 1;
    });
};

//Computed
const buttonYes = computed(() => {
    return props.editDisabled ? null : 'Save';
});

const filteredComponentTypes = computed(() => {
    return levelId => {
        if (!componentTypes.value.length) return [];

        // If no level is selected, return empty array
        if (!levelId) return [];

        // Get the level name from the options object (key is ID, value is name)
        const levelName = componentLevelOptions.value[levelId];

        if (!levelName) return [];

        // Filter component types that match this level name
        // Include source in the mapped result for later use
        return componentTypes.value
            .filter(type => type.level === levelName)
            .map(type => ({
                id: type.id,
                name: type.name,
                // Store source info for reference
                source: type.source,
            }));
    };
});

const handleComponentTypeChange = (collection, typeId) => {
    // Find the selected component type in the original data
    const selectedType = componentTypes.value.find(type => type.id === parseInt(typeId));
    // If found, set the source
    if (selectedType) {
        collection.source = selectedType.source;
    }
};

//Watcher
watch(
    () => props.scenario_mode,
    newVal => {
        form.scenario_mode = newVal;
        scenarioModeLabel.value = newVal;
    },
    { immediate: true },
);

watch(
    () => props.scenario_id,
    newVal => {
        if (form.scenario_mode === ScenarioMode.SCENARIO) {
            form.scenario_id = newVal;
            scenarioId.value = newVal;
        } else {
            form.scenario_id = null;
            scenarioId.value = null;
        }
    },
    { immediate: true },
);
</script>

<template>
    <Modal ref="collectionEditorModal" @yesEvent="save" @noEvent="closeModal" :id="'collectionEditorModal-' + props.usm_module_id" :title="'Collections & Value Sets'" :buttonYes="buttonYes" :buttonType="'primary'" :form="form" :modalClass="'modal-lg'">
        <FlashAlertWithErrors :errors="form.errors" @close="form.clearErrors()" />
        <div v-if="loading" class="text-center my-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>

        <div v-else class="mt-6">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th width="3%">
                            <button type="button" class="btn btn-sm btn-primary" @click="addRow" :disabled="props.editDisabled">
                                <i class="bi bi-plus"></i>
                            </button>
                        </th>
                        <th>Name</th>
                        <th>Level</th>
                        <th>Component Type</th>
                    </tr>
                </thead>
                <tbody>
                    <template v-for="(collection, index) in form.collections" :key="index">
                        <tr>
                            <td class="text-center align-middle">
                                <button type="button" class="btn btn-sm btn-danger" @click="removeRow(index)" :disabled="props.editDisabled">
                                    <i class="bi bi-dash"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-danger m-1" @click="toggleRow(index)" title="Hide/Show Row"><i :class="['bi', rowVisibility[index] ? 'bi-eye-fill' : 'bi-eye']"></i></button>
                            </td>
                            <td class="align-middle">
                                <input type="hidden" v-model="collection.id" />
                                <TextInput type="text" v-model="collection.name" :disabled="props.editDisabled" placeholder="Collection name" class="w-100" />
                            </td>
                            <td class="align-middle">
                                <Select
                                    v-model="collection.component_level_id"
                                    :disabled="props.editDisabled"
                                    :options="componentLevelOptions"
                                    class="w-100"
                                    @update:modelValue="
                                        value => {
                                            collection.level = componentLevelOptions[value];
                                        }
                                    "
                                />
                            </td>
                            <td class="align-middle">
                                <Select v-model="collection.component_type_id" :disabled="props.editDisabled" :options="filteredComponentTypes(collection.component_level_id)" class="w-100" @update:modelValue="typeId => handleComponentTypeChange(collection, typeId)" />
                            </td>
                        </tr>
                        <tr v-show="rowVisibility[index]">
                            <td colspan="7" class="p-0">
                                <div class="bg-light p-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2"></div>
                                    <table class="table table-sm table-bordered mb-0">
                                        <thead class="table-secondary">
                                            <tr>
                                                <th width="3%">
                                                    <button type="button" class="btn btn-sm btn-primary" @click="addValueSet(index, collection.id)"><i class="bi bi-plus"></i></button>
                                                </th>
                                                <th>Value Sets</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="(value_set, value_setIndex) in collection.value_sets" :key="value_setIndex">
                                                <td class="text-center align-middle">
                                                    <button type="button" class="btn btn-sm btn-danger" @click="removeValueSet(index, value_setIndex)">
                                                        <i class="bi bi-dash"></i>
                                                    </button>
                                                </td>
                                                <td class="align-middle"><TextInput type="text" v-model="value_set.name" /></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </template>
                    <tr v-if="form.collections.length === 0">
                        <td colspan="4" class="text-center">No collections defined. Click the + button to add a collection.</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </Modal>
</template>
