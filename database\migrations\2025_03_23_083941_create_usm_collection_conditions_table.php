<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('usm_collection_conditions', function (Blueprint $table) {
            $table->id();
            $table->string('comment')->nullable();
            $table->text('expression')->nullable();
            $table->smallInteger('sequence')->default(0);
            $table->unsignedBigInteger('usm_id');
            $table->unsignedBigInteger('usm_collection_map_id');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('usm_collection_conditions');
    }
};
