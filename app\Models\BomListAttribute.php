<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;

class BomListAttribute extends BaseModel
{
    protected $fillable = [
        'name',
        'length',
        'sequence',
        'attribute_name',
        'lineitem_attribute_id',
        'bin_matrix_id'
    ];

    public function expressions(): HasMany
    {
        return $this->hasMany(BomListExpression::class);
    }
}
