<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('component_levels', function (Blueprint $table) {
            $table->id();
            $table->integer('level')->index();
            $table->string('name');
            $table->unsignedBigInteger('usm_id'); //universal_spec_managers
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('component_levels');
    }
};
