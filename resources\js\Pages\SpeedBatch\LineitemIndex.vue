<script setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';
import { formatDate } from '@/helper';
import HeadRow from '@/Components/Table/HeadRow.vue';
import { Link } from '@inertiajs/vue3';

const props = defineProps({
    speed_batch_id: {
        type: Number,
    },
    editDisabled: {
        type: Boolean,
        default: false
    },
});

const header = ref([]);
const list = ref({});
const routeGroupName = 'lineitem_managers';

const fetchTableData = async () => {
    const response = await axios.get(route(routeGroupName + '.index', { speed_batch_id: props.speed_batch_id }));
    list.value = response.data.list;
    header.value = response.data.header;
};

onMounted(() => {
    fetchTableData();
});

const destroy = (id, name) => {
    const c = confirm(`Delete this lineitem ${name} ?`);
    if (c) {
        axios.delete(route(routeGroupName + '.destroy', id))
            .then(response => {
                // Show success message
                alert(response.data.message || 'Lineitem deleted successfully');
                // Refresh the table data
                fetchTableData();
            })
            .catch(error => {
                // Show error message
                alert(error.response?.data?.message || 'Error deleting lineitem');
            });
    }
};
</script>

<template>
    <div class="d-flex justify-content-end align-items-center mb-3">
        <div class="d-flex align-items-center gap-2">
            <Link v-if="editDisabled == false" class="btn btn-primary"
                :href="route(routeGroupName + '.create', { speed_batch_id: props.speed_batch_id })">
            <i class="bi bi-plus"></i>
            Create
            </Link>
        </div>
    </div>
    <div class="table-responsive-md">
        <table class="table table-bordered table-striped table-hover">
            <thead>
                <tr>
                    <HeadRow>Actions</HeadRow>
                    <HeadRow v-for="head in header">{{ head.title }}</HeadRow>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(item, index) in list">
                    <td width="10%">
                        <a :href="route(routeGroupName + '.edit', item.id)" class="btn btn-sm btn-link">
                            <i class="bi bi-pencil"></i>
                        </a>
                        <button v-if="editDisabled == false" @click="destroy(item.id, item.name)"
                            class="btn btn-sm btn-link">
                            <i class="bi bi-trash"></i>
                        </button>
                    </td>
                    <td>{{ item.name }}</td>
                    <td>{{ item.module_label }}</td>
                    <td>{{ item.created_user?.name }}</td>
                    <td>{{ formatDate(item.created_at) }}</td>
                </tr>
            </tbody>
        </table>
    </div>
</template>
