<?php

return [

    /*
    |--------------------------------------------------------------------------
    | DEFINE ALL INTEL CUSTOM SETTINGS
    |--------------------------------------------------------------------------
    */
    'base_module_selections' => [
        'Speed' => 'LIRA Data (Speed)', //Always exists
    ],
    'modules' => [
        'Treadmill' => 'Treadmill',
        'Fuse Data' => 'Fuse Data',
        'TP Flow Matrix' => 'TP Flow Matrix',
    ],
    'dataTypes' => [
        'varchar' => 'String',
        'integer' => 'Integer',
        'float' => 'Float',
    ],
    'expressionHtml' => "IF([column] == 'A', 'B', 'C')<br/>
                        IF([column] != 'A', 'B', 'C')<br/>
                        IF([column] >= 1, 'B', 'C')<br/>
                        IF([column] <= 1, 'B', 'C')<br/>
                        IF([column] > 1, 'B', 'C')<br/>
                        IF([column] < 1, 'B', 'C')<br/>
                        SUM([column], [column])<br/>
                        SUB([column], [column])<br/>
                        MUL([column], [column])<br/>
                        DIV([column], [column])<br/>
                        CONCAT([column],' and ',[column])<br/>
                        SUBSTR([column], 1, 2)<br/>
                        REPLACE([column], 'abc', 'def')<br/>
                        LENGTH([column])<br/>
                        IF(LENGTH([column])<10, 'SMALL', 'LARGE')",

    //columns to exclude from the attribute selector or excel editor
    'exclude_columns' => ['id', 'batch_id', 'created_at', 'updated_at', 'deleted_at', 'product_type_id', 'product_id', 'product_group_id', 'line_item_id'],

    //Bin Related
    'default_bom_list_fields' => [
        ['name' => 'Package', 'length' => 2, 'sequence' => 1,],
        ['name' => 'Sample-4|Production-8', 'length' => 1, 'sequence' => 2],
        ['name' => 'ProdID', 'length' => 3, 'sequence' => 3],
        ['name' => 'DLCP', 'length' => 1, 'sequence' => 4],
        ['name' => 'VF', 'length' => 1, 'sequence' => 5,],
        ['name' => 'RevStep', 'length' => 2, 'sequence' => 6],
    ],

    //Fuse Related
    'fuse_dataTypes' => [
        'Binary' => 'Binary',
        'Hex' => 'Hex'
    ],
    'fuse_types' => [
        'Single Ended' => 'Single Ended',
    ],
    'fuse_data_map_types' => [
        'Use Recommended Value' => 'Use Recommended Value',
        'Conditional' => 'Conditional',
        'Direct' => 'Direct'
    ],

    // default component levels - upon creation of a new USM
    'default_component_levels' => [
        ['id' => null, 'level' => 1, 'name' => 'Package'],
        ['id' => null, 'level' => 2, 'name' => 'Rail'],
        ['id' => null, 'level' => 3, 'name' => 'Domain'],
        ['id' => null, 'level' => 4, 'name' => 'Block'],
    ],

    //Speed Table Default Column and Data Type
    //Key all cap for easy comparison but saving into database still depend on user input
    'speed_default_data_types' => [
        'QDF/SSPEC' => 'varchar',
        'MM#' => 'varchar',
        'PRODUCT VARIANT' => 'varchar',
        'POWER GRADE' => 'varchar',
        'PRODUCT GRADE' => 'varchar',
        'CORE SPEED RATIO' => 'float',
        'DENSITY' => 'integer',
        'TRANSCEIVER SPEED RATIO' => 'float',
        'TRANSCEIVER COUNT' => 'float',
        'SPEC CODE' => 'varchar',
        'PINCOUNT' => 'float',
        'SAMPLE/PRODUCTION TYPE' => 'varchar'
    ],
];
