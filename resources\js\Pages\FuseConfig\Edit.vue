<script setup>
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import Checkbox from '@/Components/Checkbox.vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, useForm, Link } from '@inertiajs/vue3';
import { ref, computed, watch, onMounted } from 'vue';
import Select from '@/Components/Select.vue';
import axios from 'axios';
import ExpressionModal from '@/Components/ExpressionModal.vue';
import ExpressionSuggestions from '@/Components/ExpressionSuggestions.vue';
import ProductSelectors from '@/Components/ProductSelectors.vue';
import Multiselect from 'vue-multiselect';
import FloatingScrollButtons from '@/Components/FloatingScrollButtons.vue';

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    productTypes: {
        type: Object,
    },
    products: {
        type: Object,
    },
    productGroups: {
        type: Object,
    },
    binMatrices: {
        type: Object,
    },
    createRevision: {
        type: Boolean,
    },
    dataTypes: {
        type: Object,
    },
    types: {
        type: Object,
    },
    mapTypes: {
        type: Object,
    },
    states: {
        type: Array,
    },
    columns: {
        type: Object,
    },
    expressionHtml: {
        type: String,
    },
});

const routeGroupName = 'fuse_configs';
const routeFuseManager = 'fuse_managers';
const headerTitle = ref('Fuse Config');

const localData = ref({ ...props.data });

onMounted(() => {
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
});

watch(
    () => props.data,
    newVal => {
        localData.value = { ...newVal };
    },
);

const form = useForm({
    name: props.data.name ?? null,
    fuse_config_items: props.data.fuse_config_items ?? [],
    fuse_manager_id: props.data.fuse_manager_id ?? null,
    fuse_manager_name: props.data.fuse_manager?.name ?? null,
});

const addRow = () => {
    form.fuse_config_items.push({
        id: null,
        register: '',
        size: '',
        datatype: '',
        pin_to_read: '',
        pin_to_modify: '',
        direction: 1,
        sequence: form.fuse_config_items.length + 1,
        fuse_config_item_addresses: [],
    });
    rowVisibility.value.push(true);
};

const removeRow = index => {
    form.fuse_config_items.splice(index, 1);
    rowVisibility.value.splice(index, 1);
};

const addAddress = index => {
    form.fuse_config_items[index].fuse_config_item_addresses.push({
        id: null,
        setting: '',
        size: '',
        start_address: '',
        stop_address: '',
        fuse_type: '',
        data_map_type: '',
        de_rv_xd: '',
        fuse_group: '',
        expression: '',
    });
    // Calculate addresses for the new row
    calculateAddresses(index, form.fuse_config_items[index].fuse_config_item_addresses.length - 1);
};

const removeAddress = (index, addressIndex) => {
    form.fuse_config_items[index].fuse_config_item_addresses.splice(addressIndex, 1);
    // Recalculate addresses for all subsequent rows
    form.fuse_config_items[index].fuse_config_item_addresses.forEach((_, i) => {
        if (i >= addressIndex) {
            calculateAddresses(index, i);
        }
    });
};

const calculateAddresses = (index, addressIndex) => {
    const addresses = form.fuse_config_items[index].fuse_config_item_addresses;
    const size = parseInt(addresses[addressIndex].size) || 0;

    // Calculate start address based on previous addresses
    let startAddress = 0;
    for (let i = 0; i < addressIndex; i++) {
        const prevSize = parseInt(addresses[i].size) || 0;
        startAddress += prevSize;
    }

    // Calculate stop address
    const stopAddress = startAddress + (size > 0 ? size - 1 : 0);

    // Update the addresses
    addresses[addressIndex].start_address = startAddress.toString();
    addresses[addressIndex].stop_address = stopAddress.toString();

    // Update subsequent addresses
    for (let i = addressIndex + 1; i < addresses.length; i++) {
        const nextSize = parseInt(addresses[i].size) || 0;
        addresses[i].start_address = (parseInt(addresses[i - 1].stop_address) + 1).toString();
        addresses[i].stop_address = (parseInt(addresses[i].start_address) + (nextSize > 0 ? nextSize - 1 : 0)).toString();
    }
};

const validateSizes = () => {
    let hasError = false;
    form.fuse_config_items.forEach((row, index) => {
        const rowSize = parseInt(row.size) || 0;
        const addressSizesSum = row.fuse_config_item_addresses.reduce((sum, addr) => sum + (parseInt(addr.size) || 0), 0);

        if (rowSize !== addressSizesSum) {
            form.errors[`fuse_config_items.${index}.size`] = `Total address sizes (${addressSizesSum}) must match the row size (${rowSize})`;
            hasError = true;
        }
    });
    return !hasError;
};

const isReadOnly = computed(() => {
    return props.createRevision ? true : false;
});

const editDisabled = computed(() => {
    return localData.value.state == 'SNAPSHOT';
});

const rowVisibility = ref([]);

const toggleRow = index => {
    rowVisibility.value[index] = !rowVisibility.value[index];
};

const submit = () => {
    if (!validateSizes()) {
        return;
    }

    if (props.data.id == null) {
        form.post(route(routeGroupName + '.store'), { preserveState: true });
    } else {
        form.patch(route(routeGroupName + '.update', props.data.id), { preserveState: true });
    }
};

const setState = state => {
    const c = confirm('Change the state to ' + state);
    if (c) {
        axios
            .post(route(routeGroupName + '.state.update', props.data.id), { state: state })
            .then(response => {
                localData.value.state = state;
                alert(response.data.message);
            })
            .catch(error => {
                alert(error.response.data.message);
            });
    }
};

const buttonYes = computed(() => {
    return props.editDisabled ? null : 'Save';
});

const filteredBinMatrices = computed(() => {
    if (!form.product_group_id) {
        return []; // Return empty array if no product group selected
    }

    // Filter bin matrices to only show those matching the selected product group
    return props.binMatrices.filter(matrix => matrix.product_group_id === parseInt(form.product_group_id));
});
</script>

<template>

    <Head :title="headerTitle" />

    <AuthenticatedLayout>
        <template #header> {{ headerTitle }} </template>
        <!-- <span v-if="props.createRevision">(New Revision {{ props.data.revision }})</span> -->
        <form @submit.prevent="submit">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <ul class="nav nav-tabs card-header-tabs">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#tab_1">Details</a>
                        </li>
                    </ul>
                    <div v-if="data.id != null && props.createRevision == false" class="d-flex gap-2">
                        <!-- <div class="dropdown">
                            <button class="btn btn-sm btn-info dropdown-toggle" type="button" data-bs-toggle="dropdown"
                                aria-expanded="false"><i class="bi bi-check-circle"></i> {{ localData.state }}</button>
                            <ul class="dropdown-menu dropdown-menu">
                                <li v-for="state in states" :key="state">
                                    <button type="button" class="dropdown-item" @click="setState(state)"
                                        :disabled="state == localData.state">{{ state }}</button>
                                </li>
                            </ul>
                        </div> -->
                        <div class="dropdown">
                            <button class="btn btn-sm btn-info dropdown-toggle" type="button" data-bs-toggle="dropdown"
                                aria-expanded="false">
                                <i class="bi bi-file-earmark-arrow-down"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu">
                                <li>
                                    <a class="dropdown-item" :href="route(routeGroupName + '.fuse_def', data.id)">Fuse
                                        Definition</a>
                                </li>
                                <li>
                                    <a class="dropdown-item"
                                        :href="route(routeGroupName + '.fuse_sspec', data.id)">SSPEC</a>
                                </li>
                                <!-- <li>
                                    <hr class="dropdown-divider" />
                                </li>
                                <li>
                                    <Link class="dropdown-item" :href="route(routeGroupName + '.revision.create', data.id)"> Create New Revision</Link>
                                </li> -->
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <div class="tab-pane fade pt-10 show active" id="tab_1" role="tabpanel" aria-labelledby="tab_1">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <InputLabel for="name" value="Name" />
                                    <TextInput id="name" type="text" v-model="form.name" :invalid="form.errors.name"
                                        required />
                                    <InputError :message="form.errors.name" />
                                </div>
                                <div class="col-md-3">
                                    <InputLabel for="name" value="Fuse Manager" />
                                    <TextInput id="name" type="text" v-model="form.fuse_manager_name" disabled />
                                </div>
                            </div>

                            <div class="row g-3 mt-2">
                                <div class="col-12">
                                    <div class="text-end">
                                        <!-- Revision: {{ data.revision }} <br /> -->
                                        <template v-if="data.created_user">Created By: {{ data.created_user.name }}
                                        </template>
                                    </div>
                                </div>
                            </div>

                            <PrimaryButton v-if="!editDisabled" type="submit"
                                v-html="data.id == null ? 'Create' : 'Save'" :disabled="form.processing">
                            </PrimaryButton>

                            <div class="row g-3 mt-3">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th width="3%" class="text-center">
                                                <button type="button" class="btn btn-sm btn-primary" @click="addRow"><i
                                                        class="bi bi-plus"></i></button>
                                            </th>
                                            <th width="3%">No</th>
                                            <th>Register</th>
                                            <th>Size</th>
                                            <th>Data Type</th>
                                            <th>Pin To Read</th>
                                            <th>Pin To Modify</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <template v-for="(row, index) in form.fuse_config_items" :key="index">
                                            <tr>
                                                <td class="text-center align-middle">
                                                    <button type="button" class="btn btn-sm btn-danger m-1"
                                                        @click="removeRow(index)" title="Remove Row"><i
                                                            class="bi bi-dash"></i></button>
                                                    <button type="button" class="btn btn-sm btn-danger m-1"
                                                        @click="toggleRow(index)" title="Hide/Show Row"><i
                                                            :class="['bi', rowVisibility[index] ? 'bi-eye-fill' : 'bi-eye']"></i></button>
                                                </td>
                                                <td class="text-center align-middle">{{ index + 1 }}</td>
                                                <td class="text-center align-middle">
                                                    <TextInput type="hidden" v-model="row.id" />
                                                    <TextInput type="text" v-model="row.register" />
                                                </td>
                                                <td class="align-middle">
                                                    <TextInput type="text" v-model="row.size"
                                                        :invalid="form.errors[`fuse_config_items.${index}.size`]" />
                                                    <InputError
                                                        :message="form.errors[`fuse_config_items.${index}.size`]" />
                                                </td>
                                                <td class="align-middle">
                                                    <Select :options="dataTypes" v-model="row.datatype" />
                                                </td>
                                                <td class="align-middle">
                                                    <TextInput type="text" v-model="row.pin_to_read" />
                                                </td>
                                                <td class="align-middle">
                                                    <TextInput type="text" v-model="row.pin_to_modify" />
                                                </td>
                                            </tr>
                                            <tr v-show="rowVisibility[index]">
                                                <td colspan="7" class="p-0">
                                                    <div class="bg-light p-3">
                                                        <div
                                                            class="d-flex justify-content-between align-items-center mb-2">
                                                        </div>
                                                        <table class="table table-sm table-bordered mb-0">
                                                            <thead class="table-secondary">
                                                                <tr>
                                                                    <th width="3%">
                                                                        <button type="button"
                                                                            class="btn btn-sm btn-primary"
                                                                            @click="addAddress(index)"><i
                                                                                class="bi bi-plus"></i></button>
                                                                    </th>
                                                                    <th>Setting</th>
                                                                    <th width="5%">Stop Address</th>
                                                                    <th width="5%">Start Address</th>
                                                                    <th width="5%">Size</th>
                                                                    <th>Fuse Type</th>
                                                                    <th>Data Map Type</th>
                                                                    <th>
                                                                        <a href="#" data-bs-toggle="modal"
                                                                            data-bs-target="#expressionModal">DE/RX/XD</a>
                                                                        <button type="button"
                                                                            class="btn btn-link btn-sm"
                                                                            data-bs-toggle="tooltip" data-bs-html="true"
                                                                            :data-bs-title="expressionHtml">
                                                                            <i class="bi bi-info-circle"></i>
                                                                        </button>
                                                                    </th>
                                                                    <th>Fuse Group</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr v-for="(address, addressIndex) in row.fuse_config_item_addresses"
                                                                    :key="addressIndex">
                                                                    <td class="text-center">
                                                                        <button type="button"
                                                                            class="btn btn-sm btn-danger"
                                                                            @click="removeAddress(index, addressIndex)">
                                                                            <i class="bi bi-dash"></i>
                                                                        </button>
                                                                    </td>
                                                                    <td>
                                                                        <TextInput type="text" v-model="address.setting"
                                                                            required />
                                                                    </td>
                                                                    <td>
                                                                        <TextInput type="text"
                                                                            v-model="address.stop_address" readonly />
                                                                    </td>
                                                                    <td>
                                                                        <TextInput type="text"
                                                                            v-model="address.start_address" readonly />
                                                                    </td>
                                                                    <td>
                                                                        <TextInput type="text" v-model="address.size"
                                                                            @input="calculateAddresses(index, addressIndex)"
                                                                            required />
                                                                    </td>
                                                                    <td><Select :options="types"
                                                                            v-model="address.fuse_type" /></td>
                                                                    <td><Select :options="mapTypes"
                                                                            v-model="address.data_map_type" /></td>
                                                                    <td>
                                                                        <ExpressionSuggestions
                                                                            v-if="address.data_map_type == 'Conditional'"
                                                                            v-model="address.expression"
                                                                            :suggestions="columns" />
                                                                        <Multiselect
                                                                            v-else-if="address.data_map_type == 'Direct'"
                                                                            v-model="address.de_rv_xd"
                                                                            :options="columns" :searchable="true"
                                                                            :close-on-select="true"
                                                                            :show-labels="false" />
                                                                        <TextInput v-else type="text"
                                                                            v-model="address.de_rv_xd" />
                                                                    </td>
                                                                    <td>
                                                                        <TextInput type="text"
                                                                            v-model="address.fuse_group" />
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </td>
                                            </tr>
                                        </template>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex">
                        <div class="me-auto">
                            <Link class="btn btn-secondary me-2"
                                :href="route(routeFuseManager + '.edit', localData.fuse_manager_id)">Back</Link>
                            <PrimaryButton v-if="!editDisabled" type="submit"
                                v-html="data.id == null ? 'Create' : 'Save'" :disabled="form.processing">
                            </PrimaryButton>
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <ExpressionModal />
        <FloatingScrollButtons />
    </AuthenticatedLayout>
</template>
