<template>
    <li class="tree-node">
        <div class="node-content">
            <div class="node-left">
                <button v-if="hasChildren(node)" type="button" class="btn btn-sm expand-btn"
                    @click.prevent="toggleNode(node.id)">
                    <i :class="isExpanded(node.id) ? 'bi bi-chevron-down' : 'bi bi-chevron-right'"></i>
                </button>
                <span v-else class="expand-placeholder"></span>

                <div class="d-flex align-items-center">
                    <input v-if="enableCheckbox" type="checkbox" class="form-check-input me-1"
                        :checked="isChecked(node.id)" @change="toggleCheck(node)" />
                    <span class="node-label" :class="{
                        selected: isSelected(node.id),
                        strikethrough: !isAvailableComponentType,
                    }" @click="selectNode(node)">
                        {{ node.name || 'Unnamed Component' }}
                    </span>
                </div>
            </div>

            <div v-if="enableButtons" class="node-actions">
                <button type="button" class="btn btn-sm btn-outline-primary me-1" title="Add same level component"
                    :disabled="isProcessing" @click.prevent="$emit('add-same-level', node)">
                    <i class="bi bi-plus"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-success me-1" title="Add child component"
                    :disabled="isProcessing || node.level >= maxLevel" @click.prevent="$emit('add-next-level', node)">
                    <i class="bi bi-arrow-down-short"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-danger" title="Delete component"
                    :disabled="isProcessing" @click.prevent="$emit('delete-node', node)">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        </div>

        <!-- Recursively render children -->
        <ul v-if="hasChildren(node) && isExpanded(node.id)" class="nested-tree">
            <tree-node v-for="child in node.children" :key="child.id" :node="child" :max-level="maxLevel"
                :selected-node-id="selectedNodeId" :expanded-nodes="expandedNodes" :enable-buttons="enableButtons"
                :enable-checkbox="enableCheckbox" :checked-nodes="checkedNodes" :default-expanded="defaultExpanded"
                :available-component-type-ids="availableComponentTypeIds" :checked-component-ids="checkedComponentIds"
                :is-processing="isProcessing" @toggle-expand="$emit('toggle-expand', $event)"
                @select-node="$emit('select-node', $event)" @add-same-level="$emit('add-same-level', $event)"
                @add-next-level="$emit('add-next-level', $event)" @delete-node="$emit('delete-node', $event)"
                @toggle-check="$emit('toggle-check', $event)" />
        </ul>
    </li>
</template>

<script setup>
import { computed, onMounted, watch } from 'vue';

const props = defineProps({
    node: {
        type: Object,
        required: true,
    },
    maxLevel: {
        type: Number,
        required: true,
    },
    selectedNodeId: {
        type: [Number, String, null],
        default: null,
    },
    expandedNodes: {
        type: Set,
        required: true,
    },
    enableButtons: {
        type: Boolean,
        default: false,
    },
    enableCheckbox: {
        type: Boolean,
        default: false,
    },
    checkedNodes: {
        type: Set,
        default: () => new Set(),
    },
    defaultExpanded: {
        type: Boolean,
        default: true,
    },
    availableComponentTypeIds: {
        type: Array,
        default: () => [],
    },
    checkedComponentIds: {
        type: Array,
        default: () => [],
    },
    isProcessing: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(['toggle-expand', 'select-node', 'add-same-level', 'add-next-level', 'delete-node', 'toggle-check']);

const hasChildren = node => {
    return node.children && node.children.length > 0;
};

const isExpanded = nodeId => {
    return nodeId && props.expandedNodes.has(nodeId);
};

const isSelected = nodeId => {
    return props.selectedNodeId === nodeId;
};

const isChecked = nodeId => {
    return nodeId && props.checkedNodes.has(nodeId);
};

const toggleNode = nodeId => {
    emit('toggle-expand', nodeId);
};

const selectNode = node => {
    emit('select-node', node);
};

const toggleCheck = node => {
    emit('toggle-check', node);
};

const isAvailableComponentType = computed(() => {
    // If availableComponentTypeIds is empty or undefined, all component types are available
    if (!props.availableComponentTypeIds || props.availableComponentTypeIds.length === 0) {
        return true;
    }

    // The component type ID may be in different properties based on the data structure
    const componentTypeId = props.node.component_type_id || props.node.componentTypeId || (props.node.componentType ? props.node.componentType.id : null);

    // If we can't find a component type ID, don't apply strikethrough
    if (componentTypeId === null || componentTypeId === undefined) {
        return true;
    }

    // Check if the node's component_type_id is in the available list
    return props.availableComponentTypeIds.includes(componentTypeId);
});

onMounted(() => {
    if (props.defaultExpanded && props.node.id && !props.expandedNodes.has(props.node.id)) {
        emit('toggle-expand', props.node.id);
    }

    if (props.checkedComponentIds.length > 0 && props.enableCheckbox) {
        // Check if this component is in the checkedComponentIds array
        if (props.checkedComponentIds.includes(props.node.id)) {
            emit('toggle-check', props.node.id);
        }
    }
});

watch(
    () => props.checkedComponentIds,
    newIds => {
        if (newIds.length > 0 && props.enableCheckbox) {
            const isInCheckedIds = newIds.includes(props.node.id);
            const isAlreadyChecked = props.checkedNodes.has(props.node.id);

            // Only toggle if the state differs
            if (isInCheckedIds && !isAlreadyChecked) {
                emit('toggle-check', props.node.id);
            } else if (!isInCheckedIds && isAlreadyChecked) {
                emit('toggle-check', props.node.id);
            }
        }
    },
    { deep: true },
);
</script>

<style scoped>
.tree-node {
    margin: 5px 0;
    position: relative;
}

/* Ensure the tree nodes have no bullet points */
.nested-tree {
    list-style-type: none;
    padding-left: 20px;
    position: relative;
}

/* Add vertical lines to nested items */
.nested-tree::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 8px;
    width: 1px;
    background-color: #dee2e6;
}

/* Add horizontal lines to connect to vertical lines */
.nested-tree .tree-node::before {
    content: '';
    position: absolute;
    top: 12px;
    left: -12px;
    width: 12px;
    height: 1px;
    background-color: #dee2e6;
}

/* Node content layout */
.node-content {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
}

.node-left {
    display: flex;
    align-items: center;
}

/* Ensure buttons are properly displayed */
.expand-btn {
    background: none;
    border: none;
    padding: 0 5px;
    cursor: pointer;
}

.expand-placeholder {
    width: 24px;
    display: inline-block;
}

/* Style for node labels with hover effect */
.node-label {
    padding: 3px 8px;
    cursor: pointer;
    border-radius: 3px;
    margin-right: 10px;
}

.node-label:hover {
    background-color: #f0f0f0;
}

.node-label.selected {
    background-color: #e0e0ff;
    font-weight: bold;
}

.node-label.strikethrough {
    text-decoration: line-through;
    color: #999;
}

/* Button alignment to the right */
.node-actions {
    display: none;
    margin-left: auto;
}

.tree-node:hover .node-actions {
    display: flex;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}
</style>
