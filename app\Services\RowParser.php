<?php

namespace App\Services;

class Expression
{
    protected $expression;
    protected $debug;

    public function __construct(string $expression, bool $debug = false)
    {
        $this->expression = $expression;
        $this->debug = $debug;
    }

    public function evaluate(array $row)
    {
        return $this->parseExpression($this->expression, $row);
    }

    protected function parseExpression(string $expr, array $row)
    {
        // Handle column references in square brackets first
        $expr = preg_replace_callback('/\[([^\]]+)\]/', function ($matches) use ($row) {
            $columnName = $matches[1];
            return isset($row[$columnName]) ? $this->wrapValue($row[$columnName]) : $matches[0];
        }, $expr);

        // Handle nested expressions
        $expr = $this->resolveNestedExpressions($expr, $row);

        // Convert to uppercase for case-insensitive comparison
        $upperExpr = strtoupper($expr);

        // Parse specific functions with case-insensitive patterns
        // Handle IF functions (supports nested IF args)
        if (preg_match('/^IF\((.*)\)$/i', $expr, $ifMatches)) {
            if ($this->debug) {
                \Log::info("handleIfExpression:");
                \Log::info("expression: " . $expr);
            }
            return $this->handleIfExpression($ifMatches[1], $row);
        }

        if (preg_match('/^MUL\((.*),(.*)\)$/i', $expr, $matches)) {
            return $this->handleMulExpression($matches, $row);
        }

        if (preg_match('/^DIV\((.*),(.*)\)$/i', $expr, $matches)) {
            return $this->handleDivExpression($matches, $row);
        }

        if (preg_match('/^SUM\((.*),(.*)\)$/i', $expr, $matches)) {
            return $this->handleSumExpression($matches, $row);
        }

        if (preg_match('/^SUB\((.*),(.*)\)$/i', $expr, $matches)) {
            return $this->handleSubExpression($matches, $row);
        }

        if (preg_match('/^AVG\((.*),(.*)\)$/i', $expr, $matches)) {
            return $this->handleAvgExpression($matches, $row);
        }

        if (preg_match('/^CONCAT\((.*)\)$/i', $expr, $matches)) {
            return $this->handleConcatExpression($matches, $row);
        }

        if (preg_match('/^SUBSTR\((.*),(.*),(.*)\)$/i', $expr, $matches)) {
            return $this->handleSubstrExpression($matches, $row);
        }

        if (preg_match('/^REPLACE\((.*),(.*),(.*)\)$/i', $expr, $matches)) {
            return $this->handleReplaceExpression($matches, $row);
        }

        if (preg_match('/^LENGTH\((.*)\)$/i', $expr, $matches)) {
            return $this->handleLengthExpression($matches, $row);
        }

        // If no function matches, check if it's a column reference
        if (isset($row[$expr])) {
            return $row[$expr];
        }

        // If it's a literal value
        return trim($expr, "'\"");
    }

    protected function resolveNestedExpressions(string $expr, array $row)
    {
        // Evaluate innermost non-IF functions first to prevent recursive IF handling
        $pattern = '/\b(?!IF\b)([A-Za-z_][A-Za-z0-9_]*)\(([^()]*)\)/i';
        $iterations = 0;
        while (preg_match($pattern, $expr, $matches)) {
            if (++$iterations > 100) {
                break;
            }
            $fullFunction = $matches[0];
            // Skip full-expression to avoid recursion
            if (trim($fullFunction) === trim($expr)) {
                break;
            }
            $evaluated = $this->parseExpression($fullFunction, $row);
            // Stop if no simplification
            if ((string)$evaluated === $fullFunction || (is_string($evaluated) && "'{$evaluated}'" === $fullFunction)) {
                break;
            }
            $replacement = is_string($evaluated) ? "'{$evaluated}'" : $evaluated;
            // Replace only the first occurrence
            $expr = preg_replace('/' . preg_quote($fullFunction, '/') . '/', $replacement, $expr, 1);
        }
        return $expr;
    }

    protected function handleIfExpression(string $content, array $row)
    {
        [$condExpr, $trueExpr, $falseExpr] = $this->splitFunctionArgs($content, 3);
        $condition = $this->parseCondition(trim($condExpr), $row);
        $trueValue = $this->parseExpression(trim($trueExpr), $row);
        $falseValue = $this->parseExpression(trim($falseExpr), $row);

        if ($this->debug) {
            \Log::info("content: " . $content);
            \Log::info("condition: " . ($condition ? "TRUE" : "FALSE") . " - " . $trueValue . " : " . $falseValue);
            \Log::info("===========================");
        }
        return $condition ? $trueValue : $falseValue;
    }

    protected function handleMulExpression(array $matches, array $row)
    {
        $value1 = $this->parseExpression(trim($matches[1]), $row);
        $value2 = $this->parseExpression(trim($matches[2]), $row);
        return $value1 * $value2;
    }

    protected function handleDivExpression(array $matches, array $row)
    {
        $value1 = $this->parseExpression(trim($matches[1]), $row);
        $value2 = $this->parseExpression(trim($matches[2]), $row);

        // Check for division by zero
        if ($value2 == 0) {
            return 0; // or you could throw an exception
        }

        return $value1 / $value2;
    }

    protected function handleSumExpression(array $matches, array $row)
    {
        $value1 = $this->parseExpression(trim($matches[1]), $row);
        $value2 = $this->parseExpression(trim($matches[2]), $row);
        return $value1 + $value2;
    }

    protected function handleSubExpression(array $matches, array $row)
    {
        $value1 = $this->parseExpression(trim($matches[1]), $row);
        $value2 = $this->parseExpression(trim($matches[2]), $row);
        return $value1 - $value2;
    }

    protected function handleAvgExpression(array $matches, array $row)
    {
        $value1 = $this->parseExpression(trim($matches[1]), $row);
        $value2 = $this->parseExpression(trim($matches[2]), $row);
        return ($value1 + $value2) / 2;
    }

    protected function handleConcatExpression(array $matches, array $row)
    {
        $expr = $matches[1];
        $args = [];
        $current = '';
        $inSingle = false;
        $inDouble = false;
        $len = strlen($expr);
        for ($i = 0; $i < $len; $i++) {
            $char = $expr[$i];
            if ($char === "'" && !$inDouble) {
                $inSingle = !$inSingle;
                $current .= $char;
            } elseif ($char === '"' && !$inSingle) {
                $inDouble = !$inDouble;
                $current .= $char;
            } elseif ($char === ',' && !$inSingle && !$inDouble) {
                $args[] = $current;
                $current = '';
            } else {
                $current .= $char;
            }
        }
        if ($current !== '' || empty($args)) {
            $args[] = $current;
        }
        $result = '';
        foreach ($args as $arg) {
            $part = trim($arg);
            // Literal string segment
            if ((substr($part, 0, 1) === "'" && substr($part, -1) === "'")
                || (substr($part, 0, 1) === '"' && substr($part, -1) === '"')
            ) {
                // Strip outer quotes
                $literal = substr($part, 1, -1);
                // Unescape escaped characters
                $literal = stripslashes($literal);
                $result .= $literal;
            }
            // Column reference segment
            elseif (preg_match('/^\[([^\]]+)\]$/', $part, $colMatch)) {
                $colName = $colMatch[1];
                $result .= $row[$colName] ?? '';
            } else {
                // Nested expression or other segment
                $value = $this->parseExpression($part, $row);
                if (is_string($value)) {
                    $value = trim($value, "'");
                }
                $result .= $value;
            }
        }
        return $result;
    }

    protected function handleSubstrExpression(array $matches, array $row)
    {
        $string = $this->parseExpression(trim($matches[1]), $row);
        $start = intval($this->parseExpression(trim($matches[2]), $row));
        $length = $this->parseExpression(trim($matches[3]), $row);

        //From user perspective they want to start count from 1 not 0
        if ($start > 0) $start--;

        return substr($string, $start, $length);
    }

    protected function handleReplaceExpression(array $matches, array $row)
    {
        $string = $this->parseExpression(trim($matches[1]), $row);
        $search = $this->parseExpression(trim($matches[2]), $row);
        $replace = $this->parseExpression(trim($matches[3]), $row);
        return str_replace($search, $replace, $string);
    }

    protected function handleLengthExpression(array $matches, array $row)
    {
        $value = $this->parseExpression(trim($matches[1]), $row);
        return strlen($value);
    }

    protected function parseCondition(string $condition, array $row)
    {
        // Handle AND conditions first
        if (strpos($condition, '&&') !== false || strpos($condition, ' AND ') !== false) {
            // Split by both AND syntaxes
            $parts = preg_split('/\&\&|AND/i', $condition);
            foreach ($parts as $part) {
                if (!$this->parseCondition(trim($part), $row)) {
                    return false;
                }
            }
            return true;
        }

        // Handle OR conditions
        if (strpos($condition, '||') !== false || strpos($condition, ' OR ') !== false) {
            // Split by both OR syntaxes
            $parts = preg_split('/\|\||OR/i', $condition);
            foreach ($parts as $part) {
                if ($this->parseCondition(trim($part), $row)) {
                    return true;
                }
            }
            return false;
        }

        // Handle comparison operators
        // Order is important - check longer operators first
        if (strpos($condition, '!=') !== false) {
            list($left, $right) = array_map('trim', explode('!=', $condition));
            $leftValue = $this->parseExpression($left, $row);
            $rightValue = $this->parseExpression($right, $row);
            return $leftValue != $rightValue;
        }

        if (strpos($condition, '>=') !== false) {
            list($left, $right) = array_map('trim', explode('>=', $condition));
            $leftValue = $this->parseExpression($left, $row);
            $rightValue = $this->parseExpression($right, $row);
            return $leftValue >= $rightValue;
        }

        if (strpos($condition, '<=') !== false) {
            list($left, $right) = array_map('trim', explode('<=', $condition));
            $leftValue = $this->parseExpression($left, $row);
            $rightValue = $this->parseExpression($right, $row);
            return $leftValue <= $rightValue;
        }

        if (strpos($condition, '==') !== false) {
            list($left, $right) = array_map('trim', explode('==', $condition));
            $leftValue = $this->parseExpression($left, $row);
            $rightValue = $this->parseExpression($right, $row);
            return $leftValue == $rightValue;
        }

        if (strpos($condition, '>') !== false) {
            list($left, $right) = array_map('trim', explode('>', $condition));
            $leftValue = $this->parseExpression($left, $row);
            $rightValue = $this->parseExpression($right, $row);
            return $leftValue > $rightValue;
        }

        if (strpos($condition, '<') !== false) {
            list($left, $right) = array_map('trim', explode('<', $condition));
            $leftValue = $this->parseExpression($left, $row);
            $rightValue = $this->parseExpression($right, $row);
            return $leftValue < $rightValue;
        }

        return false;
    }

    protected function wrapValue($value)
    {
        if (is_string($value)) {
            return "'$value'";
        }
        return $value;
    }

    protected function splitFunctionArgs(string $string, int $expectedCount): array
    {
        $parts = [];
        $current = '';
        $depth = 0;
        $len = strlen($string);
        for ($i = 0; $i < $len; $i++) {
            $char = $string[$i];
            if ($char === '(') {
                $depth++;
                $current .= $char;
            } elseif ($char === ')') {
                $depth--;
                $current .= $char;
            } elseif ($char === ',' && $depth === 0) {
                $parts[] = $current;
                $current = '';
            } else {
                $current .= $char;
            }
        }
        $parts[] = $current;
        if (count($parts) > $expectedCount) {
            $last = array_slice($parts, $expectedCount - 1);
            $parts = array_slice($parts, 0, $expectedCount - 1);
            $parts[] = implode(',', $last);
        }
        while (count($parts) < $expectedCount) {
            $parts[] = '';
        }
        return $parts;
    }
}

class RowParser
{
    protected $expressions = [];

    public function addExpression(string $field, string $expression, bool $debug = false)
    {
        $this->expressions[$field] = new Expression($expression, $debug);
    }

    public function isExpression(?string $input): bool
    {
        if ($input === null) return false;
        $input = trim($input);
        // Column reference, e.g. [column]
        if (preg_match('/^\[[^\]]+\]$/', $input)) {
            return true;
        }
        // Supported function calls: IF, MUL, DIV, SUM, SUB, AVG, CONCAT, SUBSTR, REPLACE, LENGTH
        if (preg_match('/^(IF|MUL|DIV|SUM|SUB|AVG|CONCAT|SUBSTR|REPLACE|LENGTH)\s*\(.*\)$/i', $input)) {
            return true;
        }
        return false;
    }

    public function parseRow(array $row, bool $include_row = false)
    {
        if ($include_row) {
            $result = $row;
        } else {
            $result = [];
        }
        foreach ($this->expressions as $field => $expression) {
            $result[$field] = $expression->evaluate($row);
        }
        return $result;
    }
}
