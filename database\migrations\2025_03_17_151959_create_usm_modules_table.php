<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('usm_modules', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('keyword')->nullable();
            $table->string('scenario_mode')->nullable();
            $table->smallInteger('sequence')->default(0);
            $table->boolean('active')->default(true);
            $table->unsignedBigInteger('usm_id')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('usm_modules');
    }
};
