<?php

namespace App\Console\Commands;

use App\Models\GenerateLineitemLog;
use App\Models\LineDynamic;
use App\Models\LineitemManager;
use App\Services\RowParser;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class GenerateLineItemTable extends BaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-line-item-table {generate_lineitem_log_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dynamicially generate a new table base on the module';

    protected $generate_lineitem_log_id = null;
    protected $log = null;
    protected $basedModal = null;
    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->generate_lineitem_log_id = $this->argument('generate_lineitem_log_id');
        $this->start();
    }

    public function start()
    {
        $this->log = GenerateLineitemLog::with(['lineitemManager.lineitemAttributes', 'lineitemManager.speedBatch'])->find($this->generate_lineitem_log_id);
        if (!$this->log) {
            $this->updateFail('No GenerateLineitemLog found.');
            return;
        }

        $line = $this->log->lineitemManager;

        if (!$line) {
            $this->updateFail('No LineItemManager found.');
            return;
        } else if ($line->approved) {
            $this->updateFail("LineItemManager $line->id has been approved, no longer able to generate.");
            return;
        }

        $this->basedModal = $line->speedBatch->getBasedModal();

        $newTable = $line->generate_table_name;

        if (!$this->createTable($newTable, $line)) return;

        $this->populateData($newTable, $line);

        $this->createViewTable($line);

        $this->updateMessage("GenerateLineitemTable {$this->log->id} completed.");
    }


    private function createTable($newTable, $line)
    {
        try {
            // Drop table if exists (PostgreSQL syntax)
            DB::statement("DROP TABLE IF EXISTS \"{$newTable}\" CASCADE");

            // Check if the new table already exists
            if (Schema::hasTable($newTable)) {
                $this->updateFail("Table '{$newTable}' already exists.");
                return false;
            }


            // Build CREATE TABLE statement
            $createSQL = "CREATE TABLE \"{$newTable}\" (";
            $columnDefinitions = [];

            //Primary Key
            $columnDefinitions[] = "\"speed_id\" bigint PRIMARY KEY";

            // Add line item attribute columns
            if (isset($line->lineitemAttributes)) {
                foreach ($line->lineitemAttributes->reverse() as $attribute) {
                    if (!$attribute->active) continue;

                    $columnName = $attribute->column_name;
                    $definition = "\"{$columnName}\" ";

                    $definition .= databaseDataType($attribute->format);

                    $definition .= " NULL";
                    $columnDefinitions[] = $definition;
                }
            }

            $createSQL .= implode(', ', $columnDefinitions) . ')';

            // Create the new table
            DB::statement($createSQL);

            $this->updateMessage("createTable '{$newTable}' successfully.");
            return true;
        } catch (\Exception $e) {
            $this->updateFail("createTable {$newTable} failed: " . $e->getMessage());
            return false;
        }
    }

    private function populateData($newTable, $line)
    {
        try {
            //Start populate data
            $data = $this->basedModal->orderBy('id')->get();

            //New Line Table
            $lineTable = new LineDynamic;
            $lineTable->setTable($newTable);

            foreach ($data as $d) {
                $parser = new RowParser;
                foreach ($line->lineitemAttributes as $attribute) {
                    if (!$attribute->active || empty($attribute->expression)) continue;
                    $parser->addExpression($attribute->column_name, $attribute->expression);
                }
                $result = $parser->parseRow($d->toArray());

                $insert_row = [];
                foreach ($result as $column => $value) {
                    $insert_row[$column] = $value;
                }

                $insert_row['speed_id'] = $d->id;

                $lineTable->insert($insert_row);
            }
            //Update LineItemManager
            $line->generated_at = now();
            $line->save();

            $this->updateMessage("populateData {$newTable} successfully.");
            return true;
        } catch (\Exception $e) {
            $this->updateFail("populateData {$newTable} failed: " . $e->getMessage());
            return false;
        }
    }

    private function createViewTable($line)
    {
        $this->updateMessage("Start createViewTable");

        $basedTable = $line->speedBatch->generate_based_name;
        $newViewTable = $line->speedBatch->generate_view_name;
        try {
            // Drop table if exists (PostgreSQL syntax)
            DB::statement("DROP MATERIALIZED VIEW IF EXISTS \"{$newViewTable}\"");

            //Find other LineitemManager with different module to combine
            $otherLines = LineitemManager::where('module', "!=", $line->module)
                ->where('speed_batch_id', $line->speed_batch_id)
                //->where('approved', true)
                ->get();

            $line_tables = [$line->generate_table_name];
            foreach ($otherLines as $otherLine) {
                $line_tables[] = $otherLine->generate_table_name;
            }

            $selectColumns = ["{$basedTable}.id"];
            foreach ($line_tables as $line_table) {
                $columns = (new LineDynamic)->setTable($line_table)->getTableColumns();
                foreach ($columns as $column) {
                    $selectColumns[] = "{$line_table}.\"{$column}\"";
                }
            }
            //Get All Columns from Speed
            $speedColumns = $this->basedModal->getTableColumns();
            foreach ($speedColumns as $column) {
                $selectColumns[] = "{$basedTable}.\"{$column}\"";
            }

            //Build SELECT Statement
            $selectSQL = "SELECT ";
            foreach ($selectColumns as $column) {
                $selectSQL .= "{$column},";
            }
            $selectSQL = rtrim($selectSQL, ",");

            // Build CREATE TABLE statement
            $createSQL = "CREATE MATERIALIZED VIEW \"{$newViewTable}\" AS {$selectSQL} FROM {$basedTable} ";
            foreach ($line_tables as $line_table) {
                $createSQL .= " LEFT JOIN \"{$line_table}\" ON {$basedTable}.id = \"{$line_table}\".speed_id";
            }

            // Create the new table
            DB::statement($createSQL);

            //Update GenerateLineitemLog
            $this->log->completed_at = now();
            $this->log->save();

            $this->updateMessage("createViewTable '{$newViewTable}' successfully.");

            return true;
        } catch (\Exception $e) {
            $this->updateFail("createViewTable {$newViewTable} failed: " . $e->getMessage());
            return false;
        }
    }

    //Set to failed and record the message
    private function updateFail($message)
    {
        $this->log($message);
        $this->log->failed = true;
        $this->log->exception = $message;
        $this->log->save();
    }

    private function updateMessage($message)
    {
        $this->log($message);
        // Format message with timestamp and line break before appending
        $formattedMessage = "[" . now()->format('Y-m-d H:i:s') . "] " . $message . "\n";
        $this->log->messages .= $formattedMessage;
        $this->log->save();
    }
}
