<?php

namespace App\Traits;

use App\Models\Product;
use App\Models\ProductGroup;
use App\Models\ProductType;

trait WithProductData
{
    /**
     * Get product types, products, and product groups for select dropdowns
     *
     * @return array
     */
    protected function getProductData(): array
    {
        return [
            'productTypes' => ProductType::select('name', 'id')->get(),
            'products' => Product::select('name', 'id', 'product_type_id')->get(),
            'productGroups' => ProductGroup::select('name', 'id', 'product_id')->get(),
        ];
    }

    /**
     * Merge product data with existing data array
     *
     * @param array $data
     * @return array
     */
    protected function withProductData(array $data = []): array
    {
        return array_merge($data, $this->getProductData());
    }
}
