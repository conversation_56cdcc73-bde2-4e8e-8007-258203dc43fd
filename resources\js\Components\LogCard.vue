<script setup>
import { computed } from 'vue';

const props = defineProps({
    log: {
        type: Object,
        required: true,
    },
    showFullException: {
        type: Boolean,
        default: true,
    },
});

const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString();
};

const statusClass = computed(() => {
    if (props.log.failed) return 'bg-danger text-white';
    if (props.log.completed_at) return 'bg-success text-white';
    return 'bg-warning text-dark';
});

const statusText = computed(() => {
    if (props.log.failed) return 'Failed';
    if (props.log.completed_at) return 'Completed';
    return 'In Progress';
});

const getDuration = () => {
    if (!props.log.started_at || !props.log.completed_at) return 'N/A';

    const start = new Date(props.log.started_at);
    const end = new Date(props.log.completed_at);
    const durationMs = end - start;

    // Format duration
    const seconds = Math.floor(durationMs / 1000);
    if (seconds < 60) return `${seconds} seconds`;

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes} min ${remainingSeconds} sec`;
};

const truncateException = (exception) => {
    if (!exception) return '';
    if (props.showFullException) return exception;

    // Limit to 10 lines for the preview
    const lines = exception.split('\n');
    if (lines.length > 10) {
        return lines.slice(0, 10).join('\n') + '\n... (see full logs for more details)';
    }
    return exception;
};

const truncateText = (text) => {
    if (!text) return '';
    if (props.showFullException) return text;

    // Limit to 15 lines for the preview
    const lines = text.split('\n');
    if (lines.length > 15) {
        return lines.slice(0, 15).join('\n') + '\n... (see full logs for more details)';
    }
    return text;
};
</script>

<template>
    <div class="card mb-4 shadow-sm">
        <div class="card-header" :class="statusClass">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="m-0">{{ statusText }}</h5>
                <span class="badge bg-light text-dark">{{ formatDate(log.created_at) }}</span>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <!-- Left section: User info and status -->
                <div class="col-md-4 border-end">
                    <div class="mb-3">
                        <strong>Generated by:</strong> {{ log.user?.name || 'Unknown' }}
                    </div>
                    <div class="mb-3">
                        <strong>Started:</strong> {{ formatDate(log.started_at) }}
                    </div>
                    <div class="mb-3">
                        <strong>Completed:</strong> {{ formatDate(log.completed_at) }}
                    </div>
                    <div class="mb-3">
                        <strong>Duration:</strong> {{ getDuration() }}
                    </div>
                    <div v-if="log.failed" class="alert alert-danger mt-3">
                        <strong>Error:</strong> Generation failed
                    </div>
                </div>

                <!-- Right section: Messages and exceptions -->
                <div class="col-md-8">
                    <!-- Messages section -->
                    <div v-if="log.messages" class="messages-container mb-4">
                        <h6 class="text-primary mb-2">Messages:</h6>
                        <div class="bg-light p-3 rounded overflow-auto" style="max-height: 200px;">
                            <pre class="mb-0"><code>{{ truncateText(log.messages) }}</code></pre>
                        </div>
                    </div>

                    <!-- Exception section -->
                    <div v-if="log.exception" class="exception-container">
                        <h6 class="text-danger mb-2">Exception Details:</h6>
                        <div class="bg-light p-3 rounded overflow-auto"
                            :style="{ maxHeight: showFullException ? '300px' : '200px' }">
                            <pre class="mb-0"><code>{{ truncateException(log.exception) }}</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.border-end {
    border-right: 1px solid #dee2e6;
}

.exception-container pre,
.messages-container pre {
    white-space: pre-wrap;
    word-break: break-word;
}

.messages-container {
    border-bottom: 1px solid #eee;
    padding-bottom: 1rem;
}
</style>
