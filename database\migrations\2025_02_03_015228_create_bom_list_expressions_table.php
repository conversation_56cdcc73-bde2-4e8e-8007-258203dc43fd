<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bom_list_expressions', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->nullable(); //A way to diffrentiate repeated row with same bin_matrix_item_id
            $table->string('value')->nullable();
            $table->unsignedBigInteger('bom_list_attribute_id')->nullable(); // Foreign key to bom_list_attributes
            $table->unsignedBigInteger('bin_matrix_id')->nullable();
            $table->unsignedBigInteger('bin_matrix_item_id')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bom_list_expressions');
    }
};
