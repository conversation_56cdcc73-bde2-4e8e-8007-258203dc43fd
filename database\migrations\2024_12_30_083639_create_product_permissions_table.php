<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_permissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id');
            $table->morphs('accessible');
            $table->timestamps();

            $table->unique(['user_id', 'accessible_type', 'accessible_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_permissions');
    }
};
