<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('usm_module_component_attribute_values', function (Blueprint $table) {
            $table->id();
            $table->text('input_value')->nullable(); //can be value or expression
            $table->string('value')->nullable(); //actual value
            // $table->unsignedBigInteger('collection_id')->nullable(); //When use collection will not use value
            $table->unsignedBigInteger('component_id')->nullable();
            $table->unsignedBigInteger('usm_module_component_attribute_id')->nullable();
            $table->unsignedBigInteger('usm_scenario_id')->nullable(); //Record this value is belongs to which USMModuleScenario
            $table->unsignedBigInteger('usm_id');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('usm_module_component_attribute_values');
    }
};
