<?php

namespace App\Helpers;

use App\Models\Component;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Facades\DB;

class ComponentHelper
{
    /**
     * Get the full hierarchy tree of components.
     *
     * @param int $usm_id
     * @param bool $withComponentType
     * @return array
     */
    public function getComponentTree(int $usm_id, bool $withComponentType = false)
    {
        // Get all top-level components (level 1)
        $query = Component::whereNull('parent_id')->where('usm_id', $usm_id);
        $relations = ['children'];
        if ($withComponentType) {
            $relations[] = 'componentType';
            $relations[] = 'children.componentType';
        }
        $topLevelComponents = $query->with($relations)->get();
        if ($topLevelComponents->isNotEmpty()) {
            $this->eagerLoadRecursively($topLevelComponents, $withComponentType);
        }
        $tree = [];
        foreach ($topLevelComponents as $component) {
            $tree[] = $this->buildComponentNode($component, $withComponentType);
        }

        return $tree;
    }

    /**
     * Recursively eager load children relations to avoid N+1 queries.
     *
     * @param EloquentCollection $components
     * @param bool $withComponentType
     */
    private function eagerLoadRecursively(EloquentCollection $components, bool $withComponentType): void
    {
        $relations = ['children'];
        if ($withComponentType) {
            $relations[] = 'children.componentType';
        }
        // Eager load current level relations
        $components->load($relations);

        // Gather next-level children
        $children = $components->pluck('children')->flatten();
        if ($children->isEmpty()) {
            return;
        }

        // Ensure Eloquent Collection for further loading
        $childrenCollection = new EloquentCollection($children->all());
        $this->eagerLoadRecursively($childrenCollection, $withComponentType);
    }

    /**
     * Build a component node with its children.
     *
     * @param Component $component
     * @param bool $withComponentType
     * @return array
     */
    private function buildComponentNode(Component $component, bool $withComponentType)
    {
        $node = [
            'id' => $component->id,
            'name' => $component->name,
            'component_type_id' => $component->component_type_id,
            'level' => $component->level,
            'component_level_id' => $component->component_level_id,
            'parent_id' => $component->parent_id,
            'notes' => $component->notes,
        ];

        if ($withComponentType) {
            $node['component_type'] = isset($component->componentType) ? $component->componentType->toArray() : null;
        }

        $children = $component->children;
        if ($children->count() > 0) {
            $node['children'] = [];
            foreach ($children as $child) {
                $node['children'][] = $this->buildComponentNode($child, $withComponentType);
            }
        }

        return $node;
    }

    /**
     * Get the full hierarchy of a component using PostgreSQL's recursive query.
     * This is more efficient for deep hierarchies.
     *
     * @param int|null $rootId
     * @return array
     */
    public function getComponentTreeWithRecursiveCTE(?int $rootId = null)
    {
        $query = "
            WITH RECURSIVE component_tree AS (
                SELECT 
                    id, 
                    name, 
                    component_type_id, 
                    notes, 
                    component_level_id, 
                    level, 
                    parent_id,
                    ARRAY[]::bigint[] AS path,
                    0 AS depth
                FROM components
                WHERE " . ($rootId ? "id = $rootId" : "parent_id IS NULL") . "
                
                UNION ALL
                
                SELECT 
                    c.id, 
                    c.name, 
                    c.component_type_id, 
                    c.notes, 
                    c.component_level_id, 
                    c.level, 
                    c.parent_id,
                    ct.path || c.parent_id,
                    ct.depth + 1
                FROM components c
                JOIN component_tree ct ON c.parent_id = ct.id
            )
            SELECT * FROM component_tree
            ORDER BY path, depth;
        ";

        $results = DB::select($query);

        // Transform flat results into a hierarchical structure
        return $this->buildTreeFromFlatRecords($results);
    }

    /**
     * Builds a hierarchical tree from flat records returned by a recursive CTE.
     *
     * @param array $records
     * @return array
     */
    private function buildTreeFromFlatRecords(array $records)
    {
        $tree = [];
        $nodes = [];

        // First pass: create all nodes
        foreach ($records as $record) {
            $node = [
                'id' => $record->id,
                'name' => $record->name,
                'component_type_id' => $record->component_type_id,
                'notes' => $record->notes,
                'component_level_id' => $record->component_level_id,
                'parent_id' => $record->parent_id,
                'level' => $record->level,
                'children' => [],
            ];

            $nodes[$record->id] = $node;
        }

        // Second pass: build the tree
        foreach ($records as $record) {
            if ($record->parent_id === null) {
                // Root node
                $tree[] = &$nodes[$record->id];
            } else {
                // Child node
                $nodes[$record->parent_id]['children'][] = &$nodes[$record->id];
            }
        }

        return $tree;
    }
}
