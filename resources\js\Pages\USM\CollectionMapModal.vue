<script setup>
import Checkbox from '@/Components/Checkbox.vue';
import FlashAlert from '@/Components/FlashAlert.vue';
import InputError from '@/Components/InputError.vue';
import Modal from '@/Components/Modal.vue';
import Select from '@/Components/Select.vue';
import TextInput from '@/Components/TextInput.vue';
import { ScenarioMode } from '@/enums';
import { useForm } from '@inertiajs/vue3';
import axios from 'axios';
import { onMounted, ref, computed, watch } from 'vue';
import Multiselect from 'vue-multiselect';

const collectionMapModal = ref(null);

const props = defineProps({
    usm_id: {
        type: Number,
        required: true,
    },
    usm_module_id: {
        type: Number,
        required: true,
    },
    scenario_mode: {
        type: String,
        default: 'NonScenarioAttributes',
    },
    // scenario_id: {
    //     type: Number,
    //     default: null,
    // },
});

const scenario_name = ref(null);
const component_options = ref([]);
const collections = ref([]);
const scenario_options = ref([]);

// Add computed property to get value sets for selected collection
const getValueSetsForCollection = collectionId => {
    if (!collectionId) return [];

    const selectedCollection = collections.value.find(c => c.id === parseInt(collectionId));
    if (!selectedCollection || !selectedCollection.value_sets) return [];

    return selectedCollection.value_sets.map(vs => ({
        id: vs.id,
        label: `${vs.name}`,
    }));
};

const form = useForm({
    usm_collection_map_id: null,
    name: '',
    scenario_mode: props.scenario_mode,
    //usm_scenario_id: props.scenario_id,
    usm_module_id: props.usm_module_id,
    usm_id: props.usm_id,
    linked: true,
    conditions: [],
});

// Emit event to parent when save completes
const emit = defineEmits(['saveSuccess']);

onMounted(() => {
    const modalElement = document.getElementById('collectionMapModal' + props.usm_module_id);
    modalElement.addEventListener('show.bs.modal', () => {
        //Deselect usm_scenario_id when scenario_mode is NonScenarioAttributes
        //Need to re-assign, form dont update
        // if (props.scenario_mode === 'NonScenarioAttributes') {
        //     form.usm_scenario_id = null;
        // } else {
        //     form.usm_scenario_id = props.scenario_id;
        // }
        form.scenario_mode = props.scenario_mode;

        axios.get(route('usm_collection_map.edit', { usm_id: props.usm_id, usm_module_id: props.usm_module_id, scenario_mode: props.scenario_mode })).then(response => {
            form.usm_collection_map_id = response.data.usm_collection_map_id;
            form.name = response.data.name;
            form.conditions = response.data.conditions;
            scenario_name.value = response.data.scenario_name;
            component_options.value = response.data.component_options;
            collections.value = response.data.collections;
            scenario_options.value = response.data.scenario_options;
            form.linked = response.data.linked;

            // Initialize all rows to be visible by default
            rowVisibility.value = response.data.conditions.map(() => true);

            // Auto-select all value sets for existing assignments
            // form.conditions.forEach(condition => {
            //     condition.assignments.forEach(assignment => {
            //         if (assignment.collection_id) {
            //             autoSelectValueSets(assignment, assignment.collection_id);
            //         }
            //     });
            // });
        });
    });
});

const save = () => {
    form.post(route('usm_collection_map.update'), {
        onSuccess: () => {
            closeModal();
            emit('saveSuccess');
        },
    });
};

const closeModal = () => {
    form.clearErrors();
    resetInput();
    collectionMapModal.value.close();
};

const resetInput = () => {
    form.reset();
};

const rowVisibility = ref([]);

const toggleRow = index => {
    rowVisibility.value[index] = !rowVisibility.value[index];
};

const addRow = () => {
    form.conditions.push({
        id: null,
        comment: '',
        expression: '',
        sequence: form.conditions.length + 1,
        assignments: [],
        scenario_assignments: [],
    });
    rowVisibility.value.push(true);
};

const removeRow = index => {
    form.conditions.splice(index, 1);
    rowVisibility.value.splice(index, 1);

    // Recalculate sequence for remaining conditions
    form.conditions.forEach((condition, idx) => {
        condition.sequence = idx + 1;
    });
};

const addAssignment = index => {
    const newSequence = form.conditions[index].assignments.length + 1;

    const newAssignment = {
        id: null,
        collection_id: null,
        value_set_ids: [],
        component_ids: [],
        sequence: newSequence,
        scenario_ids: [],
    };

    form.conditions[index].assignments.push(newAssignment);

    // Auto-select the first collection if available
    if (collections.value.length > 0) {
        newAssignment.collection_id = collections.value[0].id;
        autoSelectValueSets(newAssignment, newAssignment.collection_id);
    }
};

const removeAssignment = (index, assignmentIndex) => {
    form.conditions[index].assignments.splice(assignmentIndex, 1);

    // Recalculate sequence for remaining assignments
    form.conditions[index].assignments.forEach((assignment, idx) => {
        assignment.sequence = idx + 1;
    });
};

const addScenarioAssignment = index => {
    const newSequence = form.conditions[index].scenario_assignments.length + 1;

    const newAssignment = {
        id: null,
        collection_id: null,
        component_ids: [],
        sequence: newSequence,
        value_sets: [],
    };

    form.conditions[index].scenario_assignments.push(newAssignment);

    // Auto-select the first collection if available
    if (collections.value.length > 0) {
        newAssignment.collection_id = collections.value[0].id;

        // Initialize value_sets array based on available value sets
        const valueSets = getValueSetsForCollection(newAssignment.collection_id);
        if (valueSets.length > 0) {
            newAssignment.value_sets = valueSets.map(vs => ({
                value_set_id: vs.id,
                scenario_ids: [],
            }));
        }
    }
};

const removeScenarioAssignment = (index, assignmentIndex) => {
    form.conditions[index].scenario_assignments.splice(assignmentIndex, 1);

    // Recalculate sequence for remaining assignments
    form.conditions[index].scenario_assignments.forEach((assignment, idx) => {
        assignment.sequence = idx + 1;
    });
};

const updateValueSetsForScenarioAssignment = (assignment, collectionId) => {
    if (!collectionId) {
        assignment.value_sets = [];
        return;
    }

    const valueSets = getValueSetsForCollection(collectionId);

    // Create new array of value_sets based on the collection's value sets
    assignment.value_sets = valueSets.map(vs => {
        // Try to find existing value set with same ID to preserve scenario_ids
        const existingValueSet = assignment.value_sets.find(evs => evs.value_set_id === vs.id);
        return {
            value_set_id: vs.id,
            scenario_ids: existingValueSet ? existingValueSet.scenario_ids : [],
        };
    });
};

// Auto-select all value sets when a collection is selected
const autoSelectValueSets = (assignment, collectionId) => {
    if (!collectionId) {
        assignment.value_set_ids = [];
        return;
    }

    const valueSets = getValueSetsForCollection(collectionId);
    if (valueSets.length > 0) {
        assignment.value_set_ids = valueSets.map(vs => vs.id);
    }
};

// Get available scenario options for a specific value set in an assignment
const getAvailableScenarioOptions = (assignment, currentValueSetIndex) => {
    //Currently return all options, below code is only return those haven't selected.
    return scenario_options.value.map(g => g.id);

    // If no assignment or value_sets, return all options
    if (!assignment || !assignment.value_sets || !assignment.value_sets.length) {
        return scenario_options.value.map(g => g.id);
    }

    // Get all scenario IDs already selected in other value sets
    const selectedScenarioIds = assignment.value_sets
        .filter((_, index) => index !== currentValueSetIndex) // Exclude current value set
        .flatMap(vs => vs.scenario_ids || []); // Get all scenario IDs

    // Return only scenario IDs that are not already selected in other value sets
    return scenario_options.value.map(g => g.id).filter(id => !selectedScenarioIds.includes(id));
};

// Handle scenario selection changes to ensure reactive updates
const handleScenarioSelection = (assignment, valueSetIndex) => {
    // Force reactivity update in all value sets
    assignment.value_sets.forEach((vs, idx) => {
        if (idx !== valueSetIndex) {
            // Create a new array with the same values to trigger reactivity
            vs.scenario_ids = [...vs.scenario_ids];
        }
    });
};

//Computed
const buttonYes = computed(() => {
    return props.editDisabled ? null : 'Save';
});

const scenarioLabel = computed(() => {
    return props.scenario_mode === 'NonScenarioAttributes' ? 'Non-Scenario' : 'Scenario';
});
</script>

<template>
    <Modal ref="collectionMapModal" @yesEvent="save" @noEvent="closeModal" :id="'collectionMapModal' + props.usm_module_id" :title="'Collection Map'" :buttonYes="buttonYes" :buttonType="'primary'" :modalClass="'modal-xl'" :form="form">
        <FlashAlert v-if="Object.keys(form.errors).length && !form.errors.file" :status="'danger'" @close="form.clearErrors()">
            <label v-for="(message, field) in form.errors" :key="field">
                {{ field }}: {{ message }}
                {{ message }}
            </label>
        </FlashAlert>

        <div class="mt-6">
            <div class="row gap-3 mb-3">
                <div class="col-md-3"><b>Mode:</b> {{ scenarioLabel }}</div>
                <!-- <div v-if="scenarioLabel === 'Scenario'" class="col-md-3"><b>Scenario:</b> {{ scenario_name }}</div> -->
            </div>

            <div class="d-flex align-items-center mb-3">
                <div class="me-3 d-flex align-items-center">
                    <span class="me-2">Map:</span>
                    <TextInput type="text" v-model="form.name" />
                </div>
                <div class="me-3 d-flex align-items-center">
                    <Checkbox id="checkLinked" v-model:checked="form.linked"> Linked </Checkbox>
                </div>
            </div>

            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th width="3%" class="text-center">
                            <button type="button" class="btn btn-sm btn-primary" @click="addRow"><i class="bi bi-plus"></i></button>
                        </th>
                        <th width="3%">No</th>
                        <th>Comment</th>
                        <th>Condition</th>
                    </tr>
                </thead>
                <tbody>
                    <template v-for="(row, index) in form.conditions" :key="index">
                        <tr>
                            <td class="text-center align-middle">
                                <button type="button" class="btn btn-sm btn-danger m-1" @click="removeRow(index)" title="Remove Row"><i class="bi bi-dash"></i></button>
                                <button type="button" class="btn btn-sm btn-danger m-1" @click="toggleRow(index)" title="Hide/Show Row"><i :class="['bi', rowVisibility[index] ? 'bi-eye-fill' : 'bi-eye']"></i></button>
                            </td>
                            <td class="text-center align-middle">{{ index + 1 }}</td>
                            <td class="text-center align-middle">
                                <TextInput type="hidden" v-model="row.id" />
                                <TextInput type="text" v-model="row.comment" />
                            </td>
                            <td class="align-middle">
                                <TextInput type="text" v-model="row.expression" :invalid="form.errors[`conditions.${index}.expression`]" />
                                <InputError :message="form.errors[`conditions.${index}.expression`]" />
                            </td>
                        </tr>
                        <tr v-show="rowVisibility[index]">
                            <td colspan="7" class="p-0">
                                <div class="bg-light p-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2"></div>
                                    <template v-if="props.scenario_mode === ScenarioMode.NON_SCENARIO">
                                        <table class="table table-sm table-bordered mb-0">
                                            <thead class="table-secondary">
                                                <tr>
                                                    <th width="3%">
                                                        <button type="button" class="btn btn-sm btn-primary" @click="addAssignment(index)"><i class="bi bi-plus"></i></button>
                                                    </th>
                                                    <th>Collection</th>
                                                    <th width="40%">Components</th>
                                                    <th width="40%">Value Sets</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr v-for="(assignment, assignmentIndex) in row.assignments" :key="assignmentIndex">
                                                    <td class="text-center">
                                                        <button type="button" class="btn btn-sm btn-danger" @click="removeAssignment(index, assignmentIndex)">
                                                            <i class="bi bi-dash"></i>
                                                        </button>
                                                        <input type="hidden" v-model="assignment.id" />
                                                    </td>
                                                    <td><Select v-model="assignment.collection_id" :options="collections" :placeholder="'Select Collection'" @update:modelValue="newVal => autoSelectValueSets(assignment, newVal)" /></td>
                                                    <td>
                                                        <Multiselect
                                                            v-model="assignment.component_ids"
                                                            :options="component_options.map(g => g.id)"
                                                            :custom-label="
                                                                id => {
                                                                    const option = component_options.find(g => g.id === id);
                                                                    return option ? option.label : '';
                                                                }
                                                            "
                                                            :searchable="true"
                                                            :close-on-select="false"
                                                            :show-labels="false"
                                                            :multiple="true"
                                                            :placeholder="'Select Components'"
                                                        />
                                                    </td>
                                                    <td>
                                                        <Multiselect
                                                            v-model="assignment.value_set_ids"
                                                            :options="getValueSetsForCollection(assignment.collection_id).map(vs => vs.id)"
                                                            :custom-label="
                                                                id => {
                                                                    const valueSet = getValueSetsForCollection(assignment.collection_id).find(vs => vs.id === id);
                                                                    return valueSet ? valueSet.label : '';
                                                                }
                                                            "
                                                            :searchable="true"
                                                            :close-on-select="false"
                                                            :show-labels="false"
                                                            :multiple="true"
                                                            :placeholder="'Select Value Sets'"
                                                        />
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </template>
                                    <template v-else>
                                        <table class="table table-sm table-bordered mb-0">
                                            <thead class="table-secondary">
                                                <tr>
                                                    <th width="3%">
                                                        <button type="button" class="btn btn-sm btn-primary" @click="addScenarioAssignment(index)"><i class="bi bi-plus"></i></button>
                                                    </th>
                                                    <th width="20%">Collection</th>
                                                    <th>Components</th>
                                                    <th width="20%">Value Sets</th>
                                                    <th>Scenarios</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <template v-for="(assignment, assignmentIndex) in row.scenario_assignments" :key="assignmentIndex">
                                                    <tr>
                                                        <td class="text-center" :rowspan="assignment.value_sets.length || 1">
                                                            <button type="button" class="btn btn-sm btn-danger" @click="removeScenarioAssignment(index, assignmentIndex)">
                                                                <i class="bi bi-dash"></i>
                                                            </button>
                                                            <input type="hidden" v-model="assignment.id" />
                                                        </td>
                                                        <td :rowspan="assignment.value_sets.length || 1"><Select v-model="assignment.collection_id" :options="collections" :placeholder="'Select Collection'" @update:modelValue="newVal => updateValueSetsForScenarioAssignment(assignment, newVal)" /></td>
                                                        <td :rowspan="assignment.value_sets.length || 1">
                                                            <Multiselect
                                                                v-model="assignment.component_ids"
                                                                :options="component_options.map(g => g.id)"
                                                                :custom-label="
                                                                    id => {
                                                                        const option = component_options.find(g => g.id === id);
                                                                        return option ? option.label : '';
                                                                    }
                                                                "
                                                                :searchable="true"
                                                                :close-on-select="false"
                                                                :show-labels="false"
                                                                :multiple="true"
                                                                :placeholder="'Select Components'"
                                                            />
                                                        </td>
                                                        <!-- First value set is rendered in the same row as collection and components -->
                                                        <template v-if="assignment.value_sets.length > 0">
                                                            <td>
                                                                {{ getValueSetsForCollection(assignment.collection_id).find(vs => vs.id === assignment.value_sets[0].value_set_id)?.label || '' }}
                                                            </td>
                                                            <td>
                                                                <Multiselect
                                                                    v-model="assignment.value_sets[0].scenario_ids"
                                                                    :options="getAvailableScenarioOptions(assignment, 0)"
                                                                    :custom-label="
                                                                        id => {
                                                                            const option = scenario_options.find(g => g.id === id);
                                                                            return option ? option.label : '';
                                                                        }
                                                                    "
                                                                    :searchable="true"
                                                                    :close-on-select="false"
                                                                    :show-labels="false"
                                                                    :multiple="true"
                                                                    :placeholder="'Select Scenario'"
                                                                    @input="() => handleScenarioSelection(assignment, 0)"
                                                                />
                                                            </td>
                                                        </template>
                                                        <template v-else>
                                                            <td colspan="2">
                                                                <div class="text-muted fst-italic">No value sets available for this collection</div>
                                                            </td>
                                                        </template>
                                                    </tr>
                                                    <!-- Additional rows for remaining value sets -->

                                                    <tr v-if="assignment?.value_sets?.length > 0" v-for="(valueSet, valueSetIndex) in assignment.value_sets.slice(1)" :key="`${assignmentIndex}-${valueSetIndex}`">
                                                        <td>
                                                            {{ getValueSetsForCollection(assignment.collection_id).find(vs => vs.id === valueSet.value_set_id)?.label || '' }}
                                                        </td>
                                                        <td>
                                                            <Multiselect
                                                                v-model="valueSet.scenario_ids"
                                                                :options="getAvailableScenarioOptions(assignment, valueSetIndex + 1)"
                                                                :custom-label="
                                                                    id => {
                                                                        const option = scenario_options.find(g => g.id === id);
                                                                        return option ? option.label : '';
                                                                    }
                                                                "
                                                                :searchable="true"
                                                                :close-on-select="false"
                                                                :show-labels="false"
                                                                :multiple="true"
                                                                :placeholder="'Select Scenario'"
                                                                @input="() => handleScenarioSelection(assignment, valueSetIndex + 1)"
                                                            />
                                                        </td>
                                                    </tr>
                                                </template>
                                            </tbody>
                                        </table>
                                    </template>
                                </div>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </Modal>
</template>
