<?php

namespace App\Services;

use App\Models\USM;
use App\Services\LineDatabase;

class GenerateLineItemDataXmlService
{
    protected $fuse_config_id;
    protected $usm_id;
    protected $fuseConfig;
    protected $lineDatabase;
    protected $lines;
    protected $columnsWithDataType;

    public function __construct(int $usm_id)
    {
        $this->usm_id = $usm_id;
        $this->usm = USM::with(['binMatrix', 'fuseConfigs'])->find($usm_id);

        $this->lineDatabase = new LineDatabase($this->usm->binMatrix->speed_batch_id);
        $this->lines = $this->lineDatabase->getModel()->get()->toArray();
        $this->columnsWithDataType = $this->lineDatabase->getColumnsWithDataType();
    }

    public function generateXML($fuseConfig)
    {
        // Create a new XML document
        $dom = new \DOMDocument('1.0', 'utf-8');
        $dom->formatOutput = true;

        // Create the root element <Suite>
        $rootElement = $dom->createElement('LineItemData');
        $rootElement->setAttribute('xmlns:xsi', 'http://www.w3.org/2001/XMLSchema-instance');
        $rootElement->setAttribute('xsi:noNamespaceSchemaLocation', 'GEN_Fuse_LineItemData.xsd');
        $dom->appendChild($rootElement);

        // Create Info element
        $infoElement = $dom->createElement('Info');
        $fuseFileReleaseElement = $dom->createElement('FuseFileRelease');
        $fuseFileReleaseElement->setAttribute('name', $fuseConfig->name);
        $fuseFileReleaseElement->setAttribute('revision', $fuseConfig->revision);
        $fuseFileReleaseElement->setAttribute('state', $fuseConfig->state);
        $infoElement->appendChild($fuseFileReleaseElement);

        $associatedBinMatricesElement = $dom->createElement('AssociatedBinMatrices');
        $binMatrixElement = $dom->createElement('BinMatrix');
        $binMatrixElement->setAttribute('name', $this->usm->binMatrix->name);
        $binMatrixElement->setAttribute('revision', $this->usm->binMatrix->revision);
        $binMatrixElement->setAttribute('state', $this->usm->binMatrix->state);
        $associatedBinMatricesElement->appendChild($binMatrixElement);
        $infoElement->appendChild($associatedBinMatricesElement);

        $fileCreatedByElement = $dom->createElement('FileCreatedBy');
        $fileCreatedByElement->appendChild($dom->createTextNode($this->usm->createdUser->name));
        $infoElement->appendChild($fileCreatedByElement);

        $fileCreatedDateTimeElement = $dom->createElement('FileCreatedDateTime');
        $fileCreatedDateTimeElement->appendChild($dom->createTextNode($this->usm->created_at->format('Y-m-d\TH:i:s\Z')));
        $infoElement->appendChild($fileCreatedDateTimeElement);

        $fileFormatElement = $dom->createElement('FileFormat');
        $fileFormatElement->appendChild($dom->createTextNode('1'));
        $infoElement->appendChild($fileFormatElement);
        $rootElement->appendChild($infoElement);

        $this->buildLineItemValues($rootElement, $dom);

        return $dom->saveXML();
    }

    private function buildLineItemValues($rootElement, $dom)
    {
        $lineItemValuesElement = $dom->createElement('LineItemValues');
        $rootElement->appendChild($lineItemValuesElement);

        foreach ($this->lines as $line) {
            $lineItemElement = $dom->createElement('LineItem');
            $lineItemElement->setAttribute('qdfSspec', $line['QDF/SSPEC']);
            $lineItemElement->setAttribute('liid', "F" . $line['MM#']);
            $lineItemValuesElement->appendChild($lineItemElement);

            foreach ($line as $column => $value) {
                $attributeElement = $dom->createElement('Attribute');
                $attributeElement->setAttribute('name', $column);
                $attributeElement->setAttribute('dataType', $this->convertColumnDataType($column));
                $attributeElement->setAttribute('unit', "");
                $attributeElement->setAttribute('multiplier', "");
                $attributeElement->appendChild($dom->createTextNode($value));
                $lineItemElement->appendChild($attributeElement);
            }
        }
    }

    private function convertColumnDataType($column)
    {
        if (isset($this->columnsWithDataType[$column])) {
            return convertDataType($this->columnsWithDataType[$column]);
        }
        return "String";
    }


    public function saveXMLToFile(): array
    {
        $paths = [];
        foreach ($this->usm->fuseConfigs as $fuseConfig) {
            $xml = $this->generateXML($fuseConfig);

            $fileName = 'lineitemdata_usm_' . $fuseConfig->id . '_' . date('Ymd_His') . '.xml';
            $path = storage_path('app/exports/usm/' . $this->usm_id . '/' . $fileName);

            // Create directory if it doesn't exist
            if (!file_exists(dirname($path))) {
                mkdir(dirname($path), 0755, true);
            }

            file_put_contents($path, $xml);
            $paths[] = $path;
        }

        return $paths;
    }
}
