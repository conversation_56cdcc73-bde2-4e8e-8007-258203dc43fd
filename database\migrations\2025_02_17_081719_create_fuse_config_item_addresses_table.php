<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fuse_config_item_addresses', function (Blueprint $table) {
            $table->id();
            $table->string('setting');
            $table->integer('stop_address');
            $table->integer('start_address');
            $table->integer('size');
            $table->string('fuse_type')->nullable();
            $table->string('encoding_type')->nullable();
            $table->string('data_map_type')->nullable(); //Use Recommended Value, Conditional
            $table->string('xml_default')->nullable();
            $table->string('fuse_group')->nullable();
            $table->string('fuselink_group')->nullable();
            $table->string('fuse_class')->nullable();
            $table->string('de_rv_xd')->nullable(); //DE/RV/XD (Original Column name), normal value is m/s/0/[dynamic]
            $table->text('expression')->nullable();
            $table->text('comment')->nullable();
            $table->unsignedBigInteger('fuse_config_item_id')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fuse_config_item_addresses');
    }
};
