<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class ComponentType extends BaseModel
{
    use SoftDeletes;

    protected $fillable = [
        'name',
        'active',
        'level',
        'type',
        'source',
        'scenario_enabled',
        'sub_elements',
        'create_user_id',
    ];

    //Default attributes
    protected $attributes = [
        'active' => true,
    ];

    protected $casts = [
        'active' => 'boolean',
        'scenario_enabled' => 'boolean',
    ];

    //Relationships
    public function components(): HasMany
    {
        return $this->hasMany(Component::class, 'component_type_id');
    }

    public function usmModuleComponentAttributes(): HasMany
    {
        return $this->hasMany(USMModuleComponentAttribute::class, 'component_type_id');
    }

    //Attributes
    public function active(): Attribute
    {
        return Attribute::make(
            get: fn(string $value) => $value ? true : false
        );
    }

    public function xmlSubElements(): Attribute
    {
        return Attribute::make(
            get: function ($value) {
                $subElements = json_decode($this->sub_elements, true);

                try {
                    // Create a new XML document
                    $xml = new \SimpleXMLElement('<UpsType></UpsType>');

                    // Add attributes to the root element
                    $xml->addAttribute('type', $this->type ?? '');
                    $xml->addAttribute('level', $this->level ?? '');
                    $xml->addAttribute('config_name', $this->name ?? '');
                    $xml->addAttribute('scenarioEnabled', $this->scenario_enabled ? 'true' : 'false');

                    if (!empty($subElements)) {

                        // Check if sub_elements is an array of objects with 'name' and 'attributes'
                        if (is_array($subElements) && isset($subElements[0]) && isset($subElements[0]['name'])) {
                            // This is an array of named elements
                            foreach ($subElements as $elementData) {
                                if (isset($elementData['name']) && !empty($elementData['name'])) {
                                    $elementName = $this->sanitizeXmlTag($elementData['name']);
                                    $element = $xml->addChild($elementName);

                                    // Add attributes to the element
                                    if (isset($elementData['attributes']) && is_array($elementData['attributes'])) {
                                        foreach ($elementData['attributes'] as $attrName => $attrValue) {
                                            // Skip adding nested elements as attributes
                                            if ($attrName === 'elements' && is_array($attrValue)) {
                                                // Process nested elements
                                                foreach ($attrValue as $nestedData) {
                                                    if (isset($nestedData['name']) && !empty($nestedData['name'])) {
                                                        $nestedName = $this->sanitizeXmlTag($nestedData['name']);
                                                        $nestedElement = $element->addChild($nestedName);

                                                        // Add attributes to nested element
                                                        if (isset($nestedData['attributes']) && is_array($nestedData['attributes'])) {
                                                            foreach ($nestedData['attributes'] as $nestedAttrName => $nestedAttrValue) {
                                                                $nestedAttrName = $this->sanitizeXmlTag($nestedAttrName);
                                                                // Convert array values to JSON strings
                                                                if (is_array($nestedAttrValue)) {
                                                                    $nestedAttrValue = json_encode($nestedAttrValue);
                                                                }
                                                                $nestedElement->addAttribute($nestedAttrName, (string)$nestedAttrValue);
                                                            }
                                                        }
                                                    }
                                                }
                                                continue; // Skip adding this as an attribute
                                            }

                                            $attrName = $this->sanitizeXmlTag($attrName);
                                            // Convert array values to JSON strings
                                            if (is_array($attrValue)) {
                                                $attrValue = json_encode($attrValue);
                                            }
                                            $element->addAttribute($attrName, (string)$attrValue);
                                        }
                                    }
                                }
                            }
                        } else {
                            // Handle as associative array (original implementation)
                            foreach ($subElements as $elementName => $elementData) {
                                // Make sure element name is valid XML
                                $elementName = $this->sanitizeXmlTag($elementName);
                                $element = $xml->addChild($elementName);

                                // Add attributes to the element
                                if (isset($elementData['attributes']) && is_array($elementData['attributes'])) {
                                    foreach ($elementData['attributes'] as $attrName => $attrValue) {
                                        $attrName = $this->sanitizeXmlTag($attrName);
                                        // Convert array values to JSON strings
                                        if (is_array($attrValue)) {
                                            $attrValue = json_encode($attrValue);
                                        }
                                        $element->addAttribute($attrName, (string)$attrValue);
                                    }
                                }

                                // Add nested elements if they exist
                                if (isset($elementData['nested_elements']) && is_array($elementData['nested_elements'])) {
                                    foreach ($elementData['nested_elements'] as $nestedData) {
                                        if (isset($nestedData['name']) && !empty($nestedData['name'])) {
                                            $nestedName = $this->sanitizeXmlTag($nestedData['name']);
                                            $nestedElement = $element->addChild($nestedName);

                                            // Add attributes to nested element
                                            if (isset($nestedData['attributes']) && is_array($nestedData['attributes'])) {
                                                foreach ($nestedData['attributes'] as $nestedAttrName => $nestedAttrValue) {
                                                    $nestedAttrName = $this->sanitizeXmlTag($nestedAttrName);
                                                    // Convert array values to JSON strings
                                                    if (is_array($nestedAttrValue)) {
                                                        $nestedAttrValue = json_encode($nestedAttrValue);
                                                    }
                                                    $nestedElement->addAttribute($nestedAttrName, (string)$nestedAttrValue);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // Get the XML string directly without using DOMDocument
                    $xmlString = $xml->asXML();

                    // Format the XML using regex for simple pretty printing
                    $xmlString = $this->formatXml($xmlString);

                    return $xmlString;
                } catch (\Exception $e) {
                    // If there's an error, return a fallback message
                    return "Error generating XML: " . $e->getMessage();
                }
            }
        );
    }

    /**
     * Sanitize a string to be a valid XML tag name
     * 
     * @param string $string The string to sanitize
     * @return string A valid XML tag name
     */
    private function sanitizeXmlTag($string)
    {
        // Replace invalid characters with underscores
        $string = preg_replace('/[^a-zA-Z0-9_\-]/', '_', $string);

        // XML names must start with a letter or underscore
        if (preg_match('/^[0-9\-]/', $string)) {
            $string = '_' . $string;
        }

        return $string;
    }

    /**
     * Format XML string with indentation
     * 
     * @param string $xml The XML string to format
     * @return string Formatted XML string
     */
    private function formatXml($xml)
    {
        try {
            $dom = new \DOMDocument('1.0');
            $dom->preserveWhiteSpace = false;
            $dom->formatOutput = true;
            // Use loadXML with LIBXML_NOERROR to avoid throwing errors
            if ($dom->loadXML($xml, LIBXML_NOERROR)) {
                return $dom->saveXML();
            }

            // If loadXML fails, try a regex-based approach for simple formatting
            $xml = preg_replace('/<\?xml.*?\?>/', '', $xml); // Remove XML declaration
            $xml = preg_replace('/>\s*</', ">\n<", $xml); // Add newlines between tags
            return $xml;
        } catch (\Exception $e) {
            // Return original XML if all formatting methods fail
            return $xml;
        }
    }

    //Static Functions Below Here

    /*
    * Build Table Header
    */
    public static function header()
    {
        $headers = [];
        return array_merge($headers, [
            ['field' => 'name', 'title' => 'Name', 'sortable' => true],
            ['field' => 'level', 'title' => 'Level', 'sortable' => true],
            ['field' => 'type', 'title' => 'Type', 'sortable' => true],
            ['field' => 'created_at', 'title' => 'Created At', 'sortable' => true],
        ]);
    }

    public static function sourceTypeOptions()
    {
        return [
            COMPLEX_SOURCE_TYPE_COMPONENT => strtoupper(COMPLEX_SOURCE_TYPE_COMPONENT),
            COMPLEX_SOURCE_TYPE_GROUP => strtoupper(COMPLEX_SOURCE_TYPE_GROUP)
        ];
    }

    public static function sourceTypes()
    {
        return [
            COMPLEX_SOURCE_TYPE_COMPONENT,
            COMPLEX_SOURCE_TYPE_GROUP,
        ];
    }
}
