<script setup>
import { ref, onMounted, computed } from 'vue';
import axios from 'axios';

const props = defineProps({
    batchId: {
        //nullable, for SpeedBatch table
        type: Number,
        default: null,
    },
    lineitemId: {
        //nullable, for LineItem table
        type: Number,
        default: null,
    },
    tableType: {
        type: String,
        default: 'view', //view, speed, lineitem
    },
    editable: {
        type: Boolean,
        default: true,
    },
});

const tableData = ref([]);
const selectedCount = ref(0);
const columnDetails = ref([]);
const control = ref({ isUpdateable: false, isDeleteable: false, isAddable: false });
const excelEditorRef = ref(null);

const fetchTableData = async () => {
    const response = await axios.get(route('dynamic.index', { batch_id: props.batchId, lineitem_id: props.lineitemId, tableType: props.tableType }));
    tableData.value = response.data.data;
    columnDetails.value = response.data.columns;
    if (props.editable) {
        control.value = response.data.control;
    }
};

const onUpdate = async updateItemArray => {
    axios.post(route('dynamic.update', { batch_id: props.batchId, tableType: props.tableType }), {
        updates: updateItemArray,
    });
};

const onDelete = async deleteItemArray => {
    console.log(deleteItemArray);
    axios.post(route('dynamic.delete', { batch_id: props.batchId, tableType: props.tableType }), {
        deletes: deleteItemArray,
    });
};

const deleteSelectedRecords = async () => {
    const deletedRecords = excelEditorRef.value.getSelectedRecords();
    await axios.delete(route('dynamic.destroy', { batch_id: props.batchId, tableType: props.tableType }), {
        data: { deletes: deletedRecords },
    });
    fetchTableData();
};

const newRecord = async () => {
    axios
        .post(route('dynamic.store', { batch_id: props.batchId, tableType: props.tableType }))
        .then(response => {
            excelEditorRef.value.newRecord({
                id: response.data.id,
            });
        })
        .catch(error => {
            alert(error.response.data.message);
        });
};

const exportAsExcel = async () => {
    const format = 'xlsx'
    const exportSelectedOnly = false
    const id = props.batchId || props.lineitemId;

    const filename = `Export_${props.tableType}_${id}.xlsx`;
    excelEditorRef.value.exportTable(format, exportSelectedOnly, filename)
};

onMounted(() => {
    fetchTableData();
});

//Computed UI
const showDeleteButton = computed(() => control.value.isDeleteable);
const disabledDelete = computed(() => selectedCount.value == 0);
const showAddButton = computed(() => control.value.isAddable);
</script>

<template>
    <div class="mb-1 d-flex gap-2">
        <button type="button" class="btn btn-sm btn-secondary" v-show="showDeleteButton" @click="deleteSelectedRecords"
            :disabled="disabledDelete">
            <i class="bi bi-trash"></i>
        </button>

        <button type="button" class="btn btn-sm btn-primary" v-show="showAddButton" @click="newRecord">
            <i class="bi bi-plus"></i>
        </button>

        <button type="button" class="btn btn-sm btn-primary" @click="exportAsExcel">
            <i class="bi bi-download"></i>
        </button>
    </div>
    <vue-excel-editor ref="excelEditorRef" v-model="tableData" filter-row @update="onUpdate" @delete="onDelete"
        no-header-edit v-model:selected-count="selectedCount" free-select>
        <!-- Key field column -->
        <vue-excel-column v-if="tableType != 'lineitem'" field="id" label="id" type="string" width="100px" key-field
            readonly />
        <!-- Dynamic columns -->
        <vue-excel-column v-for="(column, index) in columnDetails" :key="index" :field="column.field"
            :label="column.label" :type="column.type" :readonly="!control.isUpdateable" width="150px" />
    </vue-excel-editor>
</template>
